#!/usr/bin/env node

/**
 * Test Real S3 Upload via Backend
 * This script tests actual video upload to S3 through the backend
 */

const { execSync } = require('child_process');
const fs = require('fs');

console.log('🎯 === TESTING REAL S3 UPLOAD ===\n');

// Test 1: Verify backend S3 connectivity
console.log('1️⃣ Verifying Backend S3 Connectivity...');
try {
  const s3Test = execSync('curl -s http://localhost:5000/api/test-s3', { encoding: 'utf8' });
  const s3Result = JSON.parse(s3Test);
  
  if (s3Result.success) {
    console.log('✅ Backend S3 connection verified');
    console.log(`   Bucket: ${s3Result.bucket}`);
    console.log(`   Region: ${s3Result.region}`);
    console.log(`   Existing files: ${s3Result.objectCount}`);
    console.log(`   Sample files: ${s3Result.sampleFiles?.length || 0}`);
  } else {
    console.log('❌ Backend S3 connection failed:', s3Result.error);
    process.exit(1);
  }
} catch (error) {
  console.log('❌ Backend S3 test failed:', error.message);
  process.exit(1);
}

// Test 2: Create a test video file
console.log('\n2️⃣ Creating Test Video File...');
try {
  // Create a small test video file (simulated)
  const testVideoContent = Buffer.from('WEBM test video content for S3 upload test');
  fs.writeFileSync('test-video.webm', testVideoContent);
  console.log('✅ Test video file created (test-video.webm)');
} catch (error) {
  console.log('❌ Failed to create test video:', error.message);
  process.exit(1);
}

// Test 3: Upload test video via backend
console.log('\n3️⃣ Testing Upload via Backend...');
try {
  const uploadCommand = `curl -s -X POST -F "video=@test-video.webm" -F "phrase=test_upload" -F "category=testing" -F "recordingNumber=1" -F "demographics={\\"userId\\":\\"test\\",\\"ageGroup\\":\\"40to64\\",\\"gender\\":\\"female\\",\\"ethnicity\\":\\"not_specified\\"}" http://localhost:5000/upload`;
  
  console.log('Uploading test video...');
  const uploadResult = execSync(uploadCommand, { encoding: 'utf8' });
  const result = JSON.parse(uploadResult);
  
  if (result.success) {
    console.log('✅ Upload successful!');
    console.log(`   File path: ${result.filePath}`);
    console.log(`   URL: ${result.url}`);
    console.log(`   Backend upload: ${result.backendUpload}`);
  } else {
    console.log('❌ Upload failed:', result.error || result.message);
  }
  
} catch (error) {
  console.log('❌ Upload test failed:', error.message);
}

// Test 4: Verify upload in S3
console.log('\n4️⃣ Verifying Upload in S3...');
try {
  const s3Verify = execSync('curl -s http://localhost:5000/api/test-s3', { encoding: 'utf8' });
  const verifyResult = JSON.parse(s3Verify);
  
  if (verifyResult.success) {
    console.log(`✅ S3 verification complete`);
    console.log(`   Total files now: ${verifyResult.objectCount}`);
    
    // Look for our test file
    const testFile = verifyResult.sampleFiles?.find(file => 
      file.key.includes('test_upload') || file.key.includes('test')
    );
    
    if (testFile) {
      console.log(`✅ Test upload found in S3: ${testFile.key}`);
    } else {
      console.log('⚠️ Test upload not found in sample files (may be in bucket but not in first 3)');
    }
  }
} catch (error) {
  console.log('❌ S3 verification failed:', error.message);
}

// Cleanup
console.log('\n5️⃣ Cleanup...');
try {
  fs.unlinkSync('test-video.webm');
  console.log('✅ Test file cleaned up');
} catch (error) {
  console.log('⚠️ Cleanup warning:', error.message);
}

console.log('\n🎯 === UPLOAD TEST COMPLETE ===');
console.log('✅ Backend S3 connectivity verified');
console.log('✅ Real upload test completed');
console.log('✅ S3 storage confirmed operational');
console.log('\n🚀 Ready for application testing with real S3 uploads!');
