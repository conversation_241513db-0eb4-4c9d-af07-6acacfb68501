/**
 * Metadata Manager utility for ICU Dataset Application
 * Handles generation and updates of metadata.csv in the S3 bucket
 */
const { S3Client, PutObjectCommand, GetObjectCommand, HeadObjectCommand } = require('@aws-sdk/client-s3');
const { Readable } = require('stream');

/**
 * Creates or updates the metadata.csv file in the S3 bucket
 * 
 * @param {Object} metadata - Object containing metadata about the uploaded video
 * @param {string} metadata.phrase_label - The phrase label (sanitized)
 * @param {string} metadata.user_id - User ID
 * @param {string} metadata.agegroup - Age group (18to39, 40to64, 65plus)
 * @param {string} metadata.gender - Gender (male, female, nonbinary)
 * @param {string} metadata.ethnicity - Ethnicity
 * @param {string} metadata.timestamp - Timestamp in format YYYYMMDDTHHMMSS
 * @param {string} filename - The full filename of the uploaded video
 * @param {string} filePath - The S3 key/path where the file was uploaded
 * @returns {Promise<Object>} - Result of the operation
 */
async function updateMetadataCSV(metadata, filename, filePath) {
  try {
    const bucketName = process.env.AWS_S3_BUCKET || 'icudatasetphrasesfortesting';
    const region = process.env.AWS_REGION || 'us-east-1';
    const metadataKey = 'metadata.csv';
    
    // Create S3 client
    const s3Client = new S3Client({ region });
    
    let existingContent = '';
    let csvHeader = 'filename,phrase_label,user_id,agegroup,gender,ethnicity,timestamp\n';
    let fileExists = false;
    
    try {
      // Check if file already exists
      const headParams = {
        Bucket: bucketName,
        Key: metadataKey
      };
      
      await s3Client.send(new HeadObjectCommand(headParams));
      fileExists = true;
      
      // File exists, get its content
      const getParams = {
        Bucket: bucketName,
        Key: metadataKey
      };
      
      const response = await s3Client.send(new GetObjectCommand(getParams));
      
      // Convert response body (stream) to string
      const chunks = [];
      for await (const chunk of response.Body) {
        chunks.push(chunk);
      }
      existingContent = Buffer.concat(chunks).toString('utf-8');
      
      // Check if header exists, if not add it
      if (!existingContent.startsWith('filename,phrase_label')) {
        existingContent = csvHeader + existingContent;
      }
    } catch (error) {
      // File doesn't exist or error reading it, start with empty content
      if (error.name === 'NotFound' || error.name === 'NoSuchKey') {
        console.log('metadata.csv does not exist yet, will create it');
        existingContent = csvHeader;
      } else {
        console.error('Error checking metadata.csv:', error);
        throw error;
      }
    }
    
    // Create new CSV row for this upload
    const newRow = [
      filename,
      metadata.phrase_label,
      metadata.user_id,
      metadata.agegroup,
      metadata.gender,
      metadata.ethnicity,
      metadata.timestamp
    ].join(',');
    
    // Append new row to existing content
    // Ensure we have a newline at the end of existing content if it's not empty
    if (existingContent && !existingContent.endsWith('\n')) {
      existingContent += '\n';
    }
    
    // Add new row
    const updatedContent = existingContent + newRow + '\n';
    
    // Upload updated CSV to S3
    const uploadParams = {
      Bucket: bucketName,
      Key: metadataKey,
      Body: updatedContent,
      ContentType: 'text/csv'
    };
    
    await s3Client.send(new PutObjectCommand(uploadParams));
    
    console.log(`Successfully updated ${metadataKey} in S3`);
    
    return {
      success: true,
      message: `Updated ${metadataKey}`,
      isNew: !fileExists
    };
    
  } catch (error) {
    console.error('Error updating metadata CSV:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

module.exports = { updateMetadataCSV };
