/**
 * SageMaker Manager utility for ICU Dataset Application
 * Handles interaction with AWS SageMaker for model evaluation metrics by demographic
 */
const { S3Client, PutObjectCommand } = require('@aws-sdk/client-s3');
const { SageMakerRuntimeClient, InvokeEndpointCommand } = require('@aws-sdk/client-sagemaker-runtime');

/**
 * Log model evaluation metrics by demographic category
 * 
 * @param {Object} metrics - The evaluation metrics to log
 * @param {Object} demographics - The demographics data for categorization
 * @returns {Promise<Object>} - Result of the operation
 */
async function logMetricsByDemographic(metrics, demographics) {
  try {
    // Get bucket name from environment variable
    const bucketName = process.env.AWS_S3_BUCKET || 'icudatasetphrasesfortesting';
    const region = process.env.AWS_REGION || 'us-east-1';
    
    // Format timestamp in required format: YYYYMMDDTHHMMSS
    const now = new Date();
    const timestamp = now.toISOString()
      .replace(/[-:]/g, '')
      .replace(/\..+/, '');
    
    // Create metrics data JSON
    const metricsData = {
      timestamp,
      demographics,
      metrics,
    };
    
    // Create filename with demographic metadata
    const filename = [
      'metrics',
      demographics.userId ? `user${demographics.userId.toString().padStart(2, '0')}` : 'unknown',
      demographics.ageGroup || 'unknown',
      demographics.gender || 'unknown',
      demographics.ethnicity || 'unknown',
      timestamp
    ].join('__') + '.json';
    
    // Create S3 path for metrics data
    // /metrics/[AGEGROUP]/[GENDER]/[ETHNICITY]/[FILENAME].json
    const key = `metrics/${demographics.ageGroup || 'unknown'}/${demographics.gender || 'unknown'}/${demographics.ethnicity || 'unknown'}/${filename}`;
    
    // Create an S3 client
    const s3Client = new S3Client({ region });
    
    // Upload metrics data to S3
    const uploadParams = {
      Bucket: bucketName,
      Key: key,
      Body: JSON.stringify(metricsData, null, 2),
      ContentType: 'application/json'
    };
    
    // Upload the metrics data
    const uploadResponse = await s3Client.send(new PutObjectCommand(uploadParams));
    
    console.log(`Successfully uploaded metrics to S3: ${key}`);
    
    // Optionally trigger SageMaker model evaluation
    if (process.env.SAGEMAKER_ENDPOINT) {
      try {
        await triggerSageMakerEvaluation(metricsData, demographics);
      } catch (sageMakerError) {
        console.error('Failed to trigger SageMaker evaluation:', sageMakerError);
        // Continue with the metrics upload response even if SageMaker trigger fails
      }
    }
    
    return {
      success: true,
      path: key,
      message: 'Metrics logged successfully',
      sageMakerTriggered: !!process.env.SAGEMAKER_ENDPOINT
    };
  } catch (error) {
    console.error('Error logging metrics:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Trigger SageMaker model evaluation with the provided metrics data
 * 
 * @param {Object} metricsData - The metrics data to send to SageMaker
 * @param {Object} demographics - The demographics data for categorization
 * @returns {Promise<Object>} - Result of the SageMaker invocation
 */
async function triggerSageMakerEvaluation(metricsData, demographics) {
  try {
    // Create SageMaker runtime client
    const sagemakerruntime = new SageMakerRuntimeClient({
      region: process.env.AWS_REGION || 'us-east-1'
    });

    // Endpoint name from environment variable
    const endpointName = process.env.SAGEMAKER_ENDPOINT;

    // Prepare the payload for SageMaker
    const payload = {
      metrics: metricsData.metrics,
      demographics,
      timestamp: metricsData.timestamp
    };

    // Invoke SageMaker endpoint
    const command = new InvokeEndpointCommand({
      EndpointName: endpointName,
      Body: JSON.stringify(payload),
      ContentType: 'application/json',
      Accept: 'application/json'
    });

    // Call SageMaker runtime invoke endpoint
    const response = await sagemakerruntime.send(command);

    // Parse the response body
    const responseBody = JSON.parse(Buffer.from(response.Body).toString('utf8'));

    console.log('SageMaker evaluation response:', responseBody);

    return {
      success: true,
      response: responseBody
    };
  } catch (error) {
    console.error('Error invoking SageMaker endpoint:', error);
    throw error;
  }
}

/**
 * Get metrics from S3 storage with optional demographic filters
 * 
 * @param {Object} filters - Optional demographic filters to apply
 * @returns {Promise<Object>} - Aggregated metrics data
 */
async function getMetricsByDemographic(filters = {}) {
  try {
    // Get bucket name from environment variable
    const bucketName = process.env.AWS_S3_BUCKET || 'icudatasetphrasesfortesting';
    const region = process.env.AWS_REGION || 'us-east-1';
    
    // Mock implementation for now - in production, this would retrieve and aggregate 
    // metrics data from S3 based on the provided filters
    console.log('Retrieving metrics with filters:', filters);
    
    // Sample aggregated metrics data
    const aggregatedMetrics = {
      overall: {
        accuracy: 0.87,
        precision: 0.89,
        recall: 0.86,
        f1Score: 0.875,
        confidenceScore: 0.92,
        sampleCount: 1250
      },
      byDemographic: {
        gender: {
          male: {
            accuracy: 0.89,
            precision: 0.91,
            recall: 0.88,
            f1Score: 0.895,
            confidenceScore: 0.94,
            sampleCount: 480
          },
          female: {
            accuracy: 0.86,
            precision: 0.88,
            recall: 0.85,
            f1Score: 0.865,
            confidenceScore: 0.91,
            sampleCount: 530
          },
          nonbinary: {
            accuracy: 0.83,
            precision: 0.85,
            recall: 0.82,
            f1Score: 0.835,
            confidenceScore: 0.88,
            sampleCount: 240
          }
        },
        ageGroup: {
          '18to39': {
            accuracy: 0.91,
            precision: 0.92,
            recall: 0.90,
            f1Score: 0.91,
            confidenceScore: 0.95,
            sampleCount: 450
          },
          '40to64': {
            accuracy: 0.86,
            precision: 0.88,
            recall: 0.85,
            f1Score: 0.865,
            confidenceScore: 0.90,
            sampleCount: 520
          },
          '65plus': {
            accuracy: 0.82,
            precision: 0.84,
            recall: 0.80,
            f1Score: 0.82,
            confidenceScore: 0.87,
            sampleCount: 280
          }
        },
        // Additional demographic breakdowns would be included here
      }
    };
    
    return {
      success: true,
      metrics: aggregatedMetrics,
      lastUpdated: new Date().toISOString()
    };
  } catch (error) {
    console.error('Error retrieving metrics:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

module.exports = {
  logMetricsByDemographic,
  getMetricsByDemographic
};
