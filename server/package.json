{"name": "icu-dataset-server", "version": "1.0.0", "description": "Server for ICU dataset application to handle AWS S3 uploads", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js"}, "dependencies": {"@aws-sdk/client-s3": "^3.350.0", "@aws-sdk/client-sagemaker": "^3.835.0", "@aws-sdk/client-sagemaker-runtime": "^3.350.0", "@tensorflow-models/face-landmarks-detection": "^1.0.6", "@tensorflow/tfjs": "^4.22.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.21.2", "multer": "^1.4.5-lts.1", "react-webcam": "^7.2.0"}, "devDependencies": {"nodemon": "^3.0.1"}}