<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ICU App Loading Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .success { background: #d5f4e6; color: #27ae60; }
        .error { background: #fadbd8; color: #e74c3c; }
        .warning { background: #fef9e7; color: #f39c12; }
        .info { background: #d6eaf8; color: #2980b9; }
        #app-frame {
            width: 100%;
            height: 600px;
            border: 2px solid #ddd;
            margin: 20px 0;
        }
        button {
            background: #3498db;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #2980b9; }
    </style>
</head>
<body>
    <h1>🔍 ICU Dataset Application - Loading Test</h1>
    
    <div id="test-results"></div>
    
    <button onclick="testAppLoading()">Test App Loading</button>
    <button onclick="testJSBundle()">Test JS Bundle</button>
    <button onclick="testBackend()">Test Backend</button>
    <button onclick="loadAppInFrame()">Load App in Frame</button>
    
    <h2>Application Preview:</h2>
    <iframe id="app-frame" src="about:blank"></iframe>
    
    <script>
        function addResult(message, type = 'info') {
            const results = document.getElementById('test-results');
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = message;
            results.appendChild(div);
        }

        function clearResults() {
            document.getElementById('test-results').innerHTML = '';
        }

        async function testAppLoading() {
            clearResults();
            addResult('🔍 Testing application loading...', 'info');

            try {
                const response = await fetch('http://localhost:3000');
                if (response.ok) {
                    const html = await response.text();
                    
                    // Check for basic HTML structure
                    if (html.includes('<div id="root">')) {
                        addResult('✅ HTML structure correct - found root div', 'success');
                    } else {
                        addResult('❌ HTML structure issue - no root div found', 'error');
                    }
                    
                    // Check for script tag
                    if (html.includes('bundle.js')) {
                        addResult('✅ JavaScript bundle referenced in HTML', 'success');
                    } else {
                        addResult('❌ No JavaScript bundle found in HTML', 'error');
                    }
                    
                    // Check title
                    const titleMatch = html.match(/<title>([^<]+)<\/title>/);
                    if (titleMatch) {
                        addResult(`✅ Page title: "${titleMatch[1]}"`, 'success');
                    } else {
                        addResult('❌ No page title found', 'error');
                    }
                    
                } else {
                    addResult(`❌ HTTP Error: ${response.status}`, 'error');
                }
            } catch (error) {
                addResult(`❌ Network Error: ${error.message}`, 'error');
            }
        }

        async function testJSBundle() {
            addResult('🔍 Testing JavaScript bundle...', 'info');

            try {
                const response = await fetch('http://localhost:3000/static/js/bundle.js');
                if (response.ok) {
                    const size = response.headers.get('content-length');
                    addResult(`✅ JS Bundle accessible (${(size/1024/1024).toFixed(1)}MB)`, 'success');
                    
                    // Test if bundle contains React
                    const jsContent = await response.text();
                    if (jsContent.includes('React')) {
                        addResult('✅ Bundle contains React code', 'success');
                    } else {
                        addResult('⚠️ Bundle may not contain React code', 'warning');
                    }
                    
                } else {
                    addResult(`❌ JS Bundle HTTP Error: ${response.status}`, 'error');
                }
            } catch (error) {
                addResult(`❌ JS Bundle Error: ${error.message}`, 'error');
            }
        }

        async function testBackend() {
            addResult('🔍 Testing backend connection...', 'info');

            try {
                const response = await fetch('http://localhost:5000/health');
                if (response.ok) {
                    const data = await response.json();
                    addResult(`✅ Backend healthy: ${data.status}`, 'success');
                } else {
                    addResult(`❌ Backend HTTP Error: ${response.status}`, 'error');
                }
            } catch (error) {
                addResult(`❌ Backend Error: ${error.message}`, 'error');
            }
        }

        function loadAppInFrame() {
            addResult('🔍 Loading app in iframe...', 'info');
            const frame = document.getElementById('app-frame');
            frame.src = 'http://localhost:3000';
            
            frame.onload = function() {
                try {
                    // Try to access iframe content (may fail due to CORS)
                    const frameDoc = frame.contentDocument || frame.contentWindow.document;
                    if (frameDoc) {
                        const rootDiv = frameDoc.getElementById('root');
                        if (rootDiv && rootDiv.innerHTML.trim()) {
                            addResult('✅ App loaded in iframe with content', 'success');
                        } else {
                            addResult('⚠️ App loaded but root div appears empty', 'warning');
                        }
                    }
                } catch (e) {
                    addResult('⚠️ Cannot inspect iframe content (CORS), but iframe loaded', 'warning');
                }
            };
            
            frame.onerror = function() {
                addResult('❌ Failed to load app in iframe', 'error');
            };
        }

        // Auto-run tests on page load
        window.addEventListener('load', function() {
            setTimeout(() => {
                testAppLoading();
                setTimeout(() => {
                    testJSBundle();
                    setTimeout(() => {
                        testBackend();
                    }, 1000);
                }, 1000);
            }, 500);
        });
    </script>
</body>
</html>
