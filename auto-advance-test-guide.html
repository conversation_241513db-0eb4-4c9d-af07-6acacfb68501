<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Auto-Advance Test Guide</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .step {
            border: 1px solid #ddd;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            background-color: #f8f9fa;
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        .code {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .highlight {
            background-color: yellow;
            padding: 2px 4px;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Auto-Advance Test Guide</h1>
        <p>This guide explains how to test the auto-advance functionality in the ICU Dataset Application.</p>
        
        <div class="step info">
            <h3>📋 What is Auto-Advance?</h3>
            <p>Auto-advance automatically moves to the next phrase after completing 3 recordings for the current phrase. This improves user experience by eliminating the need to manually navigate between phrases.</p>
            
            <h4>Expected Behavior:</h4>
            <ul>
                <li>After recording 3 videos for a phrase, the app should automatically advance to the next phrase</li>
                <li>The phrase text should update to show the new phrase</li>
                <li>The recording counter should reset to "Recording 1 of 3" for the new phrase</li>
                <li>If it's the last phrase, the completion screen should appear</li>
            </ul>
        </div>

        <div class="step">
            <h3>🚀 Step 1: Setup</h3>
            <ol>
                <li>Open the application at <a href="http://localhost:3000" target="_blank">http://localhost:3000</a></li>
                <li>Open browser developer tools (F12) and go to the Console tab</li>
                <li>Complete the consent form</li>
                <li>Fill out the demographics form</li>
                <li>Watch the training video (or skip if already completed)</li>
            </ol>
        </div>

        <div class="step">
            <h3>📝 Step 2: Select Phrases</h3>
            <ol>
                <li>Navigate to the phrase selection page</li>
                <li>Select <strong>at least 2 phrases</strong> from different categories (this is important for testing category switching)</li>
                <li>Click "Start Recording" to proceed to the recording interface</li>
            </ol>
        </div>

        <div class="step">
            <h3>🎬 Step 3: Test Auto-Advance</h3>
            <ol>
                <li>Start recording the first phrase</li>
                <li>Complete 3 recordings for the first phrase</li>
                <li><span class="highlight">Watch the console for auto-advance logs</span></li>
                <li>After the 3rd recording, the app should automatically advance to the next phrase</li>
            </ol>
            
            <h4>Console Logs to Look For:</h4>
            <div class="code">🔄 AUTO-ADVANCE EFFECT TRIGGERED
📹 RECORDING COMPLETED FUNCTION CALLED
📹 RECORDING COMPLETED - COUNT UPDATE
🔄 AUTO-ADVANCE CHECK
🎯 AUTO-ADVANCE: Phrase completion detected
📝 AUTO-ADVANCE: Moving to next phrase</div>
        </div>

        <div class="step">
            <h3>🧪 Step 4: Use Test Component (If Auto-Advance Fails)</h3>
            <ol>
                <li>Click the hamburger menu (☰) in the top-left corner</li>
                <li>Select "Auto-Advance Test" from the menu</li>
                <li>This component shows the current state and allows manual testing</li>
                <li>Click "Simulate Recording Completion" to test the auto-advance logic</li>
                <li>Watch the console and test log for debugging information</li>
            </ol>
        </div>

        <div class="step warning">
            <h3>⚠️ Troubleshooting</h3>
            
            <h4>If Auto-Advance Doesn't Work:</h4>
            <ul>
                <li><strong>Check Console Errors:</strong> Look for JavaScript errors in the browser console</li>
                <li><strong>Verify State Updates:</strong> Check if recording counts are updating correctly</li>
                <li><strong>Check useEffect Triggers:</strong> Look for "AUTO-ADVANCE EFFECT TRIGGERED" logs</li>
                <li><strong>Verify Phrase Selection:</strong> Ensure phrases are properly selected and stored</li>
            </ul>

            <h4>Common Issues:</h4>
            <ul>
                <li><strong>State Not Updating:</strong> Recording completion might not be calling the right function</li>
                <li><strong>useEffect Not Triggering:</strong> Dependencies might be incorrect or stale</li>
                <li><strong>Race Conditions:</strong> State updates might be batched incorrectly</li>
                <li><strong>Missing Phrases:</strong> selectedPhrases might be null or empty</li>
            </ul>
        </div>

        <div class="step success">
            <h3>✅ Success Criteria</h3>
            <p>The auto-advance functionality is working correctly if:</p>
            <ul>
                <li>After 3 recordings, the phrase automatically changes</li>
                <li>The recording counter resets to "1 of 3" for the new phrase</li>
                <li>Console shows the expected auto-advance logs</li>
                <li>Category updates if the next phrase is in a different category</li>
                <li>Completion screen appears after the last phrase</li>
            </ul>
        </div>

        <div class="step info">
            <h3>🔍 Debug Information</h3>
            <p>Key files involved in auto-advance:</p>
            <ul>
                <li><code>src/providers/RecordingSessionProvider.js</code> - Main auto-advance logic</li>
                <li><code>src/components/RecordingSessionManager.js</code> - Handles recording completion</li>
                <li><code>src/components/VideoRecorder.js</code> - Triggers recording completion</li>
            </ul>
            
            <p>Key functions:</p>
            <ul>
                <li><code>recordingCompleted()</code> - Updates recording counts</li>
                <li><code>useEffect()</code> - Monitors recording counts and triggers auto-advance</li>
                <li><code>handleVideoRecorded()</code> - Called when video upload completes</li>
            </ul>
        </div>
    </div>

    <script>
        // Add some interactive functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Add click handlers for external links
            const links = document.querySelectorAll('a[target="_blank"]');
            links.forEach(link => {
                link.addEventListener('click', function(e) {
                    console.log('Opening:', this.href);
                });
            });
        });
    </script>
</body>
</html>
