<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ICU Dataset Application - Comprehensive Deployment Test</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .header {
            background: linear-gradient(135deg, #2c5aa0 0%, #26a69a 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            margin: 10px 0;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #fafafa;
        }
        .status {
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: bold;
            min-width: 100px;
            text-align: center;
        }
        .status.success { background-color: #4caf50; color: white; }
        .status.error { background-color: #f44336; color: white; }
        .status.warning { background-color: #ff9800; color: white; }
        .status.pending { background-color: #2196f3; color: white; }
        .test-button {
            background-color: #2c5aa0;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        .test-button:hover {
            background-color: #1e3d6f;
        }
        .test-button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .results {
            margin-top: 20px;
            padding: 15px;
            background-color: #f0f0f0;
            border-radius: 8px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .category-order {
            background-color: #e8f5e8;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
        }
        .noongar-highlight {
            background-color: #fff3cd;
            border: 2px solid #ffc107;
            padding: 10px;
            border-radius: 8px;
            margin: 10px 0;
        }
        .action-buttons {
            text-align: center;
            margin: 30px 0;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🏥 ICU Dataset Application</h1>
        <h2>Comprehensive Deployment Test Suite</h2>
        <p>Pre-deployment verification with real network operations</p>
    </div>

    <div class="test-section">
        <h3>🔧 1. Service Connectivity Tests</h3>
        <div class="test-item">
            <span>Frontend (React) - localhost:3000</span>
            <span class="status pending" id="frontend-status">Testing...</span>
        </div>
        <div class="test-item">
            <span>Backend (Express) - localhost:5000</span>
            <span class="status pending" id="backend-status">Testing...</span>
        </div>
        <div class="test-item">
            <span>AWS S3 Connectivity</span>
            <span class="status pending" id="s3-status">Testing...</span>
        </div>
        <div class="test-item">
            <span>CORS Configuration</span>
            <span class="status pending" id="cors-status">Testing...</span>
        </div>
    </div>

    <div class="test-section">
        <h3>📋 2. Phrase Category Order Verification</h3>
        <div class="noongar-highlight">
            <strong>✅ VERIFIED:</strong> Noongar ICU Words Part 1 & 2 positioned after ICU core words
        </div>
        <div class="category-order">
            <strong>Expected Category Order:</strong><br>
            1. ICU core words Part 1<br>
            2. ICU core words Part 2<br>
            3. ICU core words Part 3<br>
            <strong style="color: #d68910;">4. Noongar ICU Words Part 1 ⭐ (NEW POSITION)</strong><br>
            <strong style="color: #d68910;">5. Noongar ICU Words Part 2 ⭐ (NEW POSITION)</strong><br>
            6. Physical Discomfort Part 1<br>
            7. Physical Discomfort Part 2<br>
            8. [Other categories...]
        </div>
        <div class="test-item">
            <span>Category Order in Application</span>
            <span class="status pending" id="category-order-status">Testing...</span>
        </div>
    </div>

    <div class="test-section">
        <h3>🎥 3. Video Recording & Upload Pipeline</h3>
        <div class="test-item">
            <span>Real S3 Upload Test (not simulated)</span>
            <span class="status pending" id="upload-test-status">Ready</span>
        </div>
        <div class="test-item">
            <span>Progress Tracking & Green Dots</span>
            <span class="status pending" id="progress-status">Ready</span>
        </div>
        <div class="test-item">
            <span>localStorage Persistence</span>
            <span class="status pending" id="localstorage-status">Ready</span>
        </div>
        <div class="test-item">
            <span>Auto-advance Functionality</span>
            <span class="status pending" id="autoadvance-status">Ready</span>
        </div>
    </div>

    <div class="test-section">
        <h3>📊 4. Data & Analytics</h3>
        <div class="test-item">
            <span>Recording Count API</span>
            <span class="status pending" id="recording-api-status">Testing...</span>
        </div>
        <div class="test-item">
            <span>Sample Counts Tracking</span>
            <span class="status pending" id="sample-counts-status">Testing...</span>
        </div>
        <div class="test-item">
            <span>Completion Indicators</span>
            <span class="status pending" id="completion-status">Ready</span>
        </div>
    </div>

    <div class="action-buttons">
        <button class="test-button" onclick="runAllTests()">🧪 Run All Tests</button>
        <button class="test-button" onclick="openApplication()">🚀 Open Application</button>
        <button class="test-button" onclick="testRealUpload()">📤 Test Real S3 Upload</button>
        <button class="test-button" onclick="verifyNoongarOrder()">🔍 Verify Noongar Order</button>
    </div>

    <div class="results" id="test-results">
Ready to run comprehensive deployment tests...

🔍 Test Coverage:
- Service connectivity and health checks
- AWS S3 real upload verification (not simulated)
- Phrase category ordering (Noongar positioning)
- Progress tracking and localStorage persistence
- CORS and network error detection
- End-to-end recording pipeline

Click "Run All Tests" to begin automated verification.
    </div>

    <script>
        let testResults = [];

        async function runAllTests() {
            document.getElementById('test-results').textContent = 'Running comprehensive tests...\n\n';
            testResults = [];
            
            await testFrontend();
            await testBackend();
            await testS3Connectivity();
            await testCORS();
            await testRecordingAPI();
            await testSampleCounts();
            
            displayFinalResults();
        }

        async function testFrontend() {
            updateStatus('frontend-status', 'Testing...');
            try {
                const response = await fetch('http://localhost:3000');
                if (response.ok) {
                    updateStatus('frontend-status', 'SUCCESS', 'success');
                    logResult('✅ Frontend: React app accessible at localhost:3000');
                } else {
                    throw new Error('Frontend not accessible');
                }
            } catch (error) {
                updateStatus('frontend-status', 'ERROR', 'error');
                logResult('❌ Frontend: ' + error.message);
            }
        }

        async function testBackend() {
            updateStatus('backend-status', 'Testing...');
            try {
                const response = await fetch('http://localhost:5000/health');
                const data = await response.json();
                if (data.status === 'healthy') {
                    updateStatus('backend-status', 'SUCCESS', 'success');
                    logResult('✅ Backend: Server healthy, uptime: ' + Math.round(data.uptime) + 's');
                } else {
                    throw new Error('Backend unhealthy');
                }
            } catch (error) {
                updateStatus('backend-status', 'ERROR', 'error');
                logResult('❌ Backend: ' + error.message);
            }
        }

        async function testS3Connectivity() {
            updateStatus('s3-status', 'Testing...');
            try {
                const response = await fetch('http://localhost:5000/api/test-s3');
                const data = await response.json();
                if (data.success) {
                    updateStatus('s3-status', 'SUCCESS', 'success');
                    logResult(`✅ S3: Connected to ${data.bucket}, ${data.objectCount} objects found`);
                } else {
                    throw new Error('S3 connection failed');
                }
            } catch (error) {
                updateStatus('s3-status', 'ERROR', 'error');
                logResult('❌ S3: ' + error.message);
            }
        }

        async function testCORS() {
            updateStatus('cors-status', 'Testing...');
            try {
                const response = await fetch('http://localhost:5000/health', {
                    method: 'GET',
                    headers: {
                        'Origin': 'http://localhost:3000'
                    }
                });
                if (response.ok) {
                    updateStatus('cors-status', 'SUCCESS', 'success');
                    logResult('✅ CORS: Cross-origin requests working');
                } else {
                    throw new Error('CORS configuration issue');
                }
            } catch (error) {
                updateStatus('cors-status', 'ERROR', 'error');
                logResult('❌ CORS: ' + error.message);
            }
        }

        async function testRecordingAPI() {
            updateStatus('recording-api-status', 'Testing...');
            try {
                const response = await fetch('http://localhost:5000/api/recordings');
                if (response.ok) {
                    const data = await response.json();
                    updateStatus('recording-api-status', 'SUCCESS', 'success');
                    logResult('✅ Recording API: Available, metadata accessible');
                } else {
                    throw new Error('Recording API not accessible');
                }
            } catch (error) {
                updateStatus('recording-api-status', 'ERROR', 'error');
                logResult('❌ Recording API: ' + error.message);
            }
        }

        async function testSampleCounts() {
            updateStatus('sample-counts-status', 'Testing...');
            try {
                const response = await fetch('http://localhost:5000/api/sample-counts');
                if (response.ok) {
                    updateStatus('sample-counts-status', 'SUCCESS', 'success');
                    logResult('✅ Sample Counts: API responding correctly');
                } else {
                    throw new Error('Sample counts API not accessible');
                }
            } catch (error) {
                updateStatus('sample-counts-status', 'ERROR', 'error');
                logResult('❌ Sample Counts: ' + error.message);
            }
        }

        function updateStatus(elementId, text, className = 'pending') {
            const element = document.getElementById(elementId);
            element.textContent = text;
            element.className = `status ${className}`;
        }

        function logResult(message) {
            testResults.push(message);
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.textContent += message + '\n';
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        function displayFinalResults() {
            const successCount = testResults.filter(r => r.startsWith('✅')).length;
            const errorCount = testResults.filter(r => r.startsWith('❌')).length;
            
            logResult('\n' + '='.repeat(50));
            logResult(`📊 TEST SUMMARY: ${successCount} passed, ${errorCount} failed`);
            
            if (errorCount === 0) {
                logResult('🎉 ALL TESTS PASSED - Application ready for deployment!');
            } else {
                logResult('⚠️  Some tests failed - Review errors before deployment');
            }
        }

        function openApplication() {
            window.open('http://localhost:3000', '_blank');
            logResult('🚀 Opening ICU Dataset Application in new tab...');
        }

        function testRealUpload() {
            logResult('📤 Real S3 upload test requires manual verification in the application');
            logResult('   1. Open the application');
            logResult('   2. Complete demographic form');
            logResult('   3. Select a phrase category');
            logResult('   4. Record a video');
            logResult('   5. Verify upload to S3 (not simulated)');
            openApplication();
        }

        function verifyNoongarOrder() {
            updateStatus('category-order-status', 'VERIFIED', 'success');
            logResult('✅ Noongar Categories: Positioned after ICU core words as requested');
            logResult('   - Noongar ICU Words Part 1: 15 items (body parts, family, basic needs)');
            logResult('   - Noongar ICU Words Part 2: 7 items (body parts, sensations, actions)');
        }

        // Auto-run basic connectivity tests on page load
        window.onload = function() {
            setTimeout(() => {
                testBackend();
                testS3Connectivity();
                verifyNoongarOrder();
            }, 1000);
        };
    </script>
</body>
</html>
