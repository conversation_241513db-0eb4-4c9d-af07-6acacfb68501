<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 S3 CORS Fix Guide</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .urgent-fix {
            background: #dc3545;
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .fix-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .cors-policy {
            background: #1e1e1e;
            color: #00ff00;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            margin: 10px 0;
            overflow-x: auto;
        }
        .step {
            background: #e9ecef;
            padding: 15px;
            margin: 10px 0;
            border-left: 4px solid #007bff;
            border-radius: 4px;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            text-decoration: none;
            display: inline-block;
        }
        .button.danger {
            background: #dc3545;
        }
        .button.success {
            background: #28a745;
        }
    </style>
</head>
<body>
    <div class="urgent-fix">
        <h1>🚨 URGENT: S3 CORS Configuration Fix Required</h1>
        <p><strong>Issue:</strong> S3 bucket "icudatasetphrasesfortesting" is blocking CORS requests from localhost:3003</p>
        <p><strong>Impact:</strong> Video uploads fail after successful recording</p>
        <p><strong>Solution:</strong> Update S3 CORS policy immediately</p>
    </div>

    <div class="fix-container">
        <h2>🔧 Step 1: Update S3 CORS Policy</h2>
        
        <div class="step">
            <h3>1.1 Access AWS S3 Console</h3>
            <p>1. Go to <a href="https://s3.console.aws.amazon.com/s3/buckets/icudatasetphrasesfortesting" target="_blank">AWS S3 Console</a></p>
            <p>2. Navigate to bucket: <strong>icudatasetphrasesfortesting</strong></p>
            <p>3. Click on "Permissions" tab</p>
            <p>4. Scroll down to "Cross-origin resource sharing (CORS)" section</p>
            <p>5. Click "Edit"</p>
        </div>

        <div class="step">
            <h3>1.2 Replace CORS Configuration</h3>
            <p>Copy and paste this CORS policy (replaces any existing configuration):</p>
            <div class="cors-policy">
[
    {
        "AllowedHeaders": [
            "*"
        ],
        "AllowedMethods": [
            "GET",
            "PUT",
            "POST",
            "DELETE",
            "HEAD"
        ],
        "AllowedOrigins": [
            "http://localhost:3000",
            "http://localhost:3001",
            "http://localhost:3002",
            "http://localhost:3003",
            "http://localhost:3004",
            "http://127.0.0.1:3000",
            "http://127.0.0.1:3001",
            "http://127.0.0.1:3002",
            "http://127.0.0.1:3003",
            "https://icuphrasecollection.com",
            "https://*.netlify.app"
        ],
        "ExposeHeaders": [
            "ETag",
            "x-amz-meta-custom-header"
        ],
        "MaxAgeSeconds": 3000
    }
]
            </div>
        </div>

        <div class="step">
            <h3>1.3 Save Configuration</h3>
            <p>1. Click "Save changes"</p>
            <p>2. Wait for the configuration to propagate (usually 1-2 minutes)</p>
            <p>3. Verify the policy is active</p>
        </div>
    </div>

    <div class="fix-container">
        <h2>🔄 Step 2: Alternative - Backend Upload Mode</h2>
        
        <div class="warning">
            <h3>⚠️ If S3 CORS Fix Doesn't Work Immediately</h3>
            <p>We can switch to backend-proxied uploads as a fallback solution.</p>
        </div>

        <div class="step">
            <h3>2.1 Switch to Backend Upload Mode</h3>
            <p>If S3 CORS continues to fail, we can modify the application to use backend uploads:</p>
            <ol>
                <li>Frontend sends video blob to backend server</li>
                <li>Backend server uploads to S3 using server-side credentials</li>
                <li>No CORS issues since backend-to-S3 communication doesn't involve browsers</li>
            </ol>
        </div>

        <button class="button danger" onclick="switchToBackendMode()">🔄 Switch to Backend Upload Mode</button>
    </div>

    <div class="fix-container">
        <h2>🧪 Step 3: Test S3 Upload After CORS Fix</h2>
        
        <div class="step">
            <h3>3.1 Test Direct S3 Upload</h3>
            <button class="button" onclick="testS3Upload()">🧪 Test S3 CORS Configuration</button>
            <div id="s3-test-results"></div>
        </div>

        <div class="step">
            <h3>3.2 Test Video Recording Flow</h3>
            <p>After CORS fix:</p>
            <ol>
                <li>Go to <a href="http://localhost:3003" target="_blank">ICU Dataset Application</a></li>
                <li>Complete demographics and phrase selection</li>
                <li>Record a test video</li>
                <li>Monitor console for successful upload messages</li>
            </ol>
        </div>
    </div>

    <div class="fix-container">
        <h2>📋 Expected Results After Fix</h2>
        
        <div class="success">
            <h3>✅ Successful Upload Console Output:</h3>
            <div class="cors-policy">
🎬 MediaRecorder onstop event triggered
  videoBlob created: {size: 357668, type: "video/webm"}
📤 Uploading single video to AWS S3...
✅ Single recording saved successfully: {url: "https://...", key: "icu-videos/..."}
✅ Upload completed successfully - resetting processing state
🎉 Recording saved and processed without errors!
            </div>
        </div>
    </div>

    <script>
        async function testS3Upload() {
            const resultsDiv = document.getElementById('s3-test-results');
            resultsDiv.innerHTML = '<p>🧪 Testing S3 CORS configuration...</p>';
            
            try {
                // Test CORS preflight to S3
                const testUrl = 'https://icudatasetphrasesfortesting.s3.ap-southeast-2.amazonaws.com/';
                
                const response = await fetch(testUrl, {
                    method: 'OPTIONS',
                    headers: {
                        'Origin': 'http://localhost:3003',
                        'Access-Control-Request-Method': 'PUT',
                        'Access-Control-Request-Headers': 'Content-Type'
                    }
                });
                
                if (response.ok || response.status === 200) {
                    resultsDiv.innerHTML = `
                        <div class="success">
                            <h4>✅ S3 CORS Test PASSED!</h4>
                            <p>Status: ${response.status} ${response.statusText}</p>
                            <p>CORS headers are properly configured.</p>
                            <p><strong>You can now test video recording and upload!</strong></p>
                        </div>
                    `;
                } else {
                    resultsDiv.innerHTML = `
                        <div class="warning">
                            <h4>⚠️ S3 CORS Test Inconclusive</h4>
                            <p>Status: ${response.status} ${response.statusText}</p>
                            <p>Try the actual video upload test or switch to backend mode.</p>
                        </div>
                    `;
                }
                
            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="warning">
                        <h4>❌ S3 CORS Test Failed</h4>
                        <p>Error: ${error.message}</p>
                        <p>This might be expected for OPTIONS requests. Try actual video upload.</p>
                    </div>
                `;
            }
        }

        function switchToBackendMode() {
            alert('Backend upload mode switch requires code modification. Please confirm if you want to proceed with this approach.');
        }
    </script>
</body>
</html>
