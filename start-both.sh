#!/bin/bash

# Start the server in the background
echo "Starting the server..."
cd "$(dirname "$0")/server" && node server.js &
SERVER_PID=$!

# Wait for server to start
sleep 2

# Start the React app in the background
echo "Starting the React app..."
cd "$(dirname "$0")" && npm start &
REACT_PID=$!

# Function to clean up processes on exit
cleanup() {
  echo "Stopping server and React app..."
  kill $SERVER_PID $REACT_PID 2>/dev/null
  exit 0
}

# Set up trap to catch Ctrl+C
trap cleanup INT

# Keep script running
echo "Both applications are running. Press Ctrl+C to stop both."
wait
