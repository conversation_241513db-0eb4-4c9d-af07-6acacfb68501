#!/usr/bin/env node

/**
 * S3 Connectivity Diagnostic Script
 * Systematically tests S3 connectivity to identify the exact issue
 */

const { execSync } = require('child_process');
const fs = require('fs');

console.log('🔍 === S3 CONNECTIVITY DIAGNOSTIC ===\n');

// Test 1: Environment Variables Check
console.log('1️⃣ Environment Variables Check...');
try {
  const envContent = fs.readFileSync('.env', 'utf8');
  
  const identityPool = envContent.match(/REACT_APP_AWS_IDENTITY_POOL_ID=(.+)/)?.[1];
  const region = envContent.match(/REACT_APP_AWS_REGION=(.+)/)?.[1];
  const bucket = envContent.match(/REACT_APP_S3_BUCKET=(.+)/)?.[1];
  const accessKey = envContent.match(/AWS_ACCESS_KEY_ID=(.+)/)?.[1];
  const secretKey = envContent.match(/AWS_SECRET_ACCESS_KEY=(.+)/)?.[1];
  
  console.log('Frontend Configuration:');
  console.log(`  Identity Pool: ${identityPool || 'NOT SET'}`);
  console.log(`  Region: ${region || 'NOT SET'}`);
  console.log(`  Bucket: ${bucket || 'NOT SET'}`);
  
  console.log('Backend Configuration:');
  console.log(`  Access Key: ${accessKey ? accessKey.substring(0, 8) + '...' : 'NOT SET'}`);
  console.log(`  Secret Key: ${secretKey ? '***CONFIGURED***' : 'NOT SET'}`);
  
} catch (error) {
  console.log('❌ Could not read .env file:', error.message);
}

// Test 2: Backend S3 Test
console.log('\n2️⃣ Backend S3 Connection Test...');
try {
  const backendTest = execSync('curl -s "http://localhost:5000/api/test-s3"', { encoding: 'utf8' });
  console.log('Backend S3 Test Response:', backendTest);
} catch (error) {
  console.log('❌ Backend S3 test failed:', error.message);
}

// Test 3: AWS CLI Test (if available)
console.log('\n3️⃣ AWS CLI Test (if available)...');
try {
  const awsTest = execSync('aws s3 ls s3://icudatasetphrasesfortesting --region ap-southeast-2', { encoding: 'utf8' });
  console.log('✅ AWS CLI can access bucket');
  console.log('Bucket contents preview:', awsTest.split('\n').slice(0, 5).join('\n'));
} catch (error) {
  console.log('⚠️ AWS CLI not available or configured:', error.message);
}

// Test 4: Network Connectivity to AWS
console.log('\n4️⃣ Network Connectivity to AWS...');
try {
  const awsConnectivity = execSync('curl -s -I https://s3.ap-southeast-2.amazonaws.com', { encoding: 'utf8' });
  if (awsConnectivity.includes('200 OK') || awsConnectivity.includes('403 Forbidden')) {
    console.log('✅ Can reach AWS S3 endpoints');
  } else {
    console.log('❌ Cannot reach AWS S3 endpoints');
    console.log('Response:', awsConnectivity);
  }
} catch (error) {
  console.log('❌ Network connectivity to AWS failed:', error.message);
}

// Test 5: Specific Bucket Connectivity
console.log('\n5️⃣ Specific Bucket Connectivity...');
try {
  const bucketTest = execSync('curl -s -I https://icudatasetphrasesfortesting.s3.ap-southeast-2.amazonaws.com', { encoding: 'utf8' });
  if (bucketTest.includes('200 OK') || bucketTest.includes('403 Forbidden')) {
    console.log('✅ Can reach specific S3 bucket endpoint');
  } else {
    console.log('❌ Cannot reach specific S3 bucket');
    console.log('Response:', bucketTest);
  }
} catch (error) {
  console.log('❌ Bucket connectivity test failed:', error.message);
}

// Test 6: CORS Preflight Test
console.log('\n6️⃣ CORS Preflight Test...');
try {
  const corsTest = execSync(`curl -s -X OPTIONS -H "Origin: http://localhost:3000" -H "Access-Control-Request-Method: PUT" -H "Access-Control-Request-Headers: content-type" https://icudatasetphrasesfortesting.s3.ap-southeast-2.amazonaws.com`, { encoding: 'utf8' });
  console.log('CORS Response:', corsTest || 'No response (may indicate CORS issue)');
} catch (error) {
  console.log('❌ CORS test failed:', error.message);
}

console.log('\n🔧 === DIAGNOSTIC SUMMARY ===');
console.log('Check the results above to identify:');
console.log('• Missing or invalid environment variables');
console.log('• Backend S3 connection failures');
console.log('• Network connectivity issues');
console.log('• CORS configuration problems');
console.log('• Bucket access permissions');
console.log('\nNext: Run specific S3 operation tests to isolate the exact failure point.');
