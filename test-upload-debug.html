<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ICU Dataset Application - Upload Debug Test</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        h1 {
            color: #009688;
            text-align: center;
            margin-bottom: 30px;
        }
        h2 {
            color: #00796b;
            border-bottom: 2px solid #e0f2f1;
            padding-bottom: 10px;
        }
        .button-group {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        button {
            background: #009688;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: background-color 0.3s;
        }
        button:hover {
            background: #00796b;
        }
        button.danger {
            background: #f44336;
        }
        button.danger:hover {
            background: #d32f2f;
        }
        .output {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.4;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
            margin-top: 10px;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-good { background: #4caf50; }
        .status-warning { background: #ff9800; }
        .status-error { background: #f44336; }
    </style>
</head>
<body>
    <h1>🔧 ICU Dataset Application - Upload Debug Test</h1>
    
    <div class="container">
        <h2>🔍 Environment Check</h2>
        <div class="button-group">
            <button onclick="checkEnvironment()">🔧 Check Environment Variables</button>
            <button onclick="testBackendConnection()">🌐 Test Backend Connection</button>
            <button onclick="simulateUpload()">📤 Simulate Upload</button>
        </div>
        <div id="env-output" class="output"></div>
    </div>

    <div class="container">
        <h2>📤 Upload Test</h2>
        <div class="button-group">
            <button onclick="createTestBlob()">🎬 Create Test Video Blob</button>
            <button onclick="testDirectUpload()">🚀 Test Direct Upload</button>
            <button onclick="testBackendUpload()">🔄 Test Backend Upload</button>
        </div>
        <div id="upload-output" class="output"></div>
    </div>

    <div class="container">
        <h2>🐛 Debug Console</h2>
        <div class="button-group">
            <button onclick="clearConsole()">🗑️ Clear Console</button>
            <button onclick="captureConsoleErrors()">📝 Capture Console Errors</button>
        </div>
        <div id="console-output" class="output"></div>
    </div>

    <script>
        let testVideoBlob = null;
        let consoleErrors = [];

        function log(outputId, message) {
            const output = document.getElementById(outputId);
            output.textContent += message + '\n';
        }

        function clearLog(outputId) {
            document.getElementById(outputId).textContent = '';
        }

        function checkEnvironment() {
            clearLog('env-output');
            log('env-output', '🔧 Checking environment variables...\n');
            
            // Check if we're on the React app domain
            const isReactApp = window.location.port === '3003' || window.location.port === '3000';
            log('env-output', `🌐 Current URL: ${window.location.href}`);
            log('env-output', `⚛️ React app detected: ${isReactApp}`);
            
            if (isReactApp) {
                log('env-output', '\n📋 Environment variables (from React app):');
                log('env-output', '  Note: Only REACT_APP_ variables are available in browser');
                log('env-output', '  Backend variables are not visible here');
            } else {
                log('env-output', '\n⚠️ Not running on React app - environment variables not available');
                log('env-output', 'Open this test from the React app at http://localhost:3003');
            }
        }

        function testBackendConnection() {
            clearLog('env-output');
            log('env-output', '🌐 Testing backend connection...\n');
            
            const backendUrl = 'http://localhost:5000';
            
            fetch(`${backendUrl}/health`)
                .then(response => response.json())
                .then(data => {
                    log('env-output', '✅ Backend connection successful!');
                    log('env-output', JSON.stringify(data, null, 2));
                })
                .catch(error => {
                    log('env-output', '❌ Backend connection failed:');
                    log('env-output', error.message);
                    log('env-output', '\n💡 Make sure the backend server is running:');
                    log('env-output', '   cd server && npm start');
                });
        }

        function createTestBlob() {
            clearLog('upload-output');
            log('upload-output', '🎬 Creating test video blob...\n');
            
            try {
                // Create a simple test blob that mimics a video file
                const testData = new Uint8Array(1024); // 1KB test data
                testData.fill(42); // Fill with test data
                
                testVideoBlob = new Blob([testData], { type: 'video/webm' });
                
                log('upload-output', '✅ Test video blob created successfully!');
                log('upload-output', `📊 Blob details:`);
                log('upload-output', `  Size: ${testVideoBlob.size} bytes`);
                log('upload-output', `  Type: ${testVideoBlob.type}`);
                log('upload-output', `  Valid blob: ${testVideoBlob instanceof Blob}`);
            } catch (error) {
                log('upload-output', '❌ Failed to create test blob:');
                log('upload-output', error.message);
            }
        }

        function testBackendUpload() {
            clearLog('upload-output');
            log('upload-output', '🔄 Testing backend upload...\n');
            
            if (!testVideoBlob) {
                log('upload-output', '⚠️ No test blob available. Create one first.');
                return;
            }
            
            const formData = new FormData();
            formData.append('video', testVideoBlob, 'test-upload.webm');
            formData.append('phrase', 'test phrase');
            formData.append('category', 'test');
            formData.append('recordingNumber', '1');
            formData.append('demographics', JSON.stringify({
                userId: 'test-user',
                ageGroup: '25to39',
                gender: 'female',
                ethnicity: 'caucasian'
            }));
            
            log('upload-output', '📤 Sending upload request to backend...');
            
            fetch('http://localhost:5000/upload', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                log('upload-output', `📥 Response status: ${response.status}`);
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    log('upload-output', '✅ Backend upload successful!');
                    log('upload-output', JSON.stringify(data, null, 2));
                } else {
                    log('upload-output', '❌ Backend upload failed:');
                    log('upload-output', JSON.stringify(data, null, 2));
                }
            })
            .catch(error => {
                log('upload-output', '❌ Backend upload error:');
                log('upload-output', error.message);
            });
        }

        function simulateUpload() {
            clearLog('env-output');
            log('env-output', '📤 Simulating upload process...\n');
            
            // Simulate the logic from awsStorage.js
            const hasIdentityPool = true; // From .env
            const hasRegion = true; // From .env
            const hasBucket = true; // From .env
            const forceBackendMode = true; // From awsStorage.js line 15
            
            const isAWSConfigured = hasIdentityPool && hasRegion && hasBucket && !forceBackendMode;
            
            log('env-output', '🔧 Upload mode determination:');
            log('env-output', `  hasIdentityPool: ${hasIdentityPool}`);
            log('env-output', `  hasRegion: ${hasRegion}`);
            log('env-output', `  hasBucket: ${hasBucket}`);
            log('env-output', `  forceBackendMode: ${forceBackendMode}`);
            log('env-output', `  isAWSConfigured: ${isAWSConfigured}`);
            
            if (forceBackendMode) {
                log('env-output', '\n✅ Should use BACKEND UPLOAD mode');
                log('env-output', '📍 Upload URL: http://localhost:5000/upload');
            } else if (isAWSConfigured) {
                log('env-output', '\n⚠️ Should use DIRECT S3 upload mode');
                log('env-output', '📍 This might cause CORS issues');
            } else {
                log('env-output', '\n❌ AWS not configured properly');
            }
        }

        function clearConsole() {
            clearLog('console-output');
            consoleErrors = [];
        }

        function captureConsoleErrors() {
            clearLog('console-output');
            log('console-output', '📝 Console error capture started...\n');
            
            // Override console.error to capture errors
            const originalError = console.error;
            console.error = function(...args) {
                consoleErrors.push({
                    timestamp: new Date().toISOString(),
                    args: args
                });
                originalError.apply(console, args);
                
                // Update the display
                clearLog('console-output');
                log('console-output', '📝 Captured console errors:\n');
                consoleErrors.forEach((error, index) => {
                    log('console-output', `${index + 1}. [${error.timestamp}]`);
                    log('console-output', `   ${error.args.join(' ')}\n`);
                });
            };
            
            log('console-output', '✅ Console error capture active');
            log('console-output', 'Errors will appear here as they occur');
        }

        // Initialize on page load
        window.addEventListener('load', () => {
            checkEnvironment();
        });
    </script>
</body>
</html>
