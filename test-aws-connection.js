#!/usr/bin/env node

// Quick AWS Connection Test Script
// Run this with: node test-aws-connection.js

const { S3Client, ListObjectsV2Command } = require('@aws-sdk/client-s3');
const { fromCognitoIdentityPool } = require('@aws-sdk/credential-providers');

// Load environment variables
require('dotenv').config();

const BUCKET_NAME = process.env.REACT_APP_S3_BUCKET || 'icudatasetphrasesfortesting';
const REGION = process.env.REACT_APP_AWS_REGION || 'ap-southeast-2';
const IDENTITY_POOL_ID = process.env.REACT_APP_AWS_IDENTITY_POOL_ID;

console.log('🧪 AWS Connection Test Script');
console.log('==============================');
console.log('Configuration:');
console.log('  - Region:', REGION);
console.log('  - Bucket:', BUCKET_NAME);
console.log('  - Identity Pool ID:', IDENTITY_POOL_ID);
console.log('');

// Check if AWS credentials are properly configured
const isAWSConfigured = () => {
  return IDENTITY_POOL_ID && IDENTITY_POOL_ID !== 'your-identity-pool-id-here';
};

async function testAWSConnection() {
  console.log('🔍 Step 1: Checking AWS configuration...');
  
  if (!isAWSConfigured()) {
    console.error('❌ AWS not configured properly');
    console.error('   Identity Pool ID is missing or invalid');
    return;
  }
  
  console.log('✅ AWS configuration looks valid');
  
  console.log('🔍 Step 2: Initializing S3 client...');
  
  let s3Client;
  try {
    s3Client = new S3Client({
      region: REGION,
      credentials: fromCognitoIdentityPool({
        clientConfig: { region: REGION },
        identityPoolId: IDENTITY_POOL_ID
      })
    });
    console.log('✅ S3 client initialized successfully');
  } catch (error) {
    console.error('❌ Failed to initialize S3 client:', error.message);
    return;
  }
  
  console.log('🔍 Step 3: Testing bucket access...');
  
  try {
    const listCommand = new ListObjectsV2Command({
      Bucket: BUCKET_NAME,
      MaxKeys: 1
    });
    
    console.log('⏳ Sending ListObjectsV2 request...');
    const result = await s3Client.send(listCommand);
    
    console.log('✅ AWS connection test successful!');
    console.log('📊 Results:');
    console.log('   - Bucket:', BUCKET_NAME);
    console.log('   - Region:', REGION);
    console.log('   - Object count:', result.KeyCount || 0);
    console.log('   - Sample files:', result.Contents?.slice(0, 3).map(obj => obj.Key) || []);
    
  } catch (error) {
    console.error('❌ AWS connection test failed');
    console.error('📋 Error details:');
    console.error('   - Name:', error.name);
    console.error('   - Message:', error.message);
    console.error('   - Code:', error.code);
    console.error('   - Status Code:', error.$metadata?.httpStatusCode);
    console.error('   - Request ID:', error.$metadata?.requestId);
    
    // Provide specific troubleshooting advice
    console.log('');
    console.log('🔧 Troubleshooting advice:');
    
    if (error.name === 'CredentialsProviderError') {
      console.log('   - The Cognito Identity Pool credentials are invalid');
      console.log('   - Check if the Identity Pool exists in your AWS account');
      console.log('   - Verify the Identity Pool ID is correct');
    } else if (error.message?.includes('Failed to fetch') || error.message?.includes('fetch')) {
      console.log('   - This is likely a network connectivity issue');
      console.log('   - Check your internet connection');
      console.log('   - Verify the AWS region is correct');
      console.log('   - This could also be a CORS issue in browser environments');
    } else if (error.name === 'AccessDenied') {
      console.log('   - The Identity Pool doesn\'t have permissions to access S3');
      console.log('   - Check the IAM roles attached to the Identity Pool');
      console.log('   - Ensure the role has S3 permissions for the bucket');
    } else if (error.name === 'NoSuchBucket') {
      console.log('   - The S3 bucket doesn\'t exist in the specified region');
      console.log('   - Check the bucket name and region in AWS console');
    }
  }
}

// Run the test
testAWSConnection().catch(error => {
  console.error('💥 Unexpected error:', error);
});
