module.exports = {
  apps: [
    {
      name: 'icu-backend',
      script: 'server/server.js',
      cwd: '/Users/<USER>/Desktop/ICU dataset application 21.6.25',
      env: {
        NODE_ENV: 'production',
        PORT: 5000,
        // AWS credentials from .env will be loaded automatically
        AWS_ACCESS_KEY_ID: process.env.AWS_ACCESS_KEY_ID,
        AWS_SECRET_ACCESS_KEY: process.env.AWS_SECRET_ACCESS_KEY,
        AWS_REGION: process.env.AWS_REGION,
        AWS_S3_BUCKET: process.env.AWS_S3_BUCKET,
        REACT_APP_AWS_IDENTITY_POOL_ID: process.env.REACT_APP_AWS_IDENTITY_POOL_ID,
        REACT_APP_AWS_REGION: process.env.REACT_APP_AWS_REGION,
        REACT_APP_S3_BUCKET: process.env.REACT_APP_S3_BUCKET,
        ALLOWED_ORIGINS: process.env.ALLOWED_ORIGINS
      },
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: '1G',
      error_file: './logs/backend-error.log',
      out_file: './logs/backend-out.log',
      log_file: './logs/backend-combined.log',
      time: true,
      restart_delay: 1000,
      max_restarts: 10,
      min_uptime: '10s'
    },
    {
      name: 'icu-frontend',
      script: 'npm',
      args: 'start',
      cwd: '/Users/<USER>/Desktop/ICU dataset application 21.6.25',
      env: {
        NODE_ENV: 'development',
        PORT: 3000,
        BROWSER: 'none',  // Prevent browser auto-opening
        // Frontend environment variables
        REACT_APP_BACKEND_URL: 'http://localhost:5000',
        REACT_APP_AWS_IDENTITY_POOL_ID: process.env.REACT_APP_AWS_IDENTITY_POOL_ID,
        REACT_APP_AWS_REGION: process.env.REACT_APP_AWS_REGION,
        REACT_APP_S3_BUCKET: process.env.REACT_APP_S3_BUCKET,
        REACT_APP_DEBUG: process.env.REACT_APP_DEBUG
      },
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: '2G',
      error_file: './logs/frontend-error.log',
      out_file: './logs/frontend-out.log',
      log_file: './logs/frontend-combined.log',
      time: true,
      restart_delay: 2000,
      max_restarts: 5,
      min_uptime: '30s'
    }
  ]
};
