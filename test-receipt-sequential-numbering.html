<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Receipt Sequential Numbering Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .button {
            background-color: #009688;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .button:hover {
            background-color: #00796b;
        }
        .button.danger {
            background-color: #f44336;
        }
        .button.danger:hover {
            background-color: #d32f2f;
        }
        .output {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin-top: 10px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .success { color: #28a745; }
        .warning { color: #ffc107; }
        .error { color: #dc3545; }
        .info { color: #17a2b8; }
        h1 { color: #009688; }
        h2 { color: #00796b; }
        .receipt-number {
            font-size: 1.2em;
            font-weight: bold;
            color: #009688;
            background-color: #e0f2f1;
            padding: 5px 10px;
            border-radius: 3px;
            display: inline-block;
            margin: 2px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧾 Receipt Sequential Numbering Test</h1>
        <p><strong>Purpose:</strong> Test that receipt numbers are generated sequentially and persist across page refreshes.</p>
        
        <div class="test-section">
            <h2>📊 Current Receipt Counter Status</h2>
            <button class="button" onclick="checkReceiptCounter()">Check Current Counter</button>
            <div id="counter-status" class="output"></div>
        </div>

        <div class="test-section">
            <h2>🔢 Generate Sequential Receipt Numbers</h2>
            <p>Test sequential receipt number generation:</p>
            <button class="button" onclick="generateSingleReceipt()">Generate 1 Receipt</button>
            <button class="button" onclick="generateMultipleReceipts()">Generate 5 Receipts</button>
            <button class="button" onclick="generateTenReceipts()">Generate 10 Receipts</button>
            <div id="generation-output" class="output"></div>
        </div>

        <div class="test-section">
            <h2>🔄 Page Refresh Test</h2>
            <p><strong>Instructions:</strong></p>
            <ol>
                <li>Generate some receipt numbers above</li>
                <li>Note the current counter value</li>
                <li>Refresh this page (F5 or Ctrl+R)</li>
                <li>Check if the counter persisted and continues from where it left off</li>
            </ol>
            <button class="button" onclick="window.location.reload()">🔄 Refresh Page</button>
        </div>

        <div class="test-section">
            <h2>🧹 Reset Counter (Testing Only)</h2>
            <p>Reset the receipt counter to 0 for testing purposes:</p>
            <button class="button danger" onclick="resetReceiptCounter()">⚠️ Reset Counter to 0</button>
            <div id="reset-output" class="output"></div>
        </div>

        <div class="test-section">
            <h2>🚀 Open ICU Application</h2>
            <p>Test receipt generation in the actual application:</p>
            <button class="button" onclick="openApplication()">Open ICU Dataset Application</button>
        </div>
    </div>

    <script>
        function addOutput(elementId, message, type = 'info') {
            const output = document.getElementById(elementId);
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'success' ? 'success' : 
                            type === 'warning' ? 'warning' : 
                            type === 'error' ? 'error' : 'info';
            output.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            output.scrollTop = output.scrollHeight;
        }

        function clearOutput(elementId) {
            document.getElementById(elementId).innerHTML = '';
        }

        function checkReceiptCounter() {
            clearOutput('counter-status');
            addOutput('counter-status', '🔍 Checking receipt counter status...', 'info');
            
            try {
                const counter = localStorage.getItem('icuAppReceiptCounter');
                if (counter) {
                    addOutput('counter-status', `✅ Receipt counter found: ${counter}`, 'success');
                    addOutput('counter-status', `📋 Next receipt number will be: ${(parseInt(counter) + 1).toString().padStart(6, '0')}`, 'info');
                } else {
                    addOutput('counter-status', '📝 No receipt counter found - will start from 000001', 'warning');
                }
                
                // Check for any receipt mappings
                const mappingKeys = [];
                for (let i = 0; i < localStorage.length; i++) {
                    const key = localStorage.key(i);
                    if (key && key.startsWith('icuAppReceiptMapping_')) {
                        mappingKeys.push(key);
                    }
                }
                
                if (mappingKeys.length > 0) {
                    addOutput('counter-status', `🗂️ Found ${mappingKeys.length} receipt mappings`, 'info');
                } else {
                    addOutput('counter-status', '🗂️ No receipt mappings found', 'info');
                }
                
            } catch (error) {
                addOutput('counter-status', `❌ Error checking counter: ${error.message}`, 'error');
            }
        }

        function generateReceiptNumber() {
            try {
                const currentCounter = parseInt(localStorage.getItem('icuAppReceiptCounter') || '0', 10);
                const nextCounter = currentCounter + 1;
                localStorage.setItem('icuAppReceiptCounter', nextCounter.toString());
                return nextCounter.toString().padStart(6, '0');
            } catch (error) {
                console.error('Error generating receipt number:', error);
                return Date.now().toString().slice(-6);
            }
        }

        function generateSingleReceipt() {
            clearOutput('generation-output');
            addOutput('generation-output', '🧾 Generating single receipt number...', 'info');
            
            try {
                const receiptNumber = generateReceiptNumber();
                addOutput('generation-output', `✅ Generated receipt: ${receiptNumber}`, 'success');
                
                // Show in styled format
                const output = document.getElementById('generation-output');
                output.innerHTML += `<div style="margin: 10px 0;"><span class="receipt-number">${receiptNumber}</span></div>`;
                
            } catch (error) {
                addOutput('generation-output', `❌ Error: ${error.message}`, 'error');
            }
        }

        function generateMultipleReceipts() {
            clearOutput('generation-output');
            addOutput('generation-output', '🧾 Generating 5 sequential receipt numbers...', 'info');
            
            try {
                const receipts = [];
                for (let i = 0; i < 5; i++) {
                    const receiptNumber = generateReceiptNumber();
                    receipts.push(receiptNumber);
                    addOutput('generation-output', `  ${i + 1}. ${receiptNumber}`, 'success');
                }
                
                // Show in styled format
                const output = document.getElementById('generation-output');
                output.innerHTML += '<div style="margin: 10px 0;">Generated receipts: ';
                receipts.forEach(receipt => {
                    output.innerHTML += `<span class="receipt-number">${receipt}</span>`;
                });
                output.innerHTML += '</div>';
                
                addOutput('generation-output', '✅ All 5 receipts generated successfully', 'success');
                
            } catch (error) {
                addOutput('generation-output', `❌ Error: ${error.message}`, 'error');
            }
        }

        function generateTenReceipts() {
            clearOutput('generation-output');
            addOutput('generation-output', '🧾 Generating 10 sequential receipt numbers...', 'info');
            
            try {
                const receipts = [];
                for (let i = 0; i < 10; i++) {
                    const receiptNumber = generateReceiptNumber();
                    receipts.push(receiptNumber);
                    if (i < 5) {
                        addOutput('generation-output', `  ${i + 1}. ${receiptNumber}`, 'success');
                    } else if (i === 5) {
                        addOutput('generation-output', '  ... (showing first 5 and last 5)', 'info');
                    }
                    if (i >= 5) {
                        addOutput('generation-output', `  ${i + 1}. ${receiptNumber}`, 'success');
                    }
                }
                
                // Show in styled format
                const output = document.getElementById('generation-output');
                output.innerHTML += '<div style="margin: 10px 0;">All generated receipts: ';
                receipts.forEach(receipt => {
                    output.innerHTML += `<span class="receipt-number">${receipt}</span>`;
                });
                output.innerHTML += '</div>';
                
                addOutput('generation-output', '✅ All 10 receipts generated successfully', 'success');
                addOutput('generation-output', `📊 Counter now at: ${localStorage.getItem('icuAppReceiptCounter')}`, 'info');
                
            } catch (error) {
                addOutput('generation-output', `❌ Error: ${error.message}`, 'error');
            }
        }

        function resetReceiptCounter() {
            clearOutput('reset-output');
            addOutput('reset-output', '⚠️ Resetting receipt counter to 0...', 'warning');
            
            try {
                localStorage.setItem('icuAppReceiptCounter', '0');
                addOutput('reset-output', '✅ Receipt counter reset to 0', 'success');
                addOutput('reset-output', '📋 Next receipt number will be: 000001', 'info');
                
                // Also clear any receipt mappings for clean test
                const mappingKeys = [];
                for (let i = 0; i < localStorage.length; i++) {
                    const key = localStorage.key(i);
                    if (key && key.startsWith('icuAppReceiptMapping_')) {
                        mappingKeys.push(key);
                    }
                }
                
                mappingKeys.forEach(key => {
                    localStorage.removeItem(key);
                });
                
                if (mappingKeys.length > 0) {
                    addOutput('reset-output', `🗑️ Cleared ${mappingKeys.length} receipt mappings`, 'info');
                }
                
            } catch (error) {
                addOutput('reset-output', `❌ Error resetting counter: ${error.message}`, 'error');
            }
        }

        function openApplication() {
            window.open('http://localhost:3006', '_blank');
        }

        // Auto-check counter status on page load
        window.addEventListener('load', function() {
            setTimeout(checkReceiptCounter, 500);
        });
    </script>
</body>
</html>
