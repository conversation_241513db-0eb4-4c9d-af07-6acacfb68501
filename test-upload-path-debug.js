/**
 * Test Upload Path Generation Debug
 * 
 * This script tests the S3 upload path generation to verify that videos
 * are being uploaded to the correct demographic-based folder structure
 * and not to any folder named after reference numbers.
 */

console.log('🧪 === UPLOAD PATH GENERATION DEBUG TEST ===');
console.log('');

// Test the S3 key generation function (copied from awsStorage.js)
const sanitizeToAscii = (str) => {
  return str
    .normalize('NFD')
    .replace(/[\u0300-\u036f]/g, '')
    .replace(/[^\w\s-]/g, '')
    .replace(/\s+/g, '_')
    .toLowerCase();
};

const generateS3Key = (filename, ageGroup, gender, ethnicity, phrase) => {
  // Ensure all path components are properly sanitized
  const sanitizedPhrase = sanitizeToAscii(phrase);
  
  // Use normalized values from filename generation for consistency
  const validAgeGroups = ['18to39', '40to64', '65plus'];
  const validGenders = ['male', 'female', 'other'];
  const validEthnicities = ['caucasian', 'asian', 'african', 'hispanic', 'mixed', 'other', 'not_specified'];
  
  const normalizedAgeGroup = validAgeGroups.find(ag => ag === ageGroup?.toLowerCase()) || '40to64';
  const normalizedGender = validGenders.find(g => g === gender?.toLowerCase()) || 'female';
  const normalizedEthnicity = validEthnicities.find(e => e === ethnicity?.toLowerCase()) || 'not_specified';
  
  return `icu-videos/${normalizedAgeGroup}/${normalizedGender}/${normalizedEthnicity}/${sanitizedPhrase}/${filename}`;
};

// Test cases that simulate real user scenarios
const testCases = [
  {
    name: 'Basic test case',
    filename: 'hello__user01__25to40__female__caucasian__20241215_103000.webm',
    ageGroup: '25to40',
    gender: 'female', 
    ethnicity: 'caucasian',
    phrase: 'Hello'
  },
  {
    name: 'Test with complex phrase',
    filename: 'i_am_in_pain__user02__40to64__male__asian__20241215_103100.webm',
    ageGroup: '40to64',
    gender: 'male',
    ethnicity: 'asian', 
    phrase: 'I am in pain'
  },
  {
    name: 'Test with punctuation in phrase',
    filename: 'whats_wrong__user03__18to39__other__mixed__20241215_103200.webm',
    ageGroup: '18to39',
    gender: 'other',
    ethnicity: 'mixed',
    phrase: "What's wrong?"
  }
];

console.log('🔍 Testing S3 key generation...');
console.log('');

testCases.forEach((testCase, index) => {
  console.log(`Test ${index + 1}: ${testCase.name}`);
  console.log(`  Input phrase: "${testCase.phrase}"`);
  console.log(`  Demographics: ${testCase.ageGroup}, ${testCase.gender}, ${testCase.ethnicity}`);
  console.log(`  Filename: ${testCase.filename}`);
  
  const s3Key = generateS3Key(
    testCase.filename,
    testCase.ageGroup,
    testCase.gender,
    testCase.ethnicity,
    testCase.phrase
  );
  
  console.log(`  Generated S3 key: ${s3Key}`);
  
  // Verify the path structure
  const pathParts = s3Key.split('/');
  const isCorrectStructure = pathParts.length >= 5 && 
                            pathParts[0] === 'icu-videos' &&
                            !s3Key.includes('ICU-') &&
                            !s3Key.includes('Your Reference Number');
  
  console.log(`  ✅ Correct structure: ${isCorrectStructure}`);
  console.log(`  📁 Path parts: [${pathParts.join(', ')}]`);
  console.log('');
});

// Test for any reference number contamination
console.log('🔍 Testing for reference number contamination...');
console.log('');

const referenceNumbers = [
  'ICU-MD3D9V7B-4C5SYN',
  'ICU-ABC123DEF',
  'Your Reference Number ICU-MD3D9V7B-4C5SYN'
];

referenceNumbers.forEach((refNum, index) => {
  console.log(`Reference Number Test ${index + 1}: ${refNum}`);
  
  // Test if reference number could be used as a path component
  const testS3Key = generateS3Key(
    'test_file.webm',
    '25to40',
    'female',
    'caucasian',
    'Hello'
  );
  
  const containsRefNum = testS3Key.includes(refNum) || 
                        testS3Key.includes('ICU-') ||
                        testS3Key.includes('Your Reference Number');
  
  console.log(`  Generated key: ${testS3Key}`);
  console.log(`  ❌ Contains reference number: ${containsRefNum}`);
  console.log('');
});

console.log('🎯 Expected S3 path format:');
console.log('  icu-videos/[AGEGROUP]/[GENDER]/[ETHNICITY]/[PHRASE_LABEL]/[FILENAME].webm');
console.log('');
console.log('🚫 Should NEVER contain:');
console.log('  - ICU-[alphanumeric codes]');
console.log('  - "Your Reference Number"');
console.log('  - Complex reference numbers as folder names');
console.log('');

console.log('✅ Upload path generation test completed!');
