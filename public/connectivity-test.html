<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Backend Connectivity Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 { color: #2c3e50; text-align: center; }
        .test-result {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .success { background: #d5f4e6; color: #27ae60; border: 1px solid #27ae60; }
        .error { background: #fadbd8; color: #e74c3c; border: 1px solid #e74c3c; }
        .warning { background: #fef9e7; color: #f39c12; border: 1px solid #f39c12; }
        .info { background: #d6eaf8; color: #2980b9; border: 1px solid #2980b9; }
        button {
            background: #3498db;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #2980b9; }
        .critical { background: #e74c3c !important; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔗 Backend Connectivity Test</h1>
        <p><strong>Testing from:</strong> <span id="current-origin"></span></p>
        
        <div id="test-results"></div>
        
        <div style="text-align: center; margin: 20px 0;">
            <button onclick="testBackendConnectivity()" class="critical">🚨 Test Backend Connection</button>
            <button onclick="testCORS()">Test CORS</button>
            <button onclick="clearResults()">Clear Results</button>
        </div>
        
        <div style="margin-top: 30px;">
            <h2>📋 Manual Verification</h2>
            <p>If tests fail, manually check:</p>
            <ul>
                <li><a href="http://localhost:5000/health" target="_blank">Backend Health Check</a></li>
                <li><a href="http://localhost:3000" target="_blank">Frontend Application</a></li>
            </ul>
        </div>
    </div>

    <script>
        // Display current origin
        document.getElementById('current-origin').textContent = window.location.origin;

        function addResult(message, type = 'info') {
            const results = document.getElementById('test-results');
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = message;
            results.appendChild(div);
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function clearResults() {
            document.getElementById('test-results').innerHTML = '';
        }

        async function testBackendConnectivity() {
            clearResults();
            addResult('🔍 Testing backend connectivity from React app context...', 'info');
            addResult(`📍 Testing from origin: ${window.location.origin}`, 'info');

            try {
                // Test health endpoint
                addResult('🏥 Testing health endpoint...', 'info');
                addResult(`📤 Sending request from origin: ${window.location.origin}`, 'info');

                const healthResponse = await fetch('http://localhost:5000/health', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    credentials: 'include'
                });
                
                if (healthResponse.ok) {
                    const healthData = await healthResponse.json();
                    addResult(`✅ Backend Health: ${healthData.status} (uptime: ${Math.round(healthData.uptime)}s)`, 'success');
                    
                    // Check CORS headers
                    const corsHeader = healthResponse.headers.get('Access-Control-Allow-Origin');
                    const corsCredentials = healthResponse.headers.get('Access-Control-Allow-Credentials');

                    addResult('📋 CORS Headers Analysis:', 'info');
                    if (corsHeader) {
                        addResult(`✅ Access-Control-Allow-Origin: ${corsHeader}`, 'success');
                    } else {
                        addResult('⚠️ Access-Control-Allow-Origin: Not found', 'warning');
                    }

                    if (corsCredentials) {
                        addResult(`✅ Access-Control-Allow-Credentials: ${corsCredentials}`, 'success');
                    } else {
                        addResult('⚠️ Access-Control-Allow-Credentials: Not found', 'warning');
                    }

                    // List all headers for debugging
                    addResult('📝 All Response Headers:', 'info');
                    for (let [key, value] of healthResponse.headers.entries()) {
                        addResult(`  • ${key}: ${value}`, 'info');
                    }
                    
                } else {
                    addResult(`❌ Backend Health: HTTP ${healthResponse.status}`, 'error');
                }

                // Test sample counts endpoint
                addResult('📊 Testing sample counts endpoint...', 'info');
                const countsResponse = await fetch('http://localhost:5000/api/sample-counts');
                
                if (countsResponse.ok) {
                    const countsData = await countsResponse.json();
                    if (countsData.success) {
                        addResult(`✅ Sample Counts: ${countsData.totalRecordings} recordings found`, 'success');
                    } else {
                        addResult(`❌ Sample Counts: ${countsData.error}`, 'error');
                    }
                } else {
                    addResult(`❌ Sample Counts: HTTP ${countsResponse.status}`, 'error');
                }

                addResult('🎉 Backend connectivity test completed!', 'success');

            } catch (error) {
                addResult(`❌ Connection Error: ${error.message}`, 'error');
                addResult('💡 This indicates a network connectivity issue between frontend and backend', 'warning');
            }
        }

        async function testCORS() {
            addResult('🔍 Testing CORS configuration...', 'info');

            try {
                // Make a request with explicit headers
                const response = await fetch('http://localhost:5000/health', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Origin': window.location.origin
                    }
                });

                if (response.ok) {
                    addResult('✅ CORS: No CORS errors detected', 'success');
                    
                    // Check specific CORS headers
                    const allowOrigin = response.headers.get('Access-Control-Allow-Origin');
                    const allowCredentials = response.headers.get('Access-Control-Allow-Credentials');
                    
                    addResult(`📋 CORS Headers:`, 'info');
                    addResult(`  • Allow-Origin: ${allowOrigin || 'Not set'}`, 'info');
                    addResult(`  • Allow-Credentials: ${allowCredentials || 'Not set'}`, 'info');
                    
                } else {
                    addResult(`❌ CORS Test: HTTP ${response.status}`, 'error');
                }

            } catch (error) {
                if (error.message.includes('CORS')) {
                    addResult(`❌ CORS Error: ${error.message}`, 'error');
                } else {
                    addResult(`❌ Network Error: ${error.message}`, 'error');
                }
            }
        }

        // Auto-run test on page load
        window.addEventListener('load', function() {
            setTimeout(testBackendConnectivity, 1000);
        });
    </script>
</body>
</html>
