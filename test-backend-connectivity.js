// Quick backend connectivity test
// Run this in the browser console to verify backend is accessible

async function testBackendConnectivity() {
    console.log('🧪 Testing Backend Connectivity...');
    
    const tests = [
        {
            name: 'Health Check',
            url: 'http://localhost:5001/health',
            method: 'GET'
        },
        {
            name: 'CORS Preflight',
            url: 'http://localhost:5001/upload',
            method: 'OPTIONS'
        },
        {
            name: 'Sample Counts API',
            url: 'http://localhost:5001/api/sample-counts',
            method: 'GET'
        }
    ];

    for (const test of tests) {
        try {
            console.log(`\n🔍 Testing: ${test.name}`);
            console.log(`   URL: ${test.url}`);
            
            const response = await fetch(test.url, {
                method: test.method,
                headers: {
                    'Content-Type': 'application/json',
                }
            });
            
            console.log(`   ✅ Status: ${response.status} ${response.statusText}`);
            
            if (response.ok) {
                try {
                    const data = await response.json();
                    console.log(`   📄 Response:`, data);
                } catch (e) {
                    console.log(`   📄 Response: (non-JSON)`);
                }
            } else {
                console.log(`   ❌ Error: ${response.status} ${response.statusText}`);
            }
            
        } catch (error) {
            console.log(`   ❌ Network Error:`, error.message);
        }
    }
    
    console.log('\n🏁 Backend connectivity test completed');
}

// Test environment variables
function testEnvironmentConfig() {
    console.log('\n🔧 Environment Configuration:');
    console.log('   REACT_APP_BACKEND_URL:', process.env.REACT_APP_BACKEND_URL);
    console.log('   REACT_APP_AWS_REGION:', process.env.REACT_APP_AWS_REGION);
    console.log('   REACT_APP_S3_BUCKET:', process.env.REACT_APP_S3_BUCKET);
    console.log('   REACT_APP_AWS_IDENTITY_POOL_ID:', process.env.REACT_APP_AWS_IDENTITY_POOL_ID);
}

// Test localStorage data
function testLocalStorageData() {
    console.log('\n💾 LocalStorage Data:');
    console.log('   Selected phrases:', localStorage.getItem('icu_selected_phrases'));
    console.log('   Recording counts:', localStorage.getItem('icuAppRecordingsCount'));
    console.log('   Demographics:', localStorage.getItem('icu_demographics'));
}

// Run all tests
async function runAllTests() {
    console.clear();
    console.log('🚀 ICU Dataset Application - Backend Connectivity Test');
    console.log('=' .repeat(60));
    
    testEnvironmentConfig();
    testLocalStorageData();
    await testBackendConnectivity();
    
    console.log('\n📋 Next Steps:');
    console.log('1. If backend tests pass, proceed with recording tests');
    console.log('2. If backend tests fail, check server status and port configuration');
    console.log('3. Open Developer Tools Console and monitor during recording');
}

// Export for manual execution
window.testBackendConnectivity = testBackendConnectivity;
window.testEnvironmentConfig = testEnvironmentConfig;
window.testLocalStorageData = testLocalStorageData;
window.runAllTests = runAllTests;

console.log('🧪 Backend test functions loaded. Run runAllTests() to start testing.');
