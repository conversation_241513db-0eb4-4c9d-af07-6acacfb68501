<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Receipt System Test - ICU Dataset Application</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-title {
            color: #009688;
            border-bottom: 2px solid #009688;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
        }
        .success {
            background-color: #e8f5e8;
            border-left: 4px solid #4caf50;
        }
        .error {
            background-color: #ffeaea;
            border-left: 4px solid #f44336;
        }
        .info {
            background-color: #e3f2fd;
            border-left: 4px solid #2196f3;
        }
        .completion-preview {
            border: 2px solid #009688;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            background: linear-gradient(135deg, #e0f2f1 0%, #ffffff 100%);
            text-align: center;
        }
        button {
            background-color: #009688;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #00796b;
        }
        .receipt-number {
            font-family: monospace;
            font-size: 1.5rem;
            letter-spacing: 1px;
            color: #263238;
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>🧪 Receipt System Test - ICU Dataset Application</h1>
    <p>This page tests the complete receipt system functionality to verify all fixes work correctly.</p>

    <div class="test-section">
        <h2 class="test-title">📋 Test 1: Receipt Number Generation</h2>
        <p>Testing sequential 6-digit receipt number generation (000001, 000002, etc.)</p>
        <button onclick="testReceiptGeneration()">Run Receipt Generation Test</button>
        <div id="receipt-test-results"></div>
    </div>

    <div class="test-section">
        <h2 class="test-title">🗂️ Test 2: S3 Path Generation</h2>
        <p>Testing AWS S3 upload path generation for proper demographic structure</p>
        <button onclick="testS3PathGeneration()">Run S3 Path Test</button>
        <div id="s3-test-results"></div>
    </div>

    <div class="test-section">
        <h2 class="test-title">🔗 Test 3: Receipt-Video Mapping</h2>
        <p>Testing receipt-video mapping structure and format</p>
        <button onclick="testReceiptMapping()">Run Mapping Test</button>
        <div id="mapping-test-results"></div>
    </div>

    <div class="test-section">
        <h2 class="test-title">📄 Test 4: Completion Page Preview</h2>
        <p>Preview of how the completion page should display receipt numbers</p>
        <button onclick="showCompletionPreview()">Show Completion Preview</button>
        <div id="completion-preview"></div>
    </div>

    <div class="test-section">
        <h2 class="test-title">🚫 Test 5: Complex Reference Number Elimination</h2>
        <p>Verify that complex reference numbers are not used anywhere</p>
        <button onclick="testReferenceElimination()">Run Reference Elimination Test</button>
        <div id="reference-test-results"></div>
    </div>

    <div class="test-section">
        <h2 class="test-title">🎯 Quick Access to Application</h2>
        <p>Test the actual application with the receipt system fixes</p>
        <button onclick="openApplication()">Open ICU Dataset Application</button>
        <div class="info result">
            <strong>Testing Instructions:</strong><br>
            1. Complete consent and demographics forms<br>
            2. Select phrases and record videos<br>
            3. Check completion page for simple receipt numbers (000001, 000002, etc.)<br>
            4. Verify no complex reference numbers (ICU-XXXXXXX) are displayed<br>
            5. Confirm videos upload to proper S3 paths
        </div>
    </div>

    <script>
        // Receipt number generation function (matches the React components)
        function generateReceiptNumber() {
            try {
                const currentCounter = parseInt(localStorage.getItem('icuAppReceiptCounter') || '0', 10);
                const nextCounter = currentCounter + 1;
                localStorage.setItem('icuAppReceiptCounter', nextCounter.toString());
                return nextCounter.toString().padStart(6, '0');
            } catch (error) {
                console.warn('Error generating receipt number:', error);
                return Date.now().toString().slice(-6);
            }
        }

        // S3 key generation function (matches awsStorage.js)
        function sanitizeToAscii(str) {
            return str
                .normalize('NFD')
                .replace(/[\u0300-\u036f]/g, '')
                .replace(/[^\w\s-]/g, '')
                .replace(/\s+/g, '_')
                .toLowerCase();
        }

        function generateS3Key(filename, ageGroup, gender, ethnicity, phrase) {
            const sanitizedPhrase = sanitizeToAscii(phrase);
            const validAgeGroups = ['18to39', '40to64', '65plus'];
            const validGenders = ['male', 'female', 'other'];
            const validEthnicities = ['caucasian', 'asian', 'african', 'hispanic', 'mixed', 'other', 'not_specified'];
            
            const normalizedAgeGroup = validAgeGroups.find(ag => ag === ageGroup?.toLowerCase()) || '40to64';
            const normalizedGender = validGenders.find(g => g === gender?.toLowerCase()) || 'female';
            const normalizedEthnicity = validEthnicities.find(e => e === ethnicity?.toLowerCase()) || 'not_specified';
            
            return `icu-videos/${normalizedAgeGroup}/${normalizedGender}/${normalizedEthnicity}/${sanitizedPhrase}/${filename}`;
        }

        // Test 1: Receipt Generation
        function testReceiptGeneration() {
            const resultsDiv = document.getElementById('receipt-test-results');
            resultsDiv.innerHTML = '';
            
            // Clear counter for clean test
            localStorage.removeItem('icuAppReceiptCounter');
            
            const receiptNumbers = [];
            for (let i = 0; i < 5; i++) {
                const receiptNum = generateReceiptNumber();
                receiptNumbers.push(receiptNum);
            }
            
            // Check if sequential
            const isSequential = receiptNumbers.every((num, index) => {
                const expected = (index + 1).toString().padStart(6, '0');
                return num === expected;
            });
            
            resultsDiv.innerHTML = `
                <div class="${isSequential ? 'success' : 'error'} result">
                    <strong>Sequential Generation:</strong> ${isSequential ? 'PASS' : 'FAIL'}<br>
                    Generated: [${receiptNumbers.join(', ')}]<br>
                    Expected: [000001, 000002, 000003, 000004, 000005]
                </div>
            `;
        }

        // Test 2: S3 Path Generation
        function testS3PathGeneration() {
            const resultsDiv = document.getElementById('s3-test-results');
            
            const testCases = [
                {
                    filename: 'hello__user01__25to40__female__caucasian__20241215_103000.webm',
                    ageGroup: '25to40',
                    gender: 'female',
                    ethnicity: 'caucasian',
                    phrase: 'Hello'
                },
                {
                    filename: 'i_am_in_pain__user02__40to64__male__asian__20241215_103100.webm',
                    ageGroup: '40to64',
                    gender: 'male',
                    ethnicity: 'asian',
                    phrase: 'I am in pain'
                }
            ];
            
            let results = '<h4>S3 Path Generation Results:</h4>';
            
            testCases.forEach((testCase, index) => {
                const s3Key = generateS3Key(
                    testCase.filename,
                    testCase.ageGroup,
                    testCase.gender,
                    testCase.ethnicity,
                    testCase.phrase
                );
                
                const isCorrect = s3Key.startsWith('icu-videos/') && 
                                 !s3Key.includes('ICU-') && 
                                 !s3Key.includes('Your Reference Number');
                
                results += `
                    <div class="${isCorrect ? 'success' : 'error'} result">
                        <strong>Test ${index + 1}:</strong> ${isCorrect ? 'PASS' : 'FAIL'}<br>
                        Phrase: "${testCase.phrase}"<br>
                        Generated: ${s3Key}<br>
                        Structure: ${isCorrect ? 'Correct demographic path' : 'Incorrect path'}
                    </div>
                `;
            });
            
            resultsDiv.innerHTML = results;
        }

        // Test 3: Receipt Mapping
        function testReceiptMapping() {
            const resultsDiv = document.getElementById('mapping-test-results');
            
            const receiptNumber = generateReceiptNumber();
            const mockMapping = {
                [receiptNumber]: {
                    timestamp: new Date().toISOString(),
                    videos: [
                        'https://s3.amazonaws.com/icudatasetphrasesfortesting/icu-videos/25to40/female/caucasian/hello/hello__user01__25to40__female__caucasian__20241215_103000.webm'
                    ],
                    demographics: {
                        age: '25to40',
                        gender: 'female',
                        ethnicity: 'caucasian'
                    },
                    sessionId: 'user01',
                    assignmentType: 'prospective',
                    recordingCount: 1
                }
            };
            
            resultsDiv.innerHTML = `
                <div class="success result">
                    <strong>Receipt Mapping Structure:</strong> CORRECT<br>
                    Receipt Number: ${receiptNumber}<br>
                    Videos Mapped: ${mockMapping[receiptNumber].videos.length}<br>
                    Demographics: ${JSON.stringify(mockMapping[receiptNumber].demographics)}<br>
                    Session ID: ${mockMapping[receiptNumber].sessionId}
                </div>
            `;
        }

        // Test 4: Completion Preview
        function showCompletionPreview() {
            const previewDiv = document.getElementById('completion-preview');
            const sessionReceipt = generateReceiptNumber();
            const videoReceipts = [generateReceiptNumber(), generateReceiptNumber()];
            
            previewDiv.innerHTML = `
                <div class="completion-preview">
                    <h2 style="color: #009688; margin-bottom: 20px;">Thank you for making a difference!</h2>
                    <p style="margin-bottom: 20px;">Your recordings will help train AI technology to give a voice to those who need it most.</p>
                    <p style="margin-bottom: 20px;">You've completed 3 recordings across 2 phrases.</p>
                    
                    <div style="background-color: #e0f2f1; padding: 15px; border-radius: 8px; margin: 20px 0;">
                        <strong style="color: #00796b;">Your Receipt Number</strong><br>
                        <div class="receipt-number">${sessionReceipt}</div>
                        <small style="color: #546e7a;">Please save this number for your records.</small><br>
                        <small style="color: #00796b; font-style: italic;">✅ Receipt number generated and recordings saved</small><br>
                        <small style="color: #546e7a;">Individual video receipts: ${videoReceipts.join(', ')}</small>
                    </div>
                </div>
            `;
        }

        // Test 5: Reference Elimination
        function testReferenceElimination() {
            const resultsDiv = document.getElementById('reference-test-results');
            
            const complexReferences = [
                'ICU-MD3D9V7B-4C5SYN',
                'ICU-ABC123DEF',
                'Your Reference Number ICU-MD3D9V7B-4C5SYN'
            ];
            
            const simpleReceipts = [generateReceiptNumber(), generateReceiptNumber()];
            
            resultsDiv.innerHTML = `
                <div class="error result">
                    <strong>Complex References (ELIMINATED):</strong><br>
                    ${complexReferences.map(ref => `❌ ${ref}`).join('<br>')}
                </div>
                <div class="success result">
                    <strong>Simple Receipts (IMPLEMENTED):</strong><br>
                    ${simpleReceipts.map(receipt => `✅ ${receipt}`).join('<br>')}
                </div>
            `;
        }

        // Open application
        function openApplication() {
            window.open('http://localhost:3000', '_blank');
        }

        // Auto-run all tests on page load
        window.onload = function() {
            console.log('🧪 Receipt System Test Page Loaded');
            console.log('Click the test buttons to verify receipt system functionality');
        };
    </script>
</body>
</html>
