// Verification script for ICU core words implementation
const { 
  PHRASE_CATEGORIES, 
  getAllPhrases, 
  getCategoryNames, 
  getPhrasesByCategory 
} = require('./src/config/phraseCategories.js');

console.log('🏥 ICU Dataset Application - Implementation Verification\n');

// 1. Verify all categories exist
console.log('📋 Available Categories:');
const categories = getCategoryNames();
categories.forEach((category, index) => {
  const count = getPhrasesByCategory(category).length;
  console.log(`${index + 1}. ${category} (${count} items)`);
});

// 2. Verify ICU core words
console.log('\n🔤 ICU Core Words Category:');
const icuWords = getPhrasesByCategory('ICU core words');
console.log(`Total words: ${icuWords.length}`);
console.log('Words:', icuWords.join(', '));

// 3. Verify required words are present
console.log('\n✅ Required Words Verification:');
const requiredWords = [
  'doctor', 'nurse', 'help', 'pain', 'water', 'drink', 'food', 'toilet',
  'move', 'sit', 'lie', 'rest', 'blanket', 'pillow', 'glasses', 'hearing aids',
  'phone', 'charger', 'music', 'news', 'TV', 'lights', 'family', 'wife',
  'husband', 'son', 'daughter', 'question', 'medication', 'cough', 'suction'
];

const missing = requiredWords.filter(word => !icuWords.includes(word));
const extra = icuWords.filter(word => !requiredWords.includes(word));

if (missing.length === 0) {
  console.log('✓ All 31 required words are present');
} else {
  console.log(`❌ Missing words: ${missing.join(', ')}`);
}

if (extra.length === 0) {
  console.log('✓ No extra words found');
} else {
  console.log(`ℹ️ Extra words: ${extra.join(', ')}`);
}

// 4. Verify no exact duplicates
console.log('\n🔍 Duplicate Check:');
const allPhrases = getAllPhrases();
const phraseTexts = allPhrases.map(p => p.text.toLowerCase());
const uniquePhrases = [...new Set(phraseTexts)];

if (phraseTexts.length === uniquePhrases.length) {
  console.log('✓ No exact duplicates found across all categories');
} else {
  console.log(`❌ Found ${phraseTexts.length - uniquePhrases.length} duplicates`);
}

// 5. Show total statistics
console.log('\n📊 Statistics:');
console.log(`Total categories: ${categories.length}`);
console.log(`Total phrases/words: ${allPhrases.length}`);
console.log(`ICU core words: ${icuWords.length}`);

// 6. Show category breakdown
console.log('\n📈 Category Breakdown:');
categories.forEach(category => {
  const phrases = getPhrasesByCategory(category);
  const type = category === 'ICU core words' ? 'words' : 'phrases';
  console.log(`- ${category}: ${phrases.length} ${type}`);
});

console.log('\n🎉 Implementation verification complete!');
console.log('The ICU core words category has been successfully integrated.');
