<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ICU Dataset Application - Access Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .test-link {
            display: inline-block;
            padding: 10px 20px;
            background-color: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            margin: 5px;
        }
        .test-link:hover {
            background-color: #0056b3;
        }
        #results {
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🏥 ICU Dataset Application - Access Test</h1>
        <p>This page helps verify that the ICU dataset application is accessible and functioning properly.</p>
        
        <h2>📋 Test Checklist</h2>
        <div id="results">
            <div class="status warning">⏳ Testing application access...</div>
        </div>
        
        <h2>🔗 Access Links</h2>
        <p>Try accessing the application through these different methods:</p>
        
        <div>
            <a href="http://localhost:3000" class="test-link" target="_blank">
                🌐 localhost:3000
            </a>
            <a href="http://127.0.0.1:3000" class="test-link" target="_blank">
                🌐 127.0.0.1:3000
            </a>
            <a href="http://*************:3000" class="test-link" target="_blank">
                🌐 Network IP (*************:3000)
            </a>
        </div>
        
        <h2>🔧 Backend Health Check</h2>
        <div>
            <a href="http://localhost:5000/health" class="test-link" target="_blank">
                ⚕️ Backend Health Status
            </a>
        </div>
        
        <h2>📝 What to Check</h2>
        <ul>
            <li><strong>Consent Page:</strong> Should display the ICU dataset consent form with images</li>
            <li><strong>No JavaScript Errors:</strong> Open browser DevTools (F12) and check Console tab</li>
            <li><strong>Network Requests:</strong> Check Network tab for failed requests or CORS errors</li>
            <li><strong>Page Loading:</strong> Application should load completely, not show blank page or infinite loading</li>
        </ul>
        
        <h2>🚨 Troubleshooting</h2>
        <div class="status warning">
            <strong>If the application is not loading:</strong>
            <ol>
                <li>Check that both servers are running (React on port 3000, Backend on port 5000)</li>
                <li>Clear browser cache and try incognito/private browsing mode</li>
                <li>Check browser console for JavaScript errors</li>
                <li>Verify no other applications are using ports 3000 or 5000</li>
                <li>Try alternative access methods (127.0.0.1 instead of localhost)</li>
            </ol>
        </div>
    </div>

    <script>
        // Simple connectivity test
        async function testConnectivity() {
            const results = document.getElementById('results');
            results.innerHTML = '';
            
            // Test frontend
            try {
                const frontendResponse = await fetch('http://localhost:3000');
                if (frontendResponse.ok) {
                    results.innerHTML += '<div class="status success">✅ Frontend server (localhost:3000) is accessible</div>';
                } else {
                    results.innerHTML += '<div class="status error">❌ Frontend server returned error: ' + frontendResponse.status + '</div>';
                }
            } catch (error) {
                results.innerHTML += '<div class="status error">❌ Frontend server (localhost:3000) is not accessible: ' + error.message + '</div>';
            }
            
            // Test backend
            try {
                const backendResponse = await fetch('http://localhost:5000/health');
                if (backendResponse.ok) {
                    const healthData = await backendResponse.json();
                    results.innerHTML += '<div class="status success">✅ Backend server (localhost:5000) is healthy</div>';
                    results.innerHTML += '<div class="status success">📊 Backend status: ' + healthData.status + ' (uptime: ' + Math.round(healthData.uptime) + 's)</div>';
                } else {
                    results.innerHTML += '<div class="status error">❌ Backend server returned error: ' + backendResponse.status + '</div>';
                }
            } catch (error) {
                results.innerHTML += '<div class="status error">❌ Backend server (localhost:5000) is not accessible: ' + error.message + '</div>';
            }
        }
        
        // Run tests when page loads
        window.addEventListener('load', testConnectivity);
    </script>
</body>
</html>
