<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EC2 Server Endpoint Testing</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .button {
            background-color: #009688;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .button:hover {
            background-color: #00796b;
        }
        .button.danger {
            background-color: #f44336;
        }
        .button.danger:hover {
            background-color: #d32f2f;
        }
        .output {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin-top: 10px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .success { color: #28a745; }
        .warning { color: #ffc107; }
        .error { color: #dc3545; }
        .info { color: #17a2b8; }
        h1 { color: #009688; }
        h2 { color: #00796b; }
        .status-box {
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .status-online {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .status-offline {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 EC2 Server Endpoint Testing</h1>
        <p><strong>Server IP:</strong> *************:5000</p>
        <p><strong>Purpose:</strong> Test connectivity and functionality of all backend endpoints.</p>
        
        <div class="test-section">
            <h2>🔍 Server Status Check</h2>
            <button class="button" onclick="testServerStatus()">Check Server Status</button>
            <div id="status-output" class="output"></div>
        </div>

        <div class="test-section">
            <h2>🏥 Health Check Endpoint</h2>
            <button class="button" onclick="testHealthEndpoint()">Test /health</button>
            <div id="health-output" class="output"></div>
        </div>

        <div class="test-section">
            <h2>📊 API Endpoints</h2>
            <button class="button" onclick="testSampleCounts()">Test /api/sample-counts</button>
            <button class="button" onclick="testMetrics()">Test /api/metrics</button>
            <button class="button" onclick="testRecordings()">Test /api/recordings</button>
            <div id="api-output" class="output"></div>
        </div>

        <div class="test-section">
            <h2>🧾 Receipt System Endpoints</h2>
            <button class="button" onclick="testReceiptGeneration()">Test /api/receipt/generate</button>
            <button class="button" onclick="testReceiptCounter()">Test /api/receipt/counter</button>
            <div id="receipt-output" class="output"></div>
        </div>

        <div class="test-section">
            <h2>📤 Upload Endpoint</h2>
            <button class="button" onclick="testUploadEndpoint()">Test /upload (Mock)</button>
            <div id="upload-output" class="output"></div>
        </div>

        <div class="test-section">
            <h2>🌐 CORS Configuration</h2>
            <button class="button" onclick="testCORS()">Test CORS Headers</button>
            <div id="cors-output" class="output"></div>
        </div>
    </div>

    <script>
        const SERVER_URL = 'http://*************:5000';

        function addOutput(elementId, message, type = 'info') {
            const output = document.getElementById(elementId);
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'success' ? 'success' : 
                            type === 'warning' ? 'warning' : 
                            type === 'error' ? 'error' : 'info';
            output.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            output.scrollTop = output.scrollHeight;
        }

        function clearOutput(elementId) {
            document.getElementById(elementId).innerHTML = '';
        }

        async function testServerStatus() {
            clearOutput('status-output');
            addOutput('status-output', '🔍 Testing server connectivity...', 'info');
            
            try {
                const response = await fetch(`${SERVER_URL}/health`, {
                    method: 'GET',
                    mode: 'cors'
                });
                
                if (response.ok) {
                    addOutput('status-output', '✅ Server is ONLINE and responding', 'success');
                    const data = await response.json();
                    addOutput('status-output', `📊 Server Status: ${JSON.stringify(data, null, 2)}`, 'info');
                } else {
                    addOutput('status-output', `⚠️ Server responded with status: ${response.status}`, 'warning');
                }
            } catch (error) {
                addOutput('status-output', `❌ Server is OFFLINE or unreachable: ${error.message}`, 'error');
                addOutput('status-output', '💡 Check if EC2 security group allows port 5000', 'warning');
            }
        }

        async function testHealthEndpoint() {
            clearOutput('health-output');
            addOutput('health-output', '🏥 Testing health endpoint...', 'info');
            
            try {
                const response = await fetch(`${SERVER_URL}/health`);
                const data = await response.json();
                
                addOutput('health-output', '✅ Health endpoint working', 'success');
                addOutput('health-output', `📋 Health Data:\n${JSON.stringify(data, null, 2)}`, 'info');
                
                // Check specific health indicators
                if (data.services.aws === 'operational') {
                    addOutput('health-output', '✅ AWS services configured', 'success');
                } else {
                    addOutput('health-output', '⚠️ AWS services not configured', 'warning');
                }
                
            } catch (error) {
                addOutput('health-output', `❌ Health check failed: ${error.message}`, 'error');
            }
        }

        async function testSampleCounts() {
            clearOutput('api-output');
            addOutput('api-output', '📊 Testing sample counts API...', 'info');
            
            try {
                const response = await fetch(`${SERVER_URL}/api/sample-counts`);
                const data = await response.json();
                
                addOutput('api-output', '✅ Sample counts API working', 'success');
                addOutput('api-output', `📈 Sample Data:\n${JSON.stringify(data, null, 2)}`, 'info');
                
            } catch (error) {
                addOutput('api-output', `❌ Sample counts API failed: ${error.message}`, 'error');
            }
        }

        async function testMetrics() {
            addOutput('api-output', '\n📊 Testing metrics API...', 'info');
            
            try {
                const response = await fetch(`${SERVER_URL}/api/metrics`);
                const data = await response.json();
                
                addOutput('api-output', '✅ Metrics API working', 'success');
                addOutput('api-output', `📈 Metrics Data:\n${JSON.stringify(data, null, 2)}`, 'info');
                
            } catch (error) {
                addOutput('api-output', `❌ Metrics API failed: ${error.message}`, 'error');
            }
        }

        async function testRecordings() {
            addOutput('api-output', '\n📊 Testing recordings API...', 'info');
            
            try {
                const response = await fetch(`${SERVER_URL}/api/recordings`);
                const data = await response.json();
                
                addOutput('api-output', '✅ Recordings API working', 'success');
                addOutput('api-output', `📹 Recordings Data:\n${JSON.stringify(data, null, 2)}`, 'info');
                
            } catch (error) {
                addOutput('api-output', `❌ Recordings API failed: ${error.message}`, 'error');
            }
        }

        async function testReceiptGeneration() {
            clearOutput('receipt-output');
            addOutput('receipt-output', '🧾 Testing receipt generation...', 'info');
            
            try {
                const response = await fetch(`${SERVER_URL}/api/receipt/generate`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                const data = await response.json();
                
                addOutput('receipt-output', '✅ Receipt generation working', 'success');
                addOutput('receipt-output', `🧾 Receipt Data:\n${JSON.stringify(data, null, 2)}`, 'info');
                
            } catch (error) {
                addOutput('receipt-output', `❌ Receipt generation failed: ${error.message}`, 'error');
            }
        }

        async function testReceiptCounter() {
            addOutput('receipt-output', '\n🔢 Testing receipt counter...', 'info');
            
            try {
                const response = await fetch(`${SERVER_URL}/api/receipt/counter`);
                const data = await response.json();
                
                addOutput('receipt-output', '✅ Receipt counter working', 'success');
                addOutput('receipt-output', `🔢 Counter Data:\n${JSON.stringify(data, null, 2)}`, 'info');
                
            } catch (error) {
                addOutput('receipt-output', `❌ Receipt counter failed: ${error.message}`, 'error');
            }
        }

        async function testUploadEndpoint() {
            clearOutput('upload-output');
            addOutput('upload-output', '📤 Testing upload endpoint (mock data)...', 'info');
            
            try {
                // Create a mock FormData for testing
                const formData = new FormData();
                const mockBlob = new Blob(['mock video data'], { type: 'video/mp4' });
                formData.append('video', mockBlob, 'test-video.mp4');
                formData.append('phrase', 'test phrase');
                formData.append('category', 'test category');
                formData.append('demographics', JSON.stringify({
                    age: '25-34',
                    gender: 'Test',
                    ethnicity: 'Test'
                }));
                
                const response = await fetch(`${SERVER_URL}/upload`, {
                    method: 'POST',
                    body: formData
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    addOutput('upload-output', '✅ Upload endpoint working', 'success');
                    addOutput('upload-output', `📤 Upload Response:\n${JSON.stringify(data, null, 2)}`, 'info');
                } else {
                    addOutput('upload-output', `⚠️ Upload endpoint responded with status: ${response.status}`, 'warning');
                    addOutput('upload-output', `📤 Response:\n${JSON.stringify(data, null, 2)}`, 'info');
                }
                
            } catch (error) {
                addOutput('upload-output', `❌ Upload test failed: ${error.message}`, 'error');
            }
        }

        async function testCORS() {
            clearOutput('cors-output');
            addOutput('cors-output', '🌐 Testing CORS configuration...', 'info');
            
            try {
                const response = await fetch(`${SERVER_URL}/health`, {
                    method: 'OPTIONS'
                });
                
                addOutput('cors-output', '✅ CORS preflight working', 'success');
                addOutput('cors-output', `🌐 CORS Headers:`, 'info');
                
                response.headers.forEach((value, key) => {
                    if (key.toLowerCase().includes('access-control')) {
                        addOutput('cors-output', `  ${key}: ${value}`, 'info');
                    }
                });
                
            } catch (error) {
                addOutput('cors-output', `❌ CORS test failed: ${error.message}`, 'error');
            }
        }

        // Auto-test server status on page load
        window.addEventListener('load', function() {
            setTimeout(testServerStatus, 1000);
        });
    </script>
</body>
</html>
