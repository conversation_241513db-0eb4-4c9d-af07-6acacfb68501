<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Receipt System Test - ICU Dataset Application</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #00796b;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #fafafa;
        }
        .test-section h3 {
            color: #333;
            margin-top: 0;
        }
        button {
            background-color: #00796b;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #005a4f;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 3px;
            font-family: monospace;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .receipt-display {
            font-size: 24px;
            font-weight: bold;
            text-align: center;
            padding: 20px;
            background-color: #e8f5e8;
            border: 2px solid #00796b;
            border-radius: 10px;
            margin: 10px 0;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .status.online {
            background-color: #d4edda;
            color: #155724;
        }
        .status.offline {
            background-color: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧾 Receipt System Test</h1>
        <p><strong>Purpose:</strong> Test the receipt generation functionality and AWS S3 'receipt-numbers' bucket integration.</p>
        
        <div class="test-section">
            <h3>1. Backend Connectivity Test</h3>
            <button onclick="testBackendConnectivity()">Test Backend Connection</button>
            <div id="backend-status"></div>
        </div>

        <div class="test-section">
            <h3>2. Receipt Counter Test</h3>
            <button onclick="getCurrentCounter()">Get Current Counter</button>
            <button onclick="generateReceipt()">Generate Receipt Number</button>
            <div id="counter-results"></div>
        </div>

        <div class="test-section">
            <h3>3. Receipt Generation Test</h3>
            <button onclick="generateMultipleReceipts()">Generate 5 Receipt Numbers</button>
            <div id="generation-results"></div>
        </div>

        <div class="test-section">
            <h3>4. localStorage Fallback Test</h3>
            <button onclick="testLocalStorageFallback()">Test localStorage Fallback</button>
            <div id="fallback-results"></div>
        </div>

        <div class="test-section">
            <h3>5. Current Receipt Display</h3>
            <div id="current-receipt" class="receipt-display">No receipt generated yet</div>
            <button onclick="copyReceipt()">Copy Receipt Number</button>
        </div>
    </div>

    <script>
        const BACKEND_URL = 'http://localhost:5000';
        let currentReceipt = null;

        function log(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
            element.appendChild(div);
            element.scrollTop = element.scrollHeight;
        }

        async function testBackendConnectivity() {
            log('backend-status', '🔄 Testing backend connectivity...', 'info');
            
            try {
                const response = await fetch(`${BACKEND_URL}/`);
                const data = await response.json();
                
                if (data.endpoints && data.endpoints.receiptGenerate) {
                    log('backend-status', '✅ Backend connected successfully!', 'success');
                    log('backend-status', `📋 Available endpoints: ${Object.keys(data.endpoints).join(', ')}`, 'info');
                } else {
                    log('backend-status', '⚠️ Backend connected but receipt endpoints not found', 'error');
                }
            } catch (error) {
                log('backend-status', `❌ Backend connection failed: ${error.message}`, 'error');
            }
        }

        async function getCurrentCounter() {
            log('counter-results', '🔄 Fetching current receipt counter...', 'info');
            
            try {
                const response = await fetch(`${BACKEND_URL}/api/receipt/counter`);
                const data = await response.json();
                
                if (data.success) {
                    log('counter-results', `✅ Current counter: ${data.counter}`, 'success');
                    if (data.isNew) {
                        log('counter-results', '📝 Counter file created (starting from 0)', 'info');
                    }
                } else {
                    log('counter-results', `❌ Error: ${data.error}`, 'error');
                }
            } catch (error) {
                log('counter-results', `❌ Request failed: ${error.message}`, 'error');
            }
        }

        async function generateReceipt() {
            log('counter-results', '🧾 Generating receipt number...', 'info');
            
            try {
                const response = await fetch(`${BACKEND_URL}/api/receipt/generate`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                const data = await response.json();
                
                if (data.success) {
                    currentReceipt = data.receiptNumber;
                    document.getElementById('current-receipt').textContent = data.receiptNumber;
                    log('counter-results', `✅ Receipt generated: ${data.receiptNumber} (Counter: ${data.counter})`, 'success');
                } else {
                    log('counter-results', `❌ Generation failed: ${data.error}`, 'error');
                    log('counter-results', '🔄 Falling back to localStorage...', 'info');
                    
                    // Test localStorage fallback
                    const fallbackReceipt = generateLocalStorageReceipt();
                    currentReceipt = fallbackReceipt;
                    document.getElementById('current-receipt').textContent = fallbackReceipt;
                    log('counter-results', `📱 localStorage receipt: ${fallbackReceipt}`, 'success');
                }
            } catch (error) {
                log('counter-results', `❌ Request failed: ${error.message}`, 'error');
            }
        }

        async function generateMultipleReceipts() {
            log('generation-results', '🔄 Generating 5 receipt numbers...', 'info');
            
            const receipts = [];
            for (let i = 1; i <= 5; i++) {
                try {
                    const response = await fetch(`${BACKEND_URL}/api/receipt/generate`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        }
                    });
                    const data = await response.json();
                    
                    if (data.success) {
                        receipts.push(data.receiptNumber);
                        log('generation-results', `${i}. Receipt: ${data.receiptNumber}`, 'success');
                    } else {
                        log('generation-results', `${i}. Failed: ${data.error}`, 'error');
                        // Fallback to localStorage
                        const fallback = generateLocalStorageReceipt();
                        receipts.push(fallback);
                        log('generation-results', `${i}. Fallback: ${fallback}`, 'info');
                    }
                } catch (error) {
                    log('generation-results', `${i}. Error: ${error.message}`, 'error');
                }
                
                // Small delay between requests
                await new Promise(resolve => setTimeout(resolve, 100));
            }
            
            log('generation-results', `📋 Generated receipts: ${receipts.join(', ')}`, 'info');
            
            // Update current receipt to the last one
            if (receipts.length > 0) {
                currentReceipt = receipts[receipts.length - 1];
                document.getElementById('current-receipt').textContent = currentReceipt;
            }
        }

        function testLocalStorageFallback() {
            log('fallback-results', '📱 Testing localStorage fallback system...', 'info');
            
            try {
                // Clear localStorage counter for testing
                const originalCounter = localStorage.getItem('icuAppReceiptCounter');
                log('fallback-results', `📋 Original counter: ${originalCounter || '0'}`, 'info');
                
                // Generate several receipts using localStorage
                const receipts = [];
                for (let i = 1; i <= 3; i++) {
                    const receipt = generateLocalStorageReceipt();
                    receipts.push(receipt);
                    log('fallback-results', `${i}. localStorage receipt: ${receipt}`, 'success');
                }
                
                log('fallback-results', `✅ localStorage fallback working! Generated: ${receipts.join(', ')}`, 'success');
                
                // Update current receipt
                currentReceipt = receipts[receipts.length - 1];
                document.getElementById('current-receipt').textContent = currentReceipt;
                
            } catch (error) {
                log('fallback-results', `❌ localStorage fallback failed: ${error.message}`, 'error');
            }
        }

        function generateLocalStorageReceipt() {
            try {
                const currentCounter = parseInt(localStorage.getItem('icuAppReceiptCounter') || '0', 10);
                const nextCounter = currentCounter + 1;
                localStorage.setItem('icuAppReceiptCounter', nextCounter.toString());
                return nextCounter.toString().padStart(6, '0');
            } catch (error) {
                // Final fallback to timestamp
                return Date.now().toString().slice(-6);
            }
        }

        function copyReceipt() {
            if (currentReceipt) {
                navigator.clipboard.writeText(currentReceipt).then(() => {
                    alert(`Receipt number ${currentReceipt} copied to clipboard!`);
                }).catch(err => {
                    alert(`Failed to copy: ${err.message}`);
                });
            } else {
                alert('No receipt number to copy. Generate one first!');
            }
        }

        // Auto-test backend connectivity on page load
        window.addEventListener('load', () => {
            testBackendConnectivity();
        });
    </script>
</body>
</html>
