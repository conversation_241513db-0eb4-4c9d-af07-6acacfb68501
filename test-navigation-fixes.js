/**
 * Navigation and MediaPipe Fixes Test Script
 * Tests the specific fixes implemented for the critical navigation error
 */

const TEST_RESULTS = {
  mediaPipeFix: false,
  navigationFlow: false,
  recordingPageLayout: false,
  cameraFunctionality: false,
  progressBarPosition: false,
  errors: []
};

/**
 * Test MediaPipe FaceMesh initialization
 */
async function testMediaPipeFix() {
  console.log('🔍 Testing MediaPipe FaceMesh Fix...');
  
  try {
    // Navigate to recording page
    window.location.href = 'http://localhost:3000?direct=recording';
    
    // Wait for page load
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Check for MediaPipe errors in console
    const hasMediaPipeError = window.console.error.toString().includes('FaceMesh is not a constructor');
    
    if (!hasMediaPipeError) {
      TEST_RESULTS.mediaPipeFix = true;
      console.log('✅ MediaPipe FaceMesh error resolved');
    } else {
      TEST_RESULTS.errors.push('MediaPipe FaceMesh constructor error still present');
      console.log('❌ MediaPipe FaceMesh error still present');
    }
    
  } catch (error) {
    TEST_RESULTS.errors.push(`MediaPipe test failed: ${error.message}`);
    console.error('❌ MediaPipe test failed:', error);
  }
}

/**
 * Test navigation flow from home to recording
 */
async function testNavigationFlow() {
  console.log('🔍 Testing Navigation Flow...');
  
  try {
    // Start from home page
    window.location.href = 'http://localhost:3000';
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Check if we can access recording page directly
    window.location.href = 'http://localhost:3000?direct=recording';
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Check if recording page loaded without errors
    const recordingPageElements = [
      document.querySelector('[data-testid="video-recorder"]') || document.querySelector('video'),
      document.querySelector('button[type="button"]'), // Record button
      document.querySelector('canvas') // Face detection canvas
    ];
    
    const pageLoaded = recordingPageElements.some(el => el !== null);
    
    if (pageLoaded) {
      TEST_RESULTS.navigationFlow = true;
      console.log('✅ Navigation flow working');
    } else {
      TEST_RESULTS.errors.push('Recording page elements not found');
      console.log('❌ Recording page not loading properly');
    }
    
  } catch (error) {
    TEST_RESULTS.errors.push(`Navigation test failed: ${error.message}`);
    console.error('❌ Navigation test failed:', error);
  }
}

/**
 * Test recording page layout requirements
 */
async function testRecordingPageLayout() {
  console.log('🔍 Testing Recording Page Layout...');
  
  try {
    // Navigate to recording page
    window.location.href = 'http://localhost:3000?direct=recording';
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Check for blue progress button (should be removed/hidden)
    const blueProgressButton = document.querySelector('button[color="primary"][variant="contained"]');
    const hasBlueProgressButton = blueProgressButton && 
      (blueProgressButton.textContent.includes('Progress') || 
       blueProgressButton.style.position === 'fixed');
    
    // Check for recording progress bar at bottom
    const bottomProgressBar = document.querySelector('[style*="position: fixed"][style*="bottom: 0"]') ||
                             document.querySelector('[style*="position:fixed"][style*="bottom:0"]');
    
    if (!hasBlueProgressButton) {
      console.log('✅ Blue progress button removed/hidden');
    } else {
      TEST_RESULTS.errors.push('Blue progress button still visible');
      console.log('❌ Blue progress button still visible');
    }
    
    if (bottomProgressBar) {
      TEST_RESULTS.progressBarPosition = true;
      console.log('✅ Recording progress bar positioned at bottom');
    } else {
      TEST_RESULTS.errors.push('Recording progress bar not found at bottom');
      console.log('❌ Recording progress bar not found at bottom');
    }
    
    TEST_RESULTS.recordingPageLayout = !hasBlueProgressButton && bottomProgressBar;
    
  } catch (error) {
    TEST_RESULTS.errors.push(`Layout test failed: ${error.message}`);
    console.error('❌ Layout test failed:', error);
  }
}

/**
 * Test camera functionality
 */
async function testCameraFunctionality() {
  console.log('🔍 Testing Camera Functionality...');
  
  try {
    // Navigate to recording page
    window.location.href = 'http://localhost:3000?direct=recording';
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Check for camera elements
    const cameraElements = {
      webcam: document.querySelector('video'),
      recordButton: document.querySelector('button[type="button"]'),
      cameraControls: document.querySelector('[role="slider"]') || document.querySelector('input[type="range"]'),
      qualityIndicators: document.querySelectorAll('[role="progressbar"]').length > 0
    };
    
    const functionalElements = Object.values(cameraElements).filter(Boolean).length;
    
    if (functionalElements >= 2) {
      TEST_RESULTS.cameraFunctionality = true;
      console.log('✅ Camera functionality elements present');
    } else {
      TEST_RESULTS.errors.push('Camera functionality elements missing');
      console.log('❌ Camera functionality elements missing');
    }
    
  } catch (error) {
    TEST_RESULTS.errors.push(`Camera test failed: ${error.message}`);
    console.error('❌ Camera test failed:', error);
  }
}

/**
 * Generate comprehensive test report
 */
function generateTestReport() {
  console.log('\n📋 NAVIGATION FIXES TEST REPORT');
  console.log('================================');
  
  const tests = [
    { name: 'MediaPipe FaceMesh Fix', status: TEST_RESULTS.mediaPipeFix },
    { name: 'Navigation Flow', status: TEST_RESULTS.navigationFlow },
    { name: 'Recording Page Layout', status: TEST_RESULTS.recordingPageLayout },
    { name: 'Camera Functionality', status: TEST_RESULTS.cameraFunctionality },
    { name: 'Progress Bar Position', status: TEST_RESULTS.progressBarPosition }
  ];
  
  const passedTests = tests.filter(test => test.status).length;
  const totalTests = tests.length;
  
  console.log(`\n📈 Summary:`);
  console.log(`   Total Tests: ${totalTests}`);
  console.log(`   Passed: ${passedTests}`);
  console.log(`   Failed: ${totalTests - passedTests}`);
  console.log(`   Success Rate: ${Math.round((passedTests / totalTests) * 100)}%`);
  
  console.log(`\n📊 Test Results:`);
  tests.forEach(test => {
    const status = test.status ? '✅' : '❌';
    console.log(`   ${status} ${test.name}`);
  });
  
  if (TEST_RESULTS.errors.length > 0) {
    console.log(`\n❌ Errors Found:`);
    TEST_RESULTS.errors.forEach((error, index) => {
      console.log(`   ${index + 1}. ${error}`);
    });
  }
  
  console.log(`\n🎯 Critical Issues Status:`);
  console.log(`   MediaPipe Error: ${TEST_RESULTS.mediaPipeFix ? 'RESOLVED' : 'NEEDS ATTENTION'}`);
  console.log(`   Navigation Flow: ${TEST_RESULTS.navigationFlow ? 'WORKING' : 'NEEDS ATTENTION'}`);
  console.log(`   Layout Requirements: ${TEST_RESULTS.recordingPageLayout ? 'IMPLEMENTED' : 'NEEDS ATTENTION'}`);
  
  const allCriticalFixed = TEST_RESULTS.mediaPipeFix && TEST_RESULTS.navigationFlow && TEST_RESULTS.recordingPageLayout;
  console.log(`\n✨ Overall Status: ${allCriticalFixed ? 'ALL CRITICAL ISSUES RESOLVED' : 'SOME ISSUES REMAIN'}`);
  
  return TEST_RESULTS;
}

/**
 * Run all tests
 */
async function runAllTests() {
  console.log('🚀 Starting Navigation Fixes Tests...\n');
  
  await testMediaPipeFix();
  await testNavigationFlow();
  await testRecordingPageLayout();
  await testCameraFunctionality();
  
  return generateTestReport();
}

// Export for browser console use
if (typeof window !== 'undefined') {
  window.testNavigationFixes = {
    runAllTests,
    testMediaPipeFix,
    testNavigationFlow,
    testRecordingPageLayout,
    testCameraFunctionality,
    results: TEST_RESULTS
  };
  
  console.log('🧪 Navigation fixes test functions available as window.testNavigationFixes');
  console.log('   Run: window.testNavigationFixes.runAllTests()');
}

// Auto-run if in Node.js environment
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { runAllTests, TEST_RESULTS };
}
