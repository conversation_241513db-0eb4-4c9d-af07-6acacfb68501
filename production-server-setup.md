# 🚀 Production Server Setup for Week-Long Operation

## Current Status Assessment
- ❌ **Current setup**: Basic npm start processes (NOT production-ready)
- ⏰ **Runtime**: ~45 minutes since session start
- 🎯 **Requirement**: 1 week continuous operation
- 📍 **Location**: localhost:5000 (backend), localhost:3000 (frontend)

## 🛡️ Option A: Local Production Setup with PM2 (RECOMMENDED)

### Step 1: Install PM2 Process Manager
```bash
# Install PM2 globally
npm install -g pm2

# Verify installation
pm2 --version
```

### Step 2: Create PM2 Configuration
Create `ecosystem.config.js` in project root:
```javascript
module.exports = {
  apps: [
    {
      name: 'icu-backend',
      script: 'server/server.js',
      cwd: '/Users/<USER>/Desktop/ICU dataset application 21.6.25',
      env: {
        NODE_ENV: 'production',
        PORT: 5000
      },
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: '1G',
      error_file: './logs/backend-error.log',
      out_file: './logs/backend-out.log',
      log_file: './logs/backend-combined.log',
      time: true
    },
    {
      name: 'icu-frontend',
      script: 'npm',
      args: 'start',
      cwd: '/Users/<USER>/Desktop/ICU dataset application 21.6.25',
      env: {
        NODE_ENV: 'development',
        PORT: 3000
      },
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: '2G',
      error_file: './logs/frontend-error.log',
      out_file: './logs/frontend-out.log',
      log_file: './logs/frontend-combined.log',
      time: true
    }
  ]
};
```

### Step 3: Setup Logging Directory
```bash
mkdir -p logs
```

### Step 4: Start Services with PM2
```bash
# Stop current processes first
pm2 kill

# Start both services
pm2 start ecosystem.config.js

# Save PM2 configuration
pm2 save

# Setup PM2 to start on system boot
pm2 startup
```

### Step 5: Monitoring Commands
```bash
# Check status
pm2 status

# View logs
pm2 logs

# Monitor in real-time
pm2 monit

# Restart if needed
pm2 restart all
```

## 🌐 Option B: Cloud Deployment (BEST for production)

### Backend Options:
1. **Heroku** (Easiest)
   - Deploy backend to Heroku
   - Update REACT_APP_BACKEND_URL to Heroku URL
   - Automatic SSL, monitoring, scaling

2. **AWS EC2** (Most control)
   - Deploy to EC2 instance
   - Use PM2 + nginx
   - Full control over environment

3. **Railway/Render** (Modern alternatives)
   - Simple deployment
   - Built-in monitoring
   - Automatic HTTPS

### Frontend Options:
1. **Netlify** (Current plan)
   - Static site deployment
   - Automatic builds from Git
   - CDN distribution

2. **Vercel** (Alternative)
   - Similar to Netlify
   - Excellent React support

## 📋 Immediate Action Plan

### For This Week (Quick Setup):
1. **Install PM2**: `npm install -g pm2`
2. **Create ecosystem.config.js** (see above)
3. **Stop current processes**: Kill terminals 271 & 273
4. **Start with PM2**: `pm2 start ecosystem.config.js`
5. **Enable startup**: `pm2 startup && pm2 save`
6. **Test**: Verify both services accessible
7. **Monitor**: Check `pm2 status` regularly

### For Long-term (Next Week):
1. **Deploy backend to cloud** (Heroku recommended)
2. **Update frontend environment variables**
3. **Deploy frontend to Netlify**
4. **Update CORS policy** for production domains
5. **Setup monitoring/alerting**

## 🔍 Monitoring & Health Checks

### Automated Monitoring Script:
```bash
#!/bin/bash
# health-check.sh
while true; do
  # Check backend
  if curl -f http://localhost:5000/health > /dev/null 2>&1; then
    echo "$(date): Backend OK"
  else
    echo "$(date): Backend DOWN - Restarting"
    pm2 restart icu-backend
  fi
  
  # Check frontend
  if curl -f http://localhost:3000 > /dev/null 2>&1; then
    echo "$(date): Frontend OK"
  else
    echo "$(date): Frontend DOWN - Restarting"
    pm2 restart icu-frontend
  fi
  
  sleep 300  # Check every 5 minutes
done
```

## ⚠️ Critical Dependencies

### S3 Progress Tracking Requirements:
- ✅ **Backend API**: localhost:5000/api/sample-counts MUST remain accessible
- ✅ **AWS Credentials**: Current .env credentials must remain valid
- ✅ **S3 Bucket**: icudatasetphrasesfortesting must remain accessible
- ✅ **CORS Policy**: Must allow frontend domain access

### Environment Stability:
- ✅ **AWS Access Keys**: Valid for 1 week minimum
- ✅ **S3 Bucket Permissions**: Read access maintained
- ✅ **Network Connectivity**: Stable internet connection
- ✅ **System Resources**: Adequate RAM/CPU for continuous operation

## 🚨 Risk Assessment

### HIGH RISK (Current Setup):
- Process termination on terminal close
- No automatic restart on crash
- No monitoring/alerting
- No log retention
- System reboot = complete failure

### LOW RISK (PM2 Setup):
- Automatic restart on crash
- Survives terminal closure
- Comprehensive logging
- System startup integration
- Real-time monitoring

### MINIMAL RISK (Cloud Deployment):
- Professional hosting infrastructure
- Built-in monitoring/alerting
- Automatic scaling
- 99.9% uptime SLA
- Professional support
