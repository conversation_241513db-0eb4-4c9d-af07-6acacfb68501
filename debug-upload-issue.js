#!/usr/bin/env node

/**
 * Debug Upload Issue Script
 * Tests the specific upload failure scenario
 */

const { execSync } = require('child_process');
const fs = require('fs');

console.log('🔍 === DEBUGGING UPLOAD NETWORK ERROR ===\n');

// Test 1: Check if both services are running
console.log('1️⃣ Checking Service Status...');
try {
  // Check backend
  const backendHealth = execSync('curl -s http://localhost:5000/health', { encoding: 'utf8' });
  const healthData = JSON.parse(backendHealth);
  console.log('✅ Backend server is running');
  console.log(`   Status: ${healthData.status}`);
  console.log(`   AWS: ${healthData.services.aws}`);
  
  // Check frontend
  const frontendCheck = execSync('curl -s -I http://localhost:3000', { encoding: 'utf8' });
  if (frontendCheck.includes('200 OK')) {
    console.log('✅ Frontend server is running');
  } else {
    console.log('⚠️ Frontend server issue detected');
  }
} catch (error) {
  console.log('❌ Service check failed:', error.message);
  process.exit(1);
}

// Test 2: Check environment variables
console.log('\n2️⃣ Checking Environment Configuration...');
try {
  const envContent = fs.readFileSync('.env', 'utf8');
  const envLines = envContent.split('\n');
  
  const backendUrl = envLines.find(line => line.startsWith('REACT_APP_BACKEND_URL='));
  const identityPool = envLines.find(line => line.startsWith('REACT_APP_AWS_IDENTITY_POOL_ID='));
  
  console.log(`   REACT_APP_BACKEND_URL: ${backendUrl ? backendUrl.split('=')[1] : 'Not set'}`);
  console.log(`   REACT_APP_AWS_IDENTITY_POOL_ID: ${identityPool ? '✅ Set' : '❌ Missing'}`);
  
  // Check if frontend will use backend upload mode
  const identityPoolValue = identityPool ? identityPool.split('=')[1] : '';
  const willUseBackend = !identityPoolValue || identityPoolValue === 'your-identity-pool-id-here';
  console.log(`   Frontend upload mode: ${willUseBackend ? 'Backend Upload' : 'Direct S3'}`);
} catch (error) {
  console.log('❌ Environment check failed:', error.message);
}

// Test 3: Test backend upload endpoint with real video data
console.log('\n3️⃣ Testing Backend Upload with Video Data...');
try {
  // Create a test video file
  const testVideoData = Buffer.from('WEBM test video data - this simulates a real video blob');
  fs.writeFileSync('/tmp/test-upload.webm', testVideoData);
  
  const uploadResponse = execSync(`curl -s -X POST http://localhost:5000/upload \\
    -F "video=@/tmp/test-upload.webm" \\
    -F "phrase=debug network test" \\
    -F "category=debug" \\
    -F "recordingNumber=1" \\
    -F 'demographics={"userId":"debug01","ageGroup":"40to64","gender":"female","ethnicity":"not_specified"}'`, 
    { encoding: 'utf8' });
  
  const uploadData = JSON.parse(uploadResponse);
  if (uploadData.success) {
    console.log('✅ Backend upload endpoint is working');
    console.log(`   File uploaded to: ${uploadData.url}`);
  } else {
    console.log('❌ Backend upload failed');
    console.log(`   Error: ${uploadData.error}`);
  }
} catch (error) {
  console.log('❌ Backend upload test failed');
  console.log(`   Error: ${error.message}`);
}

// Test 4: Test CORS configuration
console.log('\n4️⃣ Testing CORS Configuration...');
try {
  const corsResponse = execSync(`curl -s -H "Origin: http://localhost:3000" \\
    -H "Access-Control-Request-Method: POST" \\
    -H "Access-Control-Request-Headers: Content-Type" \\
    -X OPTIONS http://localhost:5000/upload`, 
    { encoding: 'utf8' });
  
  console.log('✅ CORS preflight test completed');
  console.log('   Response:', corsResponse || 'Empty response (normal for OPTIONS)');
} catch (error) {
  console.log('❌ CORS test failed');
  console.log(`   Error: ${error.message}`);
}

// Test 5: Simulate frontend fetch request
console.log('\n5️⃣ Simulating Frontend Fetch Request...');
try {
  const testScript = `
    const FormData = require('form-data');
    const fetch = require('node-fetch');
    const fs = require('fs');
    
    async function testFrontendUpload() {
      try {
        const formData = new FormData();
        const videoBuffer = Buffer.from('Test video data from frontend simulation');
        
        formData.append('video', videoBuffer, 'test_video.webm');
        formData.append('phrase', 'frontend simulation test');
        formData.append('category', 'test');
        formData.append('recordingNumber', '1');
        formData.append('demographics', JSON.stringify({
          userId: 'frontend01',
          ageGroup: '40to64',
          gender: 'female',
          ethnicity: 'not_specified'
        }));
        
        const response = await fetch('http://localhost:5000/upload', {
          method: 'POST',
          body: formData
        });
        
        if (!response.ok) {
          throw new Error(\`HTTP \${response.status}: \${response.statusText}\`);
        }
        
        const result = await response.json();
        console.log('✅ Frontend simulation successful');
        console.log('   Upload result:', result.success);
        return true;
      } catch (error) {
        console.log('❌ Frontend simulation failed');
        console.log('   Error:', error.message);
        return false;
      }
    }
    
    testFrontendUpload();
  `;
  
  fs.writeFileSync('/tmp/test-frontend.js', testScript);
  
  // Try to run the frontend simulation
  try {
    execSync('cd /tmp && npm init -y && npm install form-data node-fetch', { stdio: 'ignore' });
    const frontendResult = execSync('node /tmp/test-frontend.js', { encoding: 'utf8' });
    console.log(frontendResult);
  } catch (npmError) {
    console.log('⚠️ Could not install dependencies for frontend simulation');
    console.log('   This is not critical - the backend upload test above is sufficient');
  }
} catch (error) {
  console.log('❌ Frontend simulation setup failed');
  console.log(`   Error: ${error.message}`);
}

console.log('\n🎯 === DIAGNOSIS AND RECOMMENDATIONS ===');
console.log('Based on the tests above, here are the most likely causes:');
console.log('');
console.log('1. **Frontend-Backend Communication Issue**:');
console.log('   - Check browser console for fetch errors');
console.log('   - Verify REACT_APP_BACKEND_URL is correct');
console.log('   - Check for CORS errors in browser network tab');
console.log('');
console.log('2. **AWS Configuration Issue**:');
console.log('   - Frontend may be trying direct S3 upload instead of backend');
console.log('   - Check if REACT_APP_AWS_IDENTITY_POOL_ID is properly set');
console.log('');
console.log('3. **Video Blob Issue**:');
console.log('   - Video blob may be empty or corrupted');
console.log('   - Check video recording process');
console.log('');
console.log('📋 === NEXT DEBUGGING STEPS ===');
console.log('1. Open browser DevTools (F12) → Console tab');
console.log('2. Try recording a video and watch for detailed error logs');
console.log('3. Check Network tab for failed requests');
console.log('4. Use the "🧪 Test S3 Upload" button in the app');
console.log('5. Look for specific error messages in the console output');

// Cleanup
try {
  fs.unlinkSync('/tmp/test-upload.webm');
  fs.unlinkSync('/tmp/test-frontend.js');
} catch (cleanupError) {
  // Ignore cleanup errors
}
