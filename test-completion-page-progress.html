<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Completion Page Progress Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-button {
            background: #009688;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 5px;
            font-weight: bold;
        }
        .test-button:hover {
            background: #00796b;
        }
        .result {
            background: #f0f0f0;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
        }
        .success { background: #e8f5e8; border-left: 4px solid #4caf50; }
        .error { background: #ffeaea; border-left: 4px solid #f44336; }
        .info { background: #e3f2fd; border-left: 4px solid #2196f3; }
        .warning { background: #fff3e0; border-left: 4px solid #ff9800; }
        .instructions {
            background: #f9f9f9;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #2196f3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 Completion Page Progress Test</h1>
        <p>This page helps test and verify the S3 progress tracking on the ICU dataset application completion page.</p>
        
        <div class="instructions">
            <h3>📋 Test Instructions</h3>
            <ol>
                <li><strong>Start Backend:</strong> Ensure the backend server is running on localhost:5000</li>
                <li><strong>Start Frontend:</strong> Ensure the React app is running on localhost:3000</li>
                <li><strong>Test S3 Connection:</strong> Click "Test S3 API" to verify backend connectivity</li>
                <li><strong>Open Completion Page:</strong> Use the browser console method to trigger the completion page</li>
                <li><strong>Monitor Console:</strong> Watch browser console for debug messages</li>
            </ol>
        </div>
        
        <div>
            <button class="test-button" onclick="testS3API()">🔗 Test S3 API</button>
            <button class="test-button" onclick="testProgressCalculation()">📊 Test Progress Calculation</button>
            <button class="test-button" onclick="openAppWithInstructions()">🚀 Open App + Instructions</button>
            <button class="test-button" onclick="clearResults()">🧹 Clear Results</button>
        </div>
        
        <div id="results"></div>
    </div>

    <script>
        function addResult(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${type}`;
            resultDiv.textContent = message;
            resultsDiv.appendChild(resultDiv);
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        async function testS3API() {
            addResult('🔄 Testing S3 API endpoints...', 'info');
            
            try {
                // Test backend health
                const healthResponse = await fetch('http://localhost:5000/health');
                if (!healthResponse.ok) throw new Error(`Health check failed: ${healthResponse.status}`);
                const healthData = await healthResponse.json();
                addResult(`✅ Backend health check passed: ${healthData.status}`, 'success');
                
                // Test sample counts API
                const countsResponse = await fetch('http://localhost:5000/api/sample-counts');
                if (!countsResponse.ok) throw new Error(`Sample counts API failed: ${countsResponse.status}`);
                const countsData = await countsResponse.json();
                
                if (countsData.success) {
                    addResult(`✅ S3 Sample Counts API working:
📊 Total recordings: ${countsData.counts.total}
📝 Unique phrases: ${Object.keys(countsData.counts.byPhrase).length}
🎯 Top phrases by count:
${Object.entries(countsData.counts.byPhrase)
  .sort(([,a], [,b]) => b - a)
  .slice(0, 10)
  .map(([phrase, count]) => `   ${phrase}: ${count}`)
  .join('\n')}
⏰ Last updated: ${countsData.lastUpdated}`, 'success');
                } else {
                    addResult(`❌ S3 API returned error: ${countsData.error}`, 'error');
                }
                
            } catch (error) {
                addResult(`❌ S3 API test failed: ${error.message}`, 'error');
                addResult(`💡 Make sure the backend server is running on localhost:5000`, 'warning');
            }
        }

        async function testProgressCalculation() {
            addResult('🧮 Testing progress calculation logic...', 'info');
            
            try {
                const response = await fetch('http://localhost:5000/api/sample-counts');
                const data = await response.json();
                
                if (!data.success) {
                    addResult(`❌ Cannot test progress calculation: ${data.error}`, 'error');
                    return;
                }
                
                // Simulate progress calculation
                const phrases = data.counts.byPhrase;
                const totalPhrases = Object.keys(phrases).length;
                const totalRecordings = data.counts.total;
                const targetPerPhrase = 20;
                const totalTargetRecordings = totalPhrases * targetPerPhrase;
                const completedPhrases = Object.values(phrases).filter(count => count >= targetPerPhrase).length;
                const overallProgress = totalTargetRecordings > 0 ? Math.round((totalRecordings / totalTargetRecordings) * 100) : 0;
                
                addResult(`📊 Progress Calculation Results:
🎯 Target per phrase: ${targetPerPhrase} recordings
📝 Total phrases in S3: ${totalPhrases}
📊 Total recordings: ${totalRecordings}
🎯 Total target recordings: ${totalTargetRecordings}
✅ Completed phrases (20+ recordings): ${completedPhrases}
📈 Overall progress: ${overallProgress}%

🔍 Phrase completion status:
${Object.entries(phrases)
  .sort(([,a], [,b]) => b - a)
  .slice(0, 15)
  .map(([phrase, count]) => {
    const progress = Math.min(100, Math.round((count / targetPerPhrase) * 100));
    const status = count >= targetPerPhrase ? '✅' : '⏳';
    return `   ${status} ${phrase}: ${count}/${targetPerPhrase} (${progress}%)`;
  })
  .join('\n')}`, 'success');
                
            } catch (error) {
                addResult(`❌ Progress calculation test failed: ${error.message}`, 'error');
            }
        }

        function openAppWithInstructions() {
            addResult('🚀 Opening ICU Dataset Application...', 'info');
            
            // Open the app in a new tab
            const appWindow = window.open('http://localhost:3000', '_blank');
            
            addResult(`📋 Instructions to test completion page progress:

METHOD 1: Quick Test (Recommended)
1. Open browser console in the new tab (F12)
2. Wait for the app to load completely
3. Run this command: window.testCompletionPage()
4. The completion page should appear with real S3 progress data
5. Look for these console messages:
   • "🔄 S3ProgressDisplay: Fetching progress data"
   • "📊 S3 sample counts received"
   • "✅ S3ProgressDisplay: Progress data received"

METHOD 2: Full Workflow Test
1. Complete consent form
2. Fill out demographics
3. Select phrases for recording
4. Record 3 videos for each phrase
5. Completion page appears automatically
6. Check that progress bar shows real S3 data

🔍 What to look for on completion page:
• Progress bar with real percentage (not 0%)
• "X of Y recordings collected" with actual numbers
• Refresh button that updates data
• No error messages about failed S3 connection

🐛 Debugging tips:
• Check browser console for error messages
• Look for S3ProgressService debug logs
• Verify backend is responding at localhost:5000
• Check that AWS credentials are configured`, 'info');
        }

        // Auto-run basic connectivity test
        window.addEventListener('load', () => {
            addResult('🎯 Completion Page Progress Test Ready', 'info');
            addResult('Click "Test S3 API" to verify backend connectivity before testing the completion page.', 'info');
        });
    </script>
</body>
</html>
