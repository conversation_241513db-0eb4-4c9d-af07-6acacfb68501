<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ICU Dataset Application - Auto-Advance Debug</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        h1 {
            color: #009688;
            text-align: center;
            margin-bottom: 30px;
        }
        h2 {
            color: #00796b;
            border-bottom: 2px solid #e0f2f1;
            padding-bottom: 10px;
        }
        .button-group {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        button {
            background: #009688;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: background-color 0.3s;
        }
        button:hover {
            background: #00796b;
        }
        button.danger {
            background: #f44336;
        }
        button.danger:hover {
            background: #d32f2f;
        }
        .output {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.4;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
            margin-top: 10px;
        }
        .test-step {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
        }
        .test-step h3 {
            margin: 0 0 10px 0;
            color: #1976d2;
        }
        .issue-box {
            background: #ffebee;
            border: 1px solid #ffcdd2;
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
        }
        .issue-box h3 {
            margin: 0 0 10px 0;
            color: #c62828;
        }
    </style>
</head>
<body>
    <h1>🔧 ICU Dataset Application - Auto-Advance Debug</h1>
    
    <div class="container">
        <div class="issue-box">
            <h3>🐛 Current Issue</h3>
            <p><strong>Problem:</strong> After completing 3 recordings of the first phrase, the application skips directly to completion instead of advancing to the second phrase.</p>
            <p><strong>Expected:</strong> Should advance through all selected phrases (phrase 1 → phrase 2 → phrase 3) before showing completion.</p>
        </div>
    </div>

    <div class="container">
        <h2>🔍 Debug Steps</h2>
        
        <div class="test-step">
            <h3>Step 1: Clear Data & Setup</h3>
            <p>1. Clear all localStorage data</p>
            <p>2. Go to React app: <a href="http://localhost:3003" target="_blank">http://localhost:3003</a></p>
            <p>3. Complete: Consent → Demographics → Training</p>
        </div>
        
        <div class="test-step">
            <h3>Step 2: Select Multiple Phrases</h3>
            <p>1. Select exactly <strong>3 phrases</strong> from any category</p>
            <p>2. Note which phrases you selected</p>
            <p>3. Click "Start Recording"</p>
        </div>
        
        <div class="test-step">
            <h3>Step 3: Record First Phrase</h3>
            <p>1. Record the first phrase <strong>3 times</strong></p>
            <p>2. Watch console logs for auto-advance messages</p>
            <p>3. <strong>Expected:</strong> Should advance to second phrase</p>
            <p>4. <strong>Actual:</strong> Likely skips to completion</p>
        </div>
        
        <div class="test-step">
            <h3>Step 4: Analyze Console Logs</h3>
            <p>Look for these key messages in browser console:</p>
            <p>• <code>🚀 === HANDLE NEXT PHRASE CALLED ===</code></p>
            <p>• <code>🔍 COMPLETION CHECK:</code></p>
            <p>• <code>🔄 AUTO-ADVANCE CHECK:</code></p>
            <p>• <code>isLastPhrase: true/false</code></p>
        </div>
    </div>

    <div class="container">
        <h2>🧪 Debug Tools</h2>
        <div class="button-group">
            <button onclick="clearAllData()" class="danger">🗑️ Clear All Data</button>
            <button onclick="checkCurrentState()">📊 Check Current State</button>
            <button onclick="simulateAutoAdvance()">🔄 Simulate Auto-Advance</button>
        </div>
        <div id="debug-output" class="output"></div>
    </div>

    <div class="container">
        <h2>🔍 Expected Console Log Pattern</h2>
        <div class="output">
After 3rd recording of first phrase:
🚀 === HANDLE NEXT PHRASE CALLED ===
🔍 COMPLETION CHECK:
  currentPhraseIndex: 0
  selectedPhrases.length - 1: 2
  isLastPhrase: false
  Will show completion? false
📝 ADVANCING TO NEXT PHRASE
📝 Next phrase details: { nextPhraseIndex: 1, ... }

After 3rd recording of second phrase:
🚀 === HANDLE NEXT PHRASE CALLED ===
🔍 COMPLETION CHECK:
  currentPhraseIndex: 1
  selectedPhrases.length - 1: 2
  isLastPhrase: false
  Will show completion? false
📝 ADVANCING TO NEXT PHRASE

After 3rd recording of third phrase:
🚀 === HANDLE NEXT PHRASE CALLED ===
🔍 COMPLETION CHECK:
  currentPhraseIndex: 2
  selectedPhrases.length - 1: 2
  isLastPhrase: true
  Will show completion? true
🏁 END OF PHRASE LIST: User has recordings, showing completion prompt
        </div>
    </div>

    <script>
        function log(outputId, message) {
            const output = document.getElementById(outputId);
            output.textContent += message + '\n';
        }

        function clearLog(outputId) {
            document.getElementById(outputId).textContent = '';
        }

        function clearAllData() {
            clearLog('debug-output');
            log('debug-output', '🗑️ Clearing all application data...\n');
            
            const keysToRemove = [
                'icuAppRecordingsCount',
                'icu_selected_phrases',
                'icuAppDemographics',
                'icuAppSelectedPhrases',
                'icuAppCompletedPhrases',
                'icuAppLocalSaves'
            ];
            
            keysToRemove.forEach(key => {
                if (localStorage.getItem(key)) {
                    localStorage.removeItem(key);
                    log('debug-output', `✅ Removed: ${key}`);
                }
            });
            
            sessionStorage.clear();
            log('debug-output', '✅ Cleared sessionStorage');
            
            log('debug-output', '\n🎉 All data cleared! Ready for fresh test.');
        }

        function checkCurrentState() {
            clearLog('debug-output');
            log('debug-output', '📊 Checking current application state...\n');
            
            // Check recording counts
            const recordingCounts = localStorage.getItem('icuAppRecordingsCount');
            const selectedPhrases = localStorage.getItem('icu_selected_phrases');
            
            log('debug-output', '📱 localStorage Status:');
            log('debug-output', `  Recording counts: ${recordingCounts ? 'EXISTS' : 'NONE'}`);
            log('debug-output', `  Selected phrases: ${selectedPhrases ? 'EXISTS' : 'NONE'}`);
            
            if (recordingCounts) {
                try {
                    const counts = JSON.parse(recordingCounts);
                    log('debug-output', '\n📈 Recording Counts:');
                    Object.entries(counts).forEach(([phraseKey, count]) => {
                        log('debug-output', `  ${phraseKey}: ${count} recordings`);
                    });
                } catch (e) {
                    log('debug-output', '❌ Error parsing recording counts');
                }
            }
            
            if (selectedPhrases) {
                try {
                    const phrases = JSON.parse(selectedPhrases);
                    log('debug-output', '\n📝 Selected Phrases:');
                    log('debug-output', `  Total: ${phrases.length} phrases`);
                    phrases.forEach((phrase, index) => {
                        log('debug-output', `  ${index}: "${phrase.phrase}" (${phrase.category})`);
                    });
                } catch (e) {
                    log('debug-output', '❌ Error parsing selected phrases');
                }
            }
        }

        function simulateAutoAdvance() {
            clearLog('debug-output');
            log('debug-output', '🔄 Simulating auto-advance logic...\n');
            
            const selectedPhrases = localStorage.getItem('icu_selected_phrases');
            const recordingCounts = localStorage.getItem('icuAppRecordingsCount');
            
            if (!selectedPhrases) {
                log('debug-output', '❌ No selected phrases found');
                return;
            }
            
            try {
                const phrases = JSON.parse(selectedPhrases);
                const counts = recordingCounts ? JSON.parse(recordingCounts) : {};
                
                log('debug-output', `📝 Simulating progression through ${phrases.length} phrases:\n`);
                
                phrases.forEach((phrase, index) => {
                    const phraseKey = `${phrase.category}:${phrase.phrase}`;
                    const currentCount = counts[phraseKey] || 0;
                    const isLastPhrase = index >= phrases.length - 1;
                    
                    log('debug-output', `Phrase ${index + 1}: "${phrase.phrase}"`);
                    log('debug-output', `  Current recordings: ${currentCount}`);
                    log('debug-output', `  Is last phrase: ${isLastPhrase}`);
                    log('debug-output', `  Action after 3 recordings: ${isLastPhrase ? 'SHOW COMPLETION' : 'ADVANCE TO NEXT'}`);
                    log('debug-output', '');
                });
                
            } catch (e) {
                log('debug-output', '❌ Error simulating auto-advance');
            }
        }

        // Initialize on page load
        window.addEventListener('load', () => {
            checkCurrentState();
        });
    </script>
</body>
</html>
