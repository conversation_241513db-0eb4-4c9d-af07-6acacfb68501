/**
 * Verify Auto-Advance Fix
 * This script tests the auto-advance functionality to ensure it's working
 */

console.log('🔧 VERIFYING AUTO-ADVANCE FIX');
console.log('=============================');

// Test 1: Check if the application is running
async function checkInfrastructure() {
    console.log('\n📡 Testing Infrastructure...');
    
    try {
        const reactResponse = await fetch('http://localhost:3000');
        console.log('✅ React server (localhost:3000):', reactResponse.ok ? 'RUNNING' : 'FAILED');
    } catch (error) {
        console.log('❌ React server (localhost:3000): FAILED -', error.message);
    }

    try {
        const backendResponse = await fetch('http://localhost:5000/health');
        console.log('✅ Backend server (localhost:5000):', backendResponse.ok ? 'RUNNING' : 'FAILED');
    } catch (error) {
        console.log('❌ Backend server (localhost:5000): FAILED -', error.message);
    }
}

// Test 2: Check localStorage for existing data
function checkLocalStorage() {
    console.log('\n💾 Checking localStorage...');
    
    const recordingsCount = localStorage.getItem('icuAppRecordingsCount');
    const sessionData = localStorage.getItem('icuAppSessionData');
    
    console.log('📊 Recordings Count:', recordingsCount ? JSON.parse(recordingsCount) : 'None');
    console.log('📋 Session Data:', sessionData ? 'Present' : 'None');
}

// Test 3: Simulate the auto-advance logic
function simulateAutoAdvance() {
    console.log('\n🧪 Simulating Auto-Advance Logic...');
    
    // Mock data similar to the application
    const mockPhrases = [
        { id: 1, phrase: "Hello", category: "Greetings" },
        { id: 2, phrase: "Thank you", category: "Greetings" },
        { id: 3, phrase: "I need help", category: "Medical" }
    ];
    
    const mockState = {
        selectedPhrases: mockPhrases,
        currentPhraseIndex: 0,
        recordingsCount: {
            'Greetings:Hello': 3  // This should trigger auto-advance
        },
        RECORDINGS_PER_PHRASE: 3
    };
    
    console.log('📊 Mock State:', mockState);
    
    // Test the auto-advance condition
    const currentPhraseObj = mockState.selectedPhrases[mockState.currentPhraseIndex];
    const phraseKey = `${currentPhraseObj.category}:${currentPhraseObj.phrase}`;
    const currentCount = mockState.recordingsCount[phraseKey] || 0;
    
    console.log('🔍 Auto-Advance Check:');
    console.log('  Current Phrase:', currentPhraseObj.phrase);
    console.log('  Phrase Key:', phraseKey);
    console.log('  Current Count:', currentCount);
    console.log('  Required:', mockState.RECORDINGS_PER_PHRASE);
    console.log('  Should Advance:', currentCount >= mockState.RECORDINGS_PER_PHRASE);
    
    if (currentCount >= mockState.RECORDINGS_PER_PHRASE) {
        console.log('✅ AUTO-ADVANCE CONDITION MET - Would advance to next phrase');
        
        if (mockState.currentPhraseIndex < mockState.selectedPhrases.length - 1) {
            const nextPhrase = mockState.selectedPhrases[mockState.currentPhraseIndex + 1];
            console.log('📝 Next Phrase:', nextPhrase.phrase);
            console.log('📝 Next Category:', nextPhrase.category);
        } else {
            console.log('🏁 LAST PHRASE - Would show completion');
        }
    } else {
        console.log('⏳ AUTO-ADVANCE CONDITION NOT MET - Need more recordings');
    }
}

// Test 4: Instructions for manual testing
function showManualTestInstructions() {
    console.log('\n📋 MANUAL TEST INSTRUCTIONS');
    console.log('============================');
    console.log('1. Open http://localhost:3000');
    console.log('2. Complete consent and demographics');
    console.log('3. Select 2-3 phrases from different categories');
    console.log('4. Record 3 videos for the first phrase');
    console.log('5. Watch console for auto-advance logs:');
    console.log('   - 🔄 AUTO-ADVANCE EFFECT TRIGGERED');
    console.log('   - 📹 RECORDING COMPLETED FUNCTION CALLED');
    console.log('   - 🎯 AUTO-ADVANCE: Phrase completion detected');
    console.log('   - 🚀 === HANDLE NEXT PHRASE CALLED ===');
    console.log('6. Verify phrase automatically changes after 3rd recording');
}

// Test 5: Check for common issues
function checkCommonIssues() {
    console.log('\n🔍 CHECKING FOR COMMON ISSUES');
    console.log('==============================');
    
    // Check if React DevTools is available
    if (window.__REACT_DEVTOOLS_GLOBAL_HOOK__) {
        console.log('✅ React DevTools detected');
    } else {
        console.log('⚠️ React DevTools not detected (optional)');
    }
    
    // Check console for errors
    const originalError = console.error;
    let errorCount = 0;
    console.error = function(...args) {
        errorCount++;
        originalError.apply(console, args);
    };
    
    setTimeout(() => {
        console.log('📊 Console Errors Detected:', errorCount);
        console.error = originalError;
    }, 1000);
    
    // Check if localStorage is working
    try {
        localStorage.setItem('test', 'test');
        localStorage.removeItem('test');
        console.log('✅ localStorage is working');
    } catch (error) {
        console.log('❌ localStorage error:', error.message);
    }
}

// Run all tests
async function runAllTests() {
    await checkInfrastructure();
    checkLocalStorage();
    simulateAutoAdvance();
    checkCommonIssues();
    showManualTestInstructions();
    
    console.log('\n🎯 VERIFICATION COMPLETE');
    console.log('========================');
    console.log('If infrastructure is running, proceed with manual testing.');
    console.log('The auto-advance fix should work after 3 recordings per phrase.');
}

// Export for browser use
if (typeof window !== 'undefined') {
    window.verifyAutoAdvanceFix = runAllTests;
    window.checkInfrastructure = checkInfrastructure;
    window.simulateAutoAdvance = simulateAutoAdvance;
}

// Run tests
runAllTests();
