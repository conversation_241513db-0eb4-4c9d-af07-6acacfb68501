<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ICU Dataset Application - Progress Tracking Test</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .header {
            background: linear-gradient(135deg, #2c5aa0 0%, #26a69a 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            margin: 10px 0;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #fafafa;
        }
        .status {
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: bold;
            min-width: 100px;
            text-align: center;
        }
        .status.success { background-color: #4caf50; color: white; }
        .status.error { background-color: #f44336; color: white; }
        .status.warning { background-color: #ff9800; color: white; }
        .status.pending { background-color: #2196f3; color: white; }
        .test-button {
            background-color: #2c5aa0;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        .test-button:hover {
            background-color: #1e3d6f;
        }
        .results {
            margin-top: 20px;
            padding: 15px;
            background-color: #f0f0f0;
            border-radius: 8px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .progress-bar {
            width: 100%;
            height: 20px;
            background-color: #e0e0e0;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #26a69a, #2c5aa0);
            transition: width 0.3s ease;
        }
        .phrase-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 12px;
            margin: 5px 0;
            background-color: #f9f9f9;
            border-radius: 6px;
            border-left: 4px solid #26a69a;
        }
        .category-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #fafafa;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🏥 ICU Dataset Application</h1>
        <h2>Real-time Progress Tracking Test</h2>
        <p>Testing dynamic recording counts from AWS S3 bucket</p>
    </div>

    <div class="test-section">
        <h3>📊 S3 Progress Data Test</h3>
        <div class="test-item">
            <span>Backend API Connection</span>
            <span class="status pending" id="api-status">Testing...</span>
        </div>
        <div class="test-item">
            <span>Sample Counts Endpoint</span>
            <span class="status pending" id="counts-status">Testing...</span>
        </div>
        <div class="test-item">
            <span>Progress Calculation</span>
            <span class="status pending" id="progress-status">Testing...</span>
        </div>
        <div class="test-item">
            <span>Phrase Mapping</span>
            <span class="status pending" id="mapping-status">Testing...</span>
        </div>
    </div>

    <div class="test-section">
        <h3>🎯 Dynamic Progress Display</h3>
        <div id="progress-display">
            <p>Loading progress data...</p>
        </div>
    </div>

    <div class="test-section">
        <h3>📋 Category Progress Summary</h3>
        <div id="category-progress">
            <p>Loading category data...</p>
        </div>
    </div>

    <div class="test-section">
        <h3>🔍 Sample Phrase Progress</h3>
        <div id="phrase-samples">
            <p>Loading phrase samples...</p>
        </div>
    </div>

    <div class="test-section">
        <h3>🧪 Test Controls</h3>
        <button class="test-button" onclick="runProgressTest()">🔄 Test Progress System</button>
        <button class="test-button" onclick="openApplication()">🚀 Open Application</button>
        <button class="test-button" onclick="refreshData()">🔄 Refresh Data</button>
        <button class="test-button" onclick="testPhraseMapping()">🔍 Test Phrase Mapping</button>
    </div>

    <div class="results" id="test-results">
Ready to test real-time progress tracking system...

🎯 Test Objectives:
- Verify S3 API connectivity
- Test dynamic progress calculation
- Validate phrase name mapping
- Check category-level aggregation
- Confirm real-time updates

Click "Test Progress System" to begin.
    </div>

    <script>
        let progressData = null;
        let testResults = [];

        async function runProgressTest() {
            document.getElementById('test-results').textContent = 'Running progress tracking tests...\n\n';
            testResults = [];
            
            await testAPIConnection();
            await testSampleCounts();
            await testProgressCalculation();
            await testPhraseMapping();
            await displayProgressData();
            await displayCategoryProgress();
            await displayPhraseSamples();
            
            displayFinalResults();
        }

        async function testAPIConnection() {
            updateStatus('api-status', 'Testing...');
            try {
                const response = await fetch('http://localhost:5000/health');
                const data = await response.json();
                if (data.status === 'healthy') {
                    updateStatus('api-status', 'SUCCESS', 'success');
                    logResult('✅ API: Backend server healthy and responsive');
                } else {
                    throw new Error('Backend unhealthy');
                }
            } catch (error) {
                updateStatus('api-status', 'ERROR', 'error');
                logResult('❌ API: ' + error.message);
            }
        }

        async function testSampleCounts() {
            updateStatus('counts-status', 'Testing...');
            try {
                const response = await fetch('http://localhost:5000/api/sample-counts');
                const data = await response.json();
                if (data.success && data.counts) {
                    progressData = data;
                    updateStatus('counts-status', 'SUCCESS', 'success');
                    logResult(`✅ Sample Counts: ${data.counts.total} total recordings found`);
                    logResult(`   - By phrase: ${Object.keys(data.counts.byPhrase).length} different phrases`);
                    logResult(`   - By gender: Male(${data.counts.byGender.male}), Female(${data.counts.byGender.female})`);
                } else {
                    throw new Error('Invalid sample counts data');
                }
            } catch (error) {
                updateStatus('counts-status', 'ERROR', 'error');
                logResult('❌ Sample Counts: ' + error.message);
            }
        }

        async function testProgressCalculation() {
            updateStatus('progress-status', 'Testing...');
            try {
                if (!progressData) throw new Error('No progress data available');
                
                const byPhrase = progressData.counts.byPhrase;
                const totalPhrases = Object.keys(byPhrase).length;
                const targetPerPhrase = 20;
                let completedPhrases = 0;
                
                Object.values(byPhrase).forEach(count => {
                    if (count >= targetPerPhrase) completedPhrases++;
                });
                
                const overallProgress = Math.round((completedPhrases / totalPhrases) * 100);
                
                updateStatus('progress-status', 'SUCCESS', 'success');
                logResult(`✅ Progress Calculation: ${overallProgress}% overall completion`);
                logResult(`   - Completed phrases: ${completedPhrases}/${totalPhrases}`);
                logResult(`   - Target per phrase: ${targetPerPhrase} recordings`);
            } catch (error) {
                updateStatus('progress-status', 'ERROR', 'error');
                logResult('❌ Progress Calculation: ' + error.message);
            }
        }

        async function testPhraseMapping() {
            updateStatus('mapping-status', 'Testing...');
            try {
                if (!progressData) throw new Error('No progress data available');
                
                const byPhrase = progressData.counts.byPhrase;
                const samplePhrases = Object.keys(byPhrase).slice(0, 5);
                
                updateStatus('mapping-status', 'SUCCESS', 'success');
                logResult('✅ Phrase Mapping: Successfully mapped S3 phrase keys');
                logResult('   Sample phrases found:');
                samplePhrases.forEach(phrase => {
                    logResult(`     - "${phrase}": ${byPhrase[phrase]} recordings`);
                });
            } catch (error) {
                updateStatus('mapping-status', 'ERROR', 'error');
                logResult('❌ Phrase Mapping: ' + error.message);
            }
        }

        async function displayProgressData() {
            const container = document.getElementById('progress-display');
            if (!progressData) {
                container.innerHTML = '<p style="color: red;">No progress data available</p>';
                return;
            }

            const total = progressData.counts.total;
            const byPhrase = progressData.counts.byPhrase;
            const completedCount = Object.values(byPhrase).filter(count => count >= 20).length;
            const totalPhrases = Object.keys(byPhrase).length;
            const overallProgress = Math.round((completedCount / totalPhrases) * 100);

            container.innerHTML = `
                <div style="text-align: center; margin-bottom: 20px;">
                    <h4>Overall Collection Progress</h4>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: ${overallProgress}%"></div>
                    </div>
                    <p><strong>${overallProgress}%</strong> complete (${completedCount}/${totalPhrases} phrases)</p>
                    <p>Total recordings: <strong>${total}</strong></p>
                </div>
            `;
        }

        async function displayCategoryProgress() {
            const container = document.getElementById('category-progress');
            if (!progressData) {
                container.innerHTML = '<p style="color: red;">No progress data available</p>';
                return;
            }

            // Group phrases by category (simplified - using first word as category)
            const byPhrase = progressData.counts.byPhrase;
            const categories = {};
            
            Object.entries(byPhrase).forEach(([phrase, count]) => {
                const category = phrase.split('_')[0] || 'Other';
                if (!categories[category]) {
                    categories[category] = { total: 0, phrases: 0, completed: 0 };
                }
                categories[category].total += count;
                categories[category].phrases++;
                if (count >= 20) categories[category].completed++;
            });

            let html = '';
            Object.entries(categories).slice(0, 8).forEach(([category, data]) => {
                const progress = Math.round((data.completed / data.phrases) * 100);
                html += `
                    <div class="category-section">
                        <h5>${category}</h5>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: ${progress}%"></div>
                        </div>
                        <p>${data.completed}/${data.phrases} phrases completed (${data.total} recordings)</p>
                    </div>
                `;
            });

            container.innerHTML = html;
        }

        async function displayPhraseSamples() {
            const container = document.getElementById('phrase-samples');
            if (!progressData) {
                container.innerHTML = '<p style="color: red;">No progress data available</p>';
                return;
            }

            const byPhrase = progressData.counts.byPhrase;
            const samples = Object.entries(byPhrase)
                .sort(([,a], [,b]) => b - a)
                .slice(0, 10);

            let html = '<h4>Top 10 Phrases by Recording Count</h4>';
            samples.forEach(([phrase, count]) => {
                const progress = Math.min(100, (count / 20) * 100);
                const status = count >= 20 ? '✅' : count > 0 ? '🔄' : '⭕';
                html += `
                    <div class="phrase-item">
                        <span>${status} ${phrase}</span>
                        <span><strong>${count}/20</strong> (${Math.round(progress)}%)</span>
                    </div>
                `;
            });

            container.innerHTML = html;
        }

        function updateStatus(elementId, text, className = 'pending') {
            const element = document.getElementById(elementId);
            element.textContent = text;
            element.className = `status ${className}`;
        }

        function logResult(message) {
            testResults.push(message);
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.textContent += message + '\n';
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        function displayFinalResults() {
            const successCount = testResults.filter(r => r.startsWith('✅')).length;
            const errorCount = testResults.filter(r => r.startsWith('❌')).length;
            
            logResult('\n' + '='.repeat(50));
            logResult(`📊 TEST SUMMARY: ${successCount} passed, ${errorCount} failed`);
            
            if (errorCount === 0) {
                logResult('🎉 ALL TESTS PASSED - Progress tracking system working!');
            } else {
                logResult('⚠️  Some tests failed - Check system configuration');
            }
        }

        function openApplication() {
            window.open('http://localhost:3000', '_blank');
            logResult('🚀 Opening ICU Dataset Application...');
        }

        function refreshData() {
            logResult('🔄 Refreshing progress data...');
            runProgressTest();
        }

        function testPhraseMapping() {
            logResult('🔍 Testing phrase name variations...');
            if (progressData) {
                const variations = [
                    'call_the_doctor',
                    'Call the doctor.',
                    'call the doctor',
                    'CALL_THE_DOCTOR'
                ];
                
                variations.forEach(variation => {
                    const count = progressData.counts.byPhrase[variation] || 0;
                    logResult(`   "${variation}": ${count} recordings`);
                });
            }
        }

        // Auto-run basic test on page load
        window.onload = function() {
            setTimeout(runProgressTest, 1000);
        };
    </script>
</body>
</html>
