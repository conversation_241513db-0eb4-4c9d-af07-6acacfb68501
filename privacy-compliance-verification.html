<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔒 Privacy Compliance Verification</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .privacy-header {
            background: #28a745;
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .compliance-check {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .privacy-violation {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .console-output {
            background: #1e1e1e;
            color: #00ff00;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 11px;
            margin: 10px 0;
            max-height: 200px;
            overflow-y: auto;
        }
        .step {
            background: #e9ecef;
            padding: 15px;
            margin: 10px 0;
            border-left: 4px solid #007bff;
            border-radius: 4px;
        }
        .button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            text-decoration: none;
            display: inline-block;
        }
        .button.success { background: #28a745; }
        .button.danger { background: #dc3545; }
        .two-column {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
    </style>
</head>
<body>
    <div class="privacy-header">
        <h1>🔒 Privacy Compliance Verification</h1>
        <p><strong>Objective:</strong> Verify that VideoRecorder only captures mouth region data for privacy compliance</p>
        <p><strong>Requirement:</strong> No eyes, upper face, or audio data should be recorded or stored</p>
    </div>

    <div class="test-container">
        <h2>🎯 Privacy Compliance Requirements</h2>
        <div class="two-column">
            <div class="compliance-check">
                <h3>✅ Required Privacy Features</h3>
                <ul>
                    <li>Record only mouth region (no eyes)</li>
                    <li>No audio tracks in recordings</li>
                    <li>Video dimensions match mouth canvas (400x300)</li>
                    <li>Content shows only lower face/mouth area</li>
                    <li>No identifiable upper face features</li>
                    <li>Consistent with UI oval viewport display</li>
                </ul>
            </div>
            <div class="privacy-violation">
                <h3>❌ Privacy Violations to Check For</h3>
                <ul>
                    <li>Full face recordings (640x480)</li>
                    <li>Eyes or forehead visible in recordings</li>
                    <li>Audio tracks present in video files</li>
                    <li>Recordings larger than mouth region</li>
                    <li>Identifiable facial features above nose</li>
                    <li>Mismatch between UI display and recording</li>
                </ul>
            </div>
        </div>
    </div>

    <div class="test-container">
        <h2>🧪 Privacy Compliance Test Steps</h2>
        
        <div class="step">
            <h3>Step 1: Open Application and Monitor Console</h3>
            <ol>
                <li>Open <a href="http://localhost:3003" target="_blank" class="button success">🔗 ICU Dataset Application</a></li>
                <li>Open Developer Tools (F12) → Console tab</li>
                <li>Look for privacy compliance messages during initialization</li>
            </ol>
            <div class="compliance-check">
                <h4>✅ Expected Console Messages:</h4>
                <div class="console-output">
🔒 PRIVACY MODE: Using mouth-region-only recording for compliance
👄 Recording only mouth movements - no eyes or upper face data
👄 Mouth canvas initialized: {width: 400, height: 300, purpose: 'Direct mouth region recording for privacy and performance'}
🔒 PRIVACY COMPLIANCE: Using mouth-region-only recording
👄 Recording mouth movements only - no eyes, no audio
                </div>
            </div>
        </div>

        <div class="step">
            <h3>Step 2: Complete Setup and Navigate to Recording</h3>
            <ol>
                <li>Fill demographics form (any test values)</li>
                <li>Select 2-3 phrases from any category</li>
                <li>Navigate to recording page</li>
                <li>Grant camera permissions</li>
                <li>Verify oval viewport shows mouth region only</li>
            </ol>
        </div>

        <div class="step">
            <h3>Step 3: Test Privacy-Compliant Recording</h3>
            <ol>
                <li>Click "Start Recording"</li>
                <li>Monitor console for privacy compliance messages</li>
                <li>Verify 5-second countdown works</li>
                <li>Check that recording stops automatically</li>
            </ol>
            <div class="compliance-check">
                <h4>✅ Expected Recording Console Output:</h4>
                <div class="console-output">
👄 Starting privacy-compliant mouth recording...
🔒 Recording mouth region only - no eyes, no audio, no upper face
👄 Mouth canvas stream details: {streamActive: true, videoTracks: 1, audioTracks: 0, canvasSize: '400x300', privacyCompliant: true}
👄 Privacy-compliant MediaRecorder created with VP9 codec
👄 Privacy-compliant mouth recording started successfully
👄 Privacy-compliant mouth recording chunk received: {size: 12345, type: "video/webm", privacyCompliant: true, contentType: "mouth-region-only"}
👄 Privacy-compliant mouth recording stopped
🔒 Mouth-region-only recording completed
                </div>
            </div>
        </div>

        <div class="step">
            <h3>Step 4: Verify Upload and Storage Compliance</h3>
            <ol>
                <li>Monitor upload process completion</li>
                <li>Check that video blob contains mouth-only data</li>
                <li>Verify successful backend upload</li>
                <li>Confirm no privacy violations in stored data</li>
            </ol>
            <div class="compliance-check">
                <h4>✅ Expected Upload Console Output:</h4>
                <div class="console-output">
👄 Privacy-compliant MediaRecorder onstop event triggered
🔒 Privacy-compliant videoBlob created: {size: 67890, type: "video/webm", contentType: "mouth-region-only", privacyCompliant: true}
👄 Mouth region drawing stopped after recording completion
🔄 FORCED BACKEND MODE: Using backend upload to bypass S3 CORS issues
✅ Backend upload successful
🎉 Recording saved and processed without errors!
                </div>
            </div>
        </div>
    </div>

    <div class="test-container">
        <h2>🔍 Privacy Compliance Verification Checklist</h2>
        
        <div class="step">
            <h3>Manual Verification Steps</h3>
            <div class="compliance-check">
                <h4>Check These Items During Testing:</h4>
                <ul>
                    <li>☐ Console shows "privacy-compliant" messages throughout recording</li>
                    <li>☐ Mouth canvas dimensions are 400x300 (not 640x480)</li>
                    <li>☐ Audio tracks count is 0 in stream details</li>
                    <li>☐ Video blob size is smaller than full-face recordings</li>
                    <li>☐ Recording content type shows "mouth-region-only"</li>
                    <li>☐ No "webcam recording" messages (should be "mouth recording")</li>
                    <li>☐ Upload process maintains privacy compliance</li>
                    <li>☐ No errors related to mouth canvas or stream creation</li>
                </ul>
            </div>
        </div>

        <div class="step">
            <h3>Privacy Violation Indicators</h3>
            <div class="privacy-violation">
                <h4>❌ Stop Testing If You See:</h4>
                <ul>
                    <li>Console messages about "webcam recording" instead of "mouth recording"</li>
                    <li>Stream details showing audioTracks > 0</li>
                    <li>Canvas size 640x480 instead of 400x300</li>
                    <li>Missing "privacyCompliant: true" in console logs</li>
                    <li>Video blob sizes similar to full-face recordings (>500KB)</li>
                    <li>Errors about mouth canvas initialization</li>
                </ul>
            </div>
        </div>
    </div>

    <div class="test-container">
        <h2>📊 Expected Privacy Compliance Results</h2>
        
        <div class="compliance-check">
            <h3>✅ Successful Privacy Compliance</h3>
            <p><strong>Recording Source:</strong> Mouth canvas (400x300) instead of webcam (640x480)</p>
            <p><strong>Content:</strong> Mouth movements only, no eyes or upper face</p>
            <p><strong>Audio:</strong> No audio tracks recorded</p>
            <p><strong>Storage:</strong> Only mouth region data saved to S3</p>
            <p><strong>UI Consistency:</strong> Recorded content matches oval viewport display</p>
            <p><strong>Data Minimization:</strong> Only essential mouth movement data captured</p>
        </div>

        <div class="step">
            <h3>Next Steps After Verification</h3>
            <ol>
                <li>If privacy compliance passes: Proceed with full testing</li>
                <li>If violations found: Stop and fix recording source</li>
                <li>Test multiple recordings to ensure consistency</li>
                <li>Verify auto-advancement maintains privacy compliance</li>
                <li>Document privacy compliance for audit purposes</li>
            </ol>
        </div>
    </div>

    <script>
        // Auto-open application for testing
        function startPrivacyTest() {
            window.open('http://localhost:3003', '_blank');
            alert('Privacy compliance test started!\n\n1. Open Developer Tools (F12)\n2. Go to Console tab\n3. Follow the test steps above\n4. Monitor for privacy compliance messages');
        }

        // Add button to start test
        window.addEventListener('load', () => {
            const button = document.createElement('button');
            button.textContent = '🚀 Start Privacy Compliance Test';
            button.className = 'button success';
            button.onclick = startPrivacyTest;
            document.querySelector('.privacy-header').appendChild(button);
        });
    </script>
</body>
</html>
