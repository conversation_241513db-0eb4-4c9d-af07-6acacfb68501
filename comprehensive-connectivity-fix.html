<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 Comprehensive Connectivity Fix</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .urgent-container {
            background: #dc3545;
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .fix-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .two-column {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        .status-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #28a745;
        }
        .error-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #dc3545;
        }
        .cors-policy {
            background: #1e1e1e;
            color: #00ff00;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 11px;
            margin: 10px 0;
            overflow-x: auto;
            max-height: 300px;
            overflow-y: auto;
        }
        .step {
            background: #e9ecef;
            padding: 15px;
            margin: 10px 0;
            border-left: 4px solid #007bff;
            border-radius: 4px;
        }
        .button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            text-decoration: none;
            display: inline-block;
        }
        .button.danger { background: #dc3545; }
        .button.success { background: #28a745; }
        .button.warning { background: #ffc107; color: #212529; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 4px; margin: 10px 0; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 15px; border-radius: 4px; margin: 10px 0; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 4px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="urgent-container">
        <h1>🚨 URGENT: Dual Connectivity Issues Diagnosed</h1>
        <div class="two-column">
            <div>
                <h3>❌ Issue 1: S3 CORS Error</h3>
                <p><strong>Problem:</strong> S3 bucket blocking localhost:3003</p>
                <p><strong>Impact:</strong> Video uploads fail after recording</p>
                <p><strong>Status:</strong> Requires immediate S3 CORS policy update</p>
            </div>
            <div>
                <h3>❌ Issue 2: Backend Test Port Mismatch</h3>
                <p><strong>Problem:</strong> Tests pointing to port 5001, server on 5000</p>
                <p><strong>Impact:</strong> Connectivity tests fail</p>
                <p><strong>Status:</strong> Fixed - servers confirmed running correctly</p>
            </div>
        </div>
    </div>

    <div class="fix-container">
        <h2>✅ Current Server Status - VERIFIED</h2>
        <div class="two-column">
            <div class="status-card">
                <h3>✅ Backend Server</h3>
                <p><strong>URL:</strong> <a href="http://localhost:5000/health" target="_blank">http://localhost:5000/health</a></p>
                <p><strong>Status:</strong> Running and responding</p>
                <p><strong>Services:</strong> Upload, Health, Sample Counts, Metrics</p>
                <button class="button success" onclick="testBackend()">🧪 Test Backend</button>
            </div>
            <div class="status-card">
                <h3>✅ Frontend Server</h3>
                <p><strong>URL:</strong> <a href="http://localhost:3003" target="_blank">http://localhost:3003</a></p>
                <p><strong>Status:</strong> Running with correct env vars</p>
                <p><strong>Environment:</strong> REACT_APP_BACKEND_URL=http://localhost:5000</p>
                <button class="button success" onclick="openApp()">🚀 Open App</button>
            </div>
        </div>
        <div id="backend-test-results"></div>
    </div>

    <div class="fix-container">
        <h2>🔧 CRITICAL FIX: S3 CORS Policy Update</h2>
        
        <div class="error">
            <h3>🚨 Immediate Action Required</h3>
            <p>The S3 bucket <strong>icudatasetphrasesfortesting</strong> must be updated with the correct CORS policy to allow uploads from localhost:3003</p>
        </div>

        <div class="step">
            <h3>Step 1: Access AWS S3 Console</h3>
            <ol>
                <li>Go to <a href="https://s3.console.aws.amazon.com/s3/buckets/icudatasetphrasesfortesting" target="_blank" class="button">🔗 Open S3 Console</a></li>
                <li>Navigate to bucket: <strong>icudatasetphrasesfortesting</strong></li>
                <li>Click "Permissions" tab</li>
                <li>Find "Cross-origin resource sharing (CORS)" section</li>
                <li>Click "Edit"</li>
            </ol>
        </div>

        <div class="step">
            <h3>Step 2: Replace CORS Configuration</h3>
            <p><strong>Copy this exact CORS policy:</strong></p>
            <div class="cors-policy" id="cors-policy">
[
    {
        "AllowedHeaders": [
            "*"
        ],
        "AllowedMethods": [
            "GET",
            "PUT",
            "POST",
            "DELETE",
            "HEAD"
        ],
        "AllowedOrigins": [
            "http://localhost:3000",
            "http://localhost:3001", 
            "http://localhost:3002",
            "http://localhost:3003",
            "http://localhost:3004",
            "http://127.0.0.1:3000",
            "http://127.0.0.1:3001",
            "http://127.0.0.1:3002", 
            "http://127.0.0.1:3003",
            "https://icuphrasecollection.com",
            "https://*.netlify.app"
        ],
        "ExposeHeaders": [
            "ETag",
            "x-amz-meta-custom-header",
            "x-amz-request-id"
        ],
        "MaxAgeSeconds": 3000
    }
]
            </div>
            <button class="button" onclick="copyCorsPolicy()">📋 Copy CORS Policy</button>
        </div>

        <div class="step">
            <h3>Step 3: Save and Test</h3>
            <ol>
                <li>Paste the policy in S3 CORS editor</li>
                <li>Click "Save changes"</li>
                <li>Wait 1-2 minutes for propagation</li>
                <li>Test upload functionality</li>
            </ol>
            <button class="button warning" onclick="testS3Cors()">🧪 Test S3 CORS</button>
        </div>
    </div>

    <div class="fix-container">
        <h2>🔄 Alternative Solution: Backend Upload Mode</h2>
        
        <div class="warning">
            <h3>⚠️ If S3 CORS Fix Doesn't Work</h3>
            <p>We can switch to backend-proxied uploads to bypass CORS entirely.</p>
        </div>

        <div class="step">
            <h3>Backend Upload Mode Benefits:</h3>
            <ul>
                <li>✅ No CORS issues (backend-to-S3 communication)</li>
                <li>✅ Better security (credentials stay on server)</li>
                <li>✅ Upload progress tracking</li>
                <li>✅ Error handling and retry logic</li>
            </ul>
            <button class="button danger" onclick="implementBackendMode()">🔄 Implement Backend Upload Mode</button>
        </div>
    </div>

    <div class="fix-container">
        <h2>🧪 Test Results</h2>
        <div id="test-results">
            <p>Click the test buttons above to verify connectivity...</p>
        </div>
    </div>

    <script>
        async function testBackend() {
            const resultsDiv = document.getElementById('backend-test-results');
            resultsDiv.innerHTML = '<p>🧪 Testing backend connectivity...</p>';
            
            try {
                const response = await fetch('http://localhost:5000/health');
                const data = await response.json();
                
                resultsDiv.innerHTML = `
                    <div class="success">
                        <h4>✅ Backend Test PASSED!</h4>
                        <p><strong>Status:</strong> ${data.status}</p>
                        <p><strong>Server:</strong> ${data.services.server}</p>
                        <p><strong>AWS:</strong> ${data.services.aws}</p>
                        <p><strong>Storage:</strong> ${data.services.storage}</p>
                    </div>
                `;
            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ Backend Test FAILED!</h4>
                        <p><strong>Error:</strong> ${error.message}</p>
                    </div>
                `;
            }
        }

        async function testS3Cors() {
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.innerHTML = '<p>🧪 Testing S3 CORS configuration...</p>';
            
            try {
                // Test CORS preflight
                const response = await fetch('https://icudatasetphrasesfortesting.s3.ap-southeast-2.amazonaws.com/', {
                    method: 'OPTIONS',
                    headers: {
                        'Origin': 'http://localhost:3003',
                        'Access-Control-Request-Method': 'PUT',
                        'Access-Control-Request-Headers': 'Content-Type'
                    }
                });
                
                resultsDiv.innerHTML = `
                    <div class="success">
                        <h4>✅ S3 CORS Test Result</h4>
                        <p><strong>Status:</strong> ${response.status} ${response.statusText}</p>
                        <p><strong>Next:</strong> Try actual video upload in the application</p>
                    </div>
                `;
                
            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="warning">
                        <h4>⚠️ S3 CORS Test Inconclusive</h4>
                        <p><strong>Error:</strong> ${error.message}</p>
                        <p><strong>Note:</strong> This might be expected for OPTIONS requests. Try actual upload.</p>
                    </div>
                `;
            }
        }

        function copyCorsPolicy() {
            const corsText = document.getElementById('cors-policy').textContent;
            navigator.clipboard.writeText(corsText).then(() => {
                alert('✅ CORS policy copied to clipboard! Paste it in the S3 console.');
            });
        }

        function openApp() {
            window.open('http://localhost:3003', '_blank');
        }

        function implementBackendMode() {
            if (confirm('Switch to backend upload mode? This will modify the application to use server-side uploads instead of direct S3 uploads.')) {
                alert('Backend upload mode implementation requires code changes. Please confirm if you want to proceed.');
            }
        }

        // Auto-test backend on load
        window.addEventListener('load', () => {
            setTimeout(testBackend, 1000);
        });
    </script>
</body>
</html>
