<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Receipt-Video Mapping Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>🧾 Receipt-Video Mapping System Test</h1>
    
    <div class="test-section">
        <h2>📋 Test 1: Receipt Number Generation</h2>
        <button onclick="testReceiptGeneration()">Run Test</button>
        <div id="receipt-test-results"></div>
    </div>

    <div class="test-section">
        <h2>🔗 Test 2: AWS S3 Connection</h2>
        <button onclick="testAWSConnection()">Test Connection</button>
        <div id="aws-test-results"></div>
    </div>

    <div class="test-section">
        <h2>📹 Test 3: Receipt-Video Mapping</h2>
        <button onclick="testReceiptMapping()">Test Mapping</button>
        <div id="mapping-test-results"></div>
    </div>

    <div class="test-section">
        <h2>🚀 Test 4: Complete Workflow</h2>
        <button onclick="testCompleteWorkflow()">Run Complete Test</button>
        <div id="workflow-test-results"></div>
    </div>

    <script>
        // Test receipt number generation
        function testReceiptGeneration() {
            const resultsDiv = document.getElementById('receipt-test-results');
            resultsDiv.innerHTML = '<div class="info">Running receipt generation test...</div>';
            
            try {
                // Clear counter for clean test
                localStorage.removeItem('icuAppReceiptCounter');
                
                const generateReceiptNumber = () => {
                    try {
                        const currentCounter = parseInt(localStorage.getItem('icuAppReceiptCounter') || '0', 10);
                        const nextCounter = currentCounter + 1;
                        localStorage.setItem('icuAppReceiptCounter', nextCounter.toString());
                        return nextCounter.toString().padStart(6, '0');
                    } catch (error) {
                        console.warn('Error generating receipt number:', error);
                        return Date.now().toString().slice(-6);
                    }
                };

                const receipts = [];
                for (let i = 0; i < 3; i++) {
                    receipts.push(generateReceiptNumber());
                }

                const isSequential = receipts.every((num, index) => {
                    const expected = (index + 1).toString().padStart(6, '0');
                    return num === expected;
                });

                resultsDiv.innerHTML = `
                    <div class="${isSequential ? 'success' : 'error'}">
                        <strong>Receipt Generation Test:</strong><br>
                        Generated: ${receipts.join(', ')}<br>
                        Sequential: ${isSequential ? '✅ PASS' : '❌ FAIL'}<br>
                        Format: ${receipts[0].length === 6 ? '✅ 6-digit format' : '❌ Wrong format'}
                    </div>
                `;
            } catch (error) {
                resultsDiv.innerHTML = `<div class="error">❌ Test failed: ${error.message}</div>`;
            }
        }

        // Test AWS connection
        function testAWSConnection() {
            const resultsDiv = document.getElementById('aws-test-results');
            resultsDiv.innerHTML = '<div class="info">Testing AWS S3 connection...</div>';
            
            // Check environment variables
            const awsConfig = {
                region: 'ap-southeast-2',
                bucket: 'icudatasetphrasesfortesting',
                identityPoolId: 'ap-southeast-2:59baeaf9-a80c-4afc-8f27-f4d6191242dd'
            };

            const configStatus = awsConfig.identityPoolId ? 'Configured' : 'Missing';
            
            resultsDiv.innerHTML = `
                <div class="${configStatus === 'Configured' ? 'success' : 'error'}">
                    <strong>AWS Configuration:</strong><br>
                    Region: ${awsConfig.region}<br>
                    Bucket: ${awsConfig.bucket}<br>
                    Identity Pool: ${configStatus}<br>
                    Status: ${configStatus === 'Configured' ? '✅ Ready for S3 operations' : '❌ Configuration incomplete'}
                </div>
            `;
        }

        // Test receipt mapping structure
        function testReceiptMapping() {
            const resultsDiv = document.getElementById('mapping-test-results');
            resultsDiv.innerHTML = '<div class="info">Testing receipt-video mapping structure...</div>';
            
            try {
                const mockReceiptEntry = {
                    timestamp: new Date().toISOString(),
                    videos: [
                        's3://icudatasetphrasesfortesting/icu-videos/18to39/male/mixed/phrase1/video1.webm',
                        's3://icudatasetphrasesfortesting/icu-videos/18to39/male/mixed/phrase2/video2.webm'
                    ],
                    demographics: {
                        age: '18to39',
                        gender: 'male',
                        ethnicity: 'mixed'
                    },
                    sessionId: 'test_session_001',
                    assignmentType: 'prospective',
                    recordingCount: 2
                };

                const requiredFields = ['timestamp', 'videos', 'demographics', 'sessionId', 'assignmentType', 'recordingCount'];
                const hasAllFields = requiredFields.every(field => mockReceiptEntry.hasOwnProperty(field));
                
                resultsDiv.innerHTML = `
                    <div class="${hasAllFields ? 'success' : 'error'}">
                        <strong>Receipt Mapping Structure:</strong><br>
                        Required fields: ${hasAllFields ? '✅ All present' : '❌ Missing fields'}<br>
                        Video count: ${mockReceiptEntry.videos.length}<br>
                        Demographics: ${Object.keys(mockReceiptEntry.demographics).join(', ')}<br>
                        <pre>${JSON.stringify(mockReceiptEntry, null, 2)}</pre>
                    </div>
                `;
            } catch (error) {
                resultsDiv.innerHTML = `<div class="error">❌ Test failed: ${error.message}</div>`;
            }
        }

        // Test complete workflow
        function testCompleteWorkflow() {
            const resultsDiv = document.getElementById('workflow-test-results');
            resultsDiv.innerHTML = '<div class="info">Running complete workflow test...</div>';
            
            try {
                // Step 1: Generate receipt
                localStorage.removeItem('icuAppReceiptCounter');
                const receiptNumber = (() => {
                    const currentCounter = parseInt(localStorage.getItem('icuAppReceiptCounter') || '0', 10);
                    const nextCounter = currentCounter + 1;
                    localStorage.setItem('icuAppReceiptCounter', nextCounter.toString());
                    return nextCounter.toString().padStart(6, '0');
                })();

                // Step 2: Mock video data
                const mockVideos = [
                    { url: 's3://icudatasetphrasesfortesting/icu-videos/18to39/male/mixed/phrase1/video1.webm' },
                    { url: 's3://icudatasetphrasesfortesting/icu-videos/18to39/male/mixed/phrase2/video2.webm' }
                ];

                // Step 3: Mock demographics
                const mockDemographics = {
                    ageGroup: '18to39',
                    gender: 'male',
                    ethnicity: 'mixed',
                    userId: 'test_user_001'
                };

                // Step 4: Create receipt mapping structure
                const receiptMapping = {
                    [receiptNumber]: {
                        timestamp: new Date().toISOString(),
                        videos: mockVideos.map(v => v.url),
                        demographics: {
                            age: mockDemographics.ageGroup,
                            gender: mockDemographics.gender,
                            ethnicity: mockDemographics.ethnicity
                        },
                        sessionId: mockDemographics.userId,
                        assignmentType: 'prospective',
                        recordingCount: mockVideos.length
                    }
                };

                resultsDiv.innerHTML = `
                    <div class="success">
                        <strong>Complete Workflow Test:</strong><br>
                        ✅ Receipt generated: ${receiptNumber}<br>
                        ✅ Videos mapped: ${mockVideos.length} files<br>
                        ✅ Demographics captured<br>
                        ✅ Receipt mapping created<br>
                        ✅ Ready for S3 storage<br>
                        <pre>${JSON.stringify(receiptMapping, null, 2)}</pre>
                    </div>
                `;
            } catch (error) {
                resultsDiv.innerHTML = `<div class="error">❌ Workflow test failed: ${error.message}</div>`;
            }
        }

        // Auto-run basic tests on page load
        window.onload = function() {
            testReceiptGeneration();
            testAWSConnection();
        };
    </script>
</body>
</html>
