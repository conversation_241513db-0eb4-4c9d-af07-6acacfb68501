<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Training Video Diagnostic Test</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .test-section { border: 1px solid #ddd; margin: 20px 0; padding: 20px; border-radius: 8px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        button { padding: 10px 20px; margin: 10px 5px; border: none; border-radius: 4px; cursor: pointer; background-color: #007bff; color: white; }
        .status { font-weight: bold; margin: 10px 0; }
        video { width: 100%; max-width: 600px; border: 2px solid #007bff; border-radius: 8px; }
        .log { background-color: #f8f9fa; padding: 10px; border-radius: 4px; font-family: monospace; margin: 10px 0; max-height: 200px; overflow-y: auto; }
    </style>
</head>
<body>
    <h1>🔍 Training Video Diagnostic Test</h1>
    
    <div class="test-section">
        <h2>📹 Direct Video Test</h2>
        <p>Testing direct access to training video files:</p>
        
        <h3>Primary Training Video:</h3>
        <video id="primaryVideo" controls>
            <source src="/videos/training-video.mp4" type="video/mp4">
            Your browser does not support the video tag.
        </video>
        <div id="primaryStatus" class="status">Loading...</div>
        
        <h3>Backup Training Video:</h3>
        <video id="backupVideo" controls>
            <source src="/videos/training-video-backup.mp4" type="video/mp4">
            Your browser does not support the video tag.
        </video>
        <div id="backupStatus" class="status">Loading...</div>
    </div>

    <div class="test-section">
        <h2>🔗 File Accessibility Test</h2>
        <button onclick="testFileAccess()">Test Video File URLs</button>
        <div id="accessResults" class="log"></div>
    </div>

    <div class="test-section">
        <h2>🧪 Application Navigation Test</h2>
        <p>Test the complete workflow navigation:</p>
        <button onclick="testWorkflow()">Test Workflow Navigation</button>
        <div id="workflowResults" class="log"></div>
    </div>

    <div class="test-section">
        <h2>📊 Console Logs</h2>
        <button onclick="clearLogs()">Clear Logs</button>
        <div id="consoleLogs" class="log"></div>
    </div>

    <script>
        // Capture console logs
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        function addToLog(message, type = 'log') {
            const logsDiv = document.getElementById('consoleLogs');
            const timestamp = new Date().toLocaleTimeString();
            logsDiv.innerHTML += `<div style="color: ${type === 'error' ? 'red' : type === 'warn' ? 'orange' : 'black'}">[${timestamp}] ${message}</div>`;
            logsDiv.scrollTop = logsDiv.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addToLog(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addToLog('ERROR: ' + args.join(' '), 'error');
        };
        
        console.warn = function(...args) {
            originalWarn.apply(console, args);
            addToLog('WARN: ' + args.join(' '), 'warn');
        };

        // Test video loading
        function setupVideoTests() {
            const primaryVideo = document.getElementById('primaryVideo');
            const backupVideo = document.getElementById('backupVideo');
            const primaryStatus = document.getElementById('primaryStatus');
            const backupStatus = document.getElementById('backupStatus');

            // Primary video events
            primaryVideo.addEventListener('loadeddata', () => {
                primaryStatus.innerHTML = '✅ Primary video loaded successfully';
                primaryStatus.className = 'status success';
                console.log('Primary training video loaded successfully');
            });

            primaryVideo.addEventListener('error', (e) => {
                primaryStatus.innerHTML = '❌ Primary video failed to load';
                primaryStatus.className = 'status error';
                console.error('Primary training video error:', e);
            });

            primaryVideo.addEventListener('canplay', () => {
                console.log('Primary video can play - duration:', primaryVideo.duration);
            });

            // Backup video events
            backupVideo.addEventListener('loadeddata', () => {
                backupStatus.innerHTML = '✅ Backup video loaded successfully';
                backupStatus.className = 'status success';
                console.log('Backup training video loaded successfully');
            });

            backupVideo.addEventListener('error', (e) => {
                backupStatus.innerHTML = '❌ Backup video failed to load';
                backupStatus.className = 'status error';
                console.error('Backup training video error:', e);
            });

            backupVideo.addEventListener('canplay', () => {
                console.log('Backup video can play - duration:', backupVideo.duration);
            });
        }

        // Test file accessibility
        async function testFileAccess() {
            const resultsDiv = document.getElementById('accessResults');
            resultsDiv.innerHTML = 'Testing file accessibility...\n';

            const files = [
                '/videos/training-video.mp4',
                '/videos/training-video-backup.mp4'
            ];

            for (const file of files) {
                try {
                    const response = await fetch(file, { method: 'HEAD' });
                    if (response.ok) {
                        const size = response.headers.get('content-length');
                        resultsDiv.innerHTML += `✅ ${file} - Status: ${response.status}, Size: ${size} bytes\n`;
                        console.log(`File accessible: ${file} (${size} bytes)`);
                    } else {
                        resultsDiv.innerHTML += `❌ ${file} - Status: ${response.status}\n`;
                        console.error(`File not accessible: ${file} - Status: ${response.status}`);
                    }
                } catch (error) {
                    resultsDiv.innerHTML += `❌ ${file} - Error: ${error.message}\n`;
                    console.error(`File access error: ${file}`, error);
                }
            }
        }

        // Test workflow navigation
        function testWorkflow() {
            const resultsDiv = document.getElementById('workflowResults');
            resultsDiv.innerHTML = 'Testing workflow navigation...\n';

            // Check if we're in the main application
            if (window.location.pathname === '/') {
                resultsDiv.innerHTML += '✅ On main application page\n';
                resultsDiv.innerHTML += '📝 To test training video:\n';
                resultsDiv.innerHTML += '1. Go to main application (http://localhost:8080)\n';
                resultsDiv.innerHTML += '2. Complete consent form\n';
                resultsDiv.innerHTML += '3. Fill demographics form\n';
                resultsDiv.innerHTML += '4. Training video should appear\n';
                resultsDiv.innerHTML += '5. Check for video loading and controls\n';
            } else {
                resultsDiv.innerHTML += '⚠️ Not on main application page\n';
                resultsDiv.innerHTML += 'Navigate to http://localhost:8080 to test full workflow\n';
            }

            console.log('Workflow test completed - check main application');
        }

        function clearLogs() {
            document.getElementById('consoleLogs').innerHTML = '';
        }

        // Initialize tests
        window.onload = function() {
            setupVideoTests();
            console.log('Training video diagnostic page loaded');
            console.log('Testing video file accessibility...');
            testFileAccess();
        };
    </script>
</body>
</html>
