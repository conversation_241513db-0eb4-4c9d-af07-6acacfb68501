<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>S3 Connectivity Diagnostic</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 1000px; margin: 0 auto; padding: 20px; }
        .test-section { border: 1px solid #ddd; margin: 20px 0; padding: 20px; border-radius: 8px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button { padding: 10px 20px; margin: 10px 5px; border: none; border-radius: 4px; cursor: pointer; background-color: #007bff; color: white; }
        .log { background-color: #f8f9fa; padding: 10px; border-radius: 4px; font-family: monospace; margin: 10px 0; max-height: 300px; overflow-y: auto; white-space: pre-wrap; }
        .status { font-weight: bold; margin: 10px 0; }
        .cors-policy { background-color: #f8f9fa; padding: 15px; border-radius: 4px; font-family: monospace; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>🔍 S3 Connectivity Diagnostic for ICU Dataset Application</h1>
    
    <div class="test-section info">
        <h2>📋 Current Issue</h2>
        <p><strong>Problem:</strong> S3 progress display showing "using cached data: failed to fetch s3 data: failed to fetch"</p>
        <p><strong>Expected:</strong> Real-time progress data from S3 bucket "icudatasetphrasesfortesting"</p>
        <p><strong>Domain:</strong> icuphrasecollection.com</p>
    </div>

    <div class="test-section">
        <h2>🔧 1. Environment Configuration Test</h2>
        <button onclick="testEnvironmentConfig()">Test Environment Variables</button>
        <div id="env-results" class="log"></div>
    </div>

    <div class="test-section">
        <h2>🌐 2. Backend API Connectivity Test</h2>
        <button onclick="testBackendAPI()">Test Backend API</button>
        <div id="backend-results" class="log"></div>
    </div>

    <div class="test-section">
        <h2>☁️ 3. Direct S3 Access Test</h2>
        <button onclick="testDirectS3Access()">Test Direct S3 Access</button>
        <div id="s3-results" class="log"></div>
    </div>

    <div class="test-section">
        <h2>📊 4. S3 Progress Service Test</h2>
        <button onclick="testS3ProgressService()">Test S3 Progress Service</button>
        <div id="progress-results" class="log"></div>
    </div>

    <div class="test-section warning">
        <h2>🔧 Required CORS Policy for S3 Bucket</h2>
        <p>Apply this CORS policy to your S3 bucket "icudatasetphrasesfortesting":</p>
        <div class="cors-policy">
[
    {
        "AllowedHeaders": ["*"],
        "AllowedMethods": ["GET", "PUT", "POST", "DELETE", "HEAD"],
        "AllowedOrigins": [
            "http://localhost:3000",
            "http://localhost:3001", 
            "http://localhost:8080",
            "https://*.netlify.app",
            "https://icuphrasecollection.com",
            "http://icuphrasecollection.com",
            "https://www.icuphrasecollection.com",
            "http://www.icuphrasecollection.com"
        ],
        "ExposeHeaders": [
            "ETag",
            "x-amz-server-side-encryption",
            "x-amz-request-id",
            "x-amz-id-2",
            "x-amz-version-id"
        ],
        "MaxAgeSeconds": 3000
    }
]
        </div>
        <button onclick="copyCORSPolicy()">Copy CORS Policy</button>
    </div>

    <div class="test-section">
        <h2>📝 Console Logs</h2>
        <button onclick="clearLogs()">Clear Logs</button>
        <div id="console-logs" class="log"></div>
    </div>

    <script>
        // Capture console logs
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        function addToLog(message, type = 'log') {
            const logsDiv = document.getElementById('console-logs');
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? 'red' : type === 'warn' ? 'orange' : 'black';
            logsDiv.innerHTML += `<span style="color: ${color}">[${timestamp}] ${message}</span>\n`;
            logsDiv.scrollTop = logsDiv.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addToLog(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addToLog('ERROR: ' + args.join(' '), 'error');
        };
        
        console.warn = function(...args) {
            originalWarn.apply(console, args);
            addToLog('WARN: ' + args.join(' '), 'warn');
        };

        function testEnvironmentConfig() {
            const resultsDiv = document.getElementById('env-results');
            resultsDiv.innerHTML = 'Testing environment configuration...\n';
            
            const envVars = {
                'AWS Identity Pool': process.env.REACT_APP_AWS_IDENTITY_POOL_ID,
                'AWS Region': process.env.REACT_APP_AWS_REGION,
                'S3 Bucket': process.env.REACT_APP_S3_BUCKET,
                'Backend URL': process.env.REACT_APP_BACKEND_URL,
                'Node Environment': process.env.NODE_ENV
            };
            
            let results = '';
            let allConfigured = true;
            
            for (const [key, value] of Object.entries(envVars)) {
                const status = value ? '✅' : '❌';
                results += `${status} ${key}: ${value || 'NOT SET'}\n`;
                if (!value && key !== 'Backend URL') allConfigured = false;
            }
            
            if (allConfigured) {
                results += '\n✅ AWS configuration looks good for frontend S3 access!';
            } else {
                results += '\n❌ Missing required AWS environment variables.';
            }
            
            resultsDiv.innerHTML = results;
            console.log('Environment configuration tested');
        }
        
        async function testBackendAPI() {
            const resultsDiv = document.getElementById('backend-results');
            resultsDiv.innerHTML = 'Testing backend API connectivity...\n';
            
            const backendUrl = process.env.REACT_APP_BACKEND_URL || 'http://localhost:5000';
            
            try {
                // Test health endpoint
                resultsDiv.innerHTML += `Testing: ${backendUrl}/health\n`;
                const healthResponse = await fetch(`${backendUrl}/health`);
                const healthData = await healthResponse.json();
                
                resultsDiv.innerHTML += `✅ Health check: ${healthData.status}\n`;
                
                // Test sample counts endpoint
                resultsDiv.innerHTML += `Testing: ${backendUrl}/api/sample-counts\n`;
                const countsResponse = await fetch(`${backendUrl}/api/sample-counts`);
                const countsData = await countsResponse.json();
                
                if (countsData.success) {
                    resultsDiv.innerHTML += `✅ Sample counts: ${countsData.counts.total} total recordings\n`;
                } else {
                    resultsDiv.innerHTML += `❌ Sample counts failed: ${countsData.error}\n`;
                }
                
            } catch (error) {
                resultsDiv.innerHTML += `❌ Backend API error: ${error.message}\n`;
                resultsDiv.innerHTML += `Note: This is expected on Netlify (frontend-only deployment)\n`;
                console.error('Backend API test failed:', error);
            }
        }
        
        async function testDirectS3Access() {
            const resultsDiv = document.getElementById('s3-results');
            resultsDiv.innerHTML = 'Testing direct S3 access...\n';
            
            // Check if AWS SDK is available
            if (typeof AWS === 'undefined') {
                resultsDiv.innerHTML += '⚠️ AWS SDK not loaded. Testing with fetch API...\n';
                
                // Test CORS with a simple HEAD request to S3
                const bucketUrl = 'https://icudatasetphrasesfortesting.s3.ap-southeast-2.amazonaws.com';
                
                try {
                    const response = await fetch(bucketUrl, { method: 'HEAD' });
                    resultsDiv.innerHTML += `✅ S3 bucket accessible: ${response.status}\n`;
                } catch (error) {
                    resultsDiv.innerHTML += `❌ S3 CORS error: ${error.message}\n`;
                    resultsDiv.innerHTML += `This indicates CORS policy needs to be updated\n`;
                }
            } else {
                resultsDiv.innerHTML += '✅ AWS SDK available for direct S3 access\n';
                // Add AWS SDK tests here if needed
            }
        }
        
        async function testS3ProgressService() {
            const resultsDiv = document.getElementById('progress-results');
            resultsDiv.innerHTML = 'Testing S3 Progress Service...\n';
            
            try {
                // Simulate the s3ProgressService call
                const backendUrl = process.env.REACT_APP_BACKEND_URL || 'http://localhost:5000';
                const isProduction = process.env.NODE_ENV === 'production';
                const hasBackend = !!process.env.REACT_APP_BACKEND_URL;
                
                resultsDiv.innerHTML += `Environment: ${isProduction ? 'production' : 'development'}\n`;
                resultsDiv.innerHTML += `Backend configured: ${hasBackend}\n`;
                resultsDiv.innerHTML += `Backend URL: ${backendUrl}\n`;
                
                if (isProduction && !hasBackend) {
                    resultsDiv.innerHTML += `⚠️ Production mode without backend - will use mock data\n`;
                } else {
                    resultsDiv.innerHTML += `🔄 Attempting to fetch from backend API...\n`;
                    
                    const response = await fetch(`${backendUrl}/api/sample-counts`);
                    const data = await response.json();
                    
                    if (data.success) {
                        resultsDiv.innerHTML += `✅ S3 data fetched successfully\n`;
                        resultsDiv.innerHTML += `Total recordings: ${data.counts.total}\n`;
                        resultsDiv.innerHTML += `Data source: ${data.source || 'backend-api'}\n`;
                    } else {
                        resultsDiv.innerHTML += `❌ S3 fetch failed: ${data.error}\n`;
                    }
                }
                
            } catch (error) {
                resultsDiv.innerHTML += `❌ S3 Progress Service error: ${error.message}\n`;
                console.error('S3 Progress Service test failed:', error);
            }
        }
        
        function copyCORSPolicy() {
            const corsPolicy = `[
    {
        "AllowedHeaders": ["*"],
        "AllowedMethods": ["GET", "PUT", "POST", "DELETE", "HEAD"],
        "AllowedOrigins": [
            "http://localhost:3000",
            "http://localhost:3001", 
            "http://localhost:8080",
            "https://*.netlify.app",
            "https://icuphrasecollection.com",
            "http://icuphrasecollection.com",
            "https://www.icuphrasecollection.com",
            "http://www.icuphrasecollection.com"
        ],
        "ExposeHeaders": [
            "ETag",
            "x-amz-server-side-encryption",
            "x-amz-request-id",
            "x-amz-id-2",
            "x-amz-version-id"
        ],
        "MaxAgeSeconds": 3000
    }
]`;
            
            navigator.clipboard.writeText(corsPolicy).then(() => {
                alert('CORS policy copied to clipboard!');
            });
        }
        
        function clearLogs() {
            document.getElementById('console-logs').innerHTML = '';
        }
        
        // Auto-run environment test on page load
        window.onload = function() {
            console.log('S3 Connectivity Diagnostic loaded');
            testEnvironmentConfig();
        };
    </script>
</body>
</html>
