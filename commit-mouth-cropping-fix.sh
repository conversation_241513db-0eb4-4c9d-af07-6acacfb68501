#!/bin/bash

# ICU Dataset Application - Mouth Cropping Enhancement Commit
echo "🔄 === COMMITTING MOUTH CROPPING ENHANCEMENTS ==="
echo ""

echo "1️⃣ Adding VideoRecorder.js changes..."
git add src/components/VideoRecorder.js

echo ""
echo "2️⃣ Creating commit for mouth cropping improvements..."
git commit -m "Enhance video mouth cropping for complete upper lip capture

- Moved lip center position from 41% to 38% down from top (3% upward shift)
- Increased vertical capture area by 10% for better upper lip visibility
- Adjusted privacy boundary from 60% to 57% to accommodate larger capture area
- Enhanced all mouth cropping sections consistently across VideoRecorder.js
- Maintains 150×75 pixel LipNet compatibility and 25fps frame rate
- Preserves privacy compliance by excluding eyes/upper face area
- Ensures complete upper lip capture without cutting off top portion

Technical improvements:
- Enhanced lip positioning logic in main drawing function
- Updated enhanced mouth detection with improved vertical capture
- Modified canvas initialization with larger vertical area
- Improved stream creation with better upper lip focus
- Updated forced redraw section with consistent parameters

Benefits:
- Complete upper lip visibility in saved video files
- Maintained mouth-only privacy compliance
- Better LipNet preprocessing compatibility
- Consistent cropping across all recording scenarios
- Optimized for training event deployment with mobile networks"

echo ""
echo "3️⃣ Checking git status..."
git status --short

echo ""
echo "4️⃣ Checking for remote repository..."
if git remote | grep -q origin; then
    echo "5️⃣ Pushing to GitHub..."
    CURRENT_BRANCH=$(git branch --show-current)
    echo "Current branch: $CURRENT_BRANCH"
    
    if git push origin $CURRENT_BRANCH; then
        echo "✅ Successfully pushed mouth cropping enhancements to GitHub!"
        echo ""
        echo "🎉 === BACKUP COMPLETED SUCCESSFULLY ==="
        echo "📊 Mouth cropping improvements backed up to GitHub"
        echo "🔗 Repository: $(git remote get-url origin)"
    else
        echo "❌ Push failed. Commit created locally."
        echo "💡 Run: git push origin $CURRENT_BRANCH"
    fi
else
    echo "⚠️ No remote repository configured"
    echo "💡 Add remote: git remote add origin https://github.com/username/repo.git"
    echo "📋 Commit created locally"
fi

echo ""
echo "📋 === SUMMARY ==="
echo "✅ VideoRecorder.js mouth cropping enhanced"
echo "✅ Upper lip capture improved by 10% vertical expansion"
echo "✅ Privacy compliance maintained (excludes eyes/upper face)"
echo "✅ LipNet compatibility preserved (150×75 pixels, 25fps)"
echo "✅ All cropping sections updated consistently"

echo ""
echo "🔧 === TESTING RECOMMENDATIONS ==="
echo "1. Test video recording with enhanced cropping"
echo "2. Verify complete upper lip visibility in saved files"
echo "3. Confirm privacy compliance (no eyes visible)"
echo "4. Check 25fps frame rate consistency"
echo "5. Validate 150×75 pixel output dimensions"

echo ""
echo "🚀 === READY FOR DEPLOYMENT ==="
echo "Enhanced mouth cropping ready for training events!"
