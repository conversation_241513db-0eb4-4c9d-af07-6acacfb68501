/**
 * Test Auto-Advance Functionality
 * This script tests the auto-advance logic in isolation
 */

// Mock data for testing
const mockPhrases = [
  { id: 1, phrase: "Hello", category: "Greetings" },
  { id: 2, phrase: "Thank you", category: "Greetings" },
  { id: 3, phrase: "I need help", category: "Medical" }
];

const RECORDINGS_PER_PHRASE = 3;

// Mock state
let mockState = {
  selectedPhrases: mockPhrases,
  currentPhraseIndex: 0,
  recordingsCount: {},
  RECORDINGS_PER_PHRASE: RECORDINGS_PER_PHRASE
};

// Mock dispatch function
const mockDispatch = (action) => {
  console.log('🔄 MOCK DISPATCH:', action);
  
  switch (action.type) {
    case 'UPDATE_RECORDINGS_COUNT':
      mockState.recordingsCount[action.payload.phraseKey] = action.payload.count;
      console.log('📊 Updated recordings count:', mockState.recordingsCount);
      break;
    case 'SET_CURRENT_PHRASE_INDEX':
      mockState.currentPhraseIndex = action.payload;
      console.log('📝 Updated phrase index:', mockState.currentPhraseIndex);
      break;
    case 'SET_COMPLETION_PROMPT':
      console.log('🏁 Completion prompt:', action.payload);
      break;
  }
};

// Auto-advance logic (copied from RecordingSessionProvider)
const testAutoAdvance = () => {
  console.log('🧪 TESTING AUTO-ADVANCE LOGIC');
  console.log('📊 Current state:', {
    currentPhraseIndex: mockState.currentPhraseIndex,
    recordingsCount: mockState.recordingsCount,
    selectedPhrases: mockState.selectedPhrases.map(p => p.phrase)
  });

  if (!mockState.selectedPhrases || mockState.selectedPhrases.length === 0 || mockState.currentPhraseIndex < 0) {
    console.log('🔄 AUTO-ADVANCE: Early return - no phrases or invalid index');
    return;
  }

  const currentPhraseObj = mockState.selectedPhrases[mockState.currentPhraseIndex];
  if (!currentPhraseObj) {
    console.log('🔄 AUTO-ADVANCE: Early return - no current phrase object');
    return;
  }

  const phraseKey = `${currentPhraseObj.category}:${currentPhraseObj.phrase}`;
  const currentCount = mockState.recordingsCount[phraseKey] || 0;

  console.log('🔄 AUTO-ADVANCE CHECK:', {
    phrase: currentPhraseObj.phrase,
    phraseKey,
    currentCount,
    required: mockState.RECORDINGS_PER_PHRASE,
    shouldAdvance: currentCount >= mockState.RECORDINGS_PER_PHRASE
  });

  if (currentCount >= mockState.RECORDINGS_PER_PHRASE) {
    console.log('🎯 AUTO-ADVANCE: Phrase completion detected');

    // Check if this is the last phrase
    if (mockState.currentPhraseIndex >= mockState.selectedPhrases.length - 1) {
      console.log('🏁 AUTO-ADVANCE: All phrases completed');
      mockDispatch({
        type: 'SET_COMPLETION_PROMPT',
        payload: true
      });
    } else {
      console.log('📝 AUTO-ADVANCE: Moving to next phrase');
      const nextPhraseIndex = mockState.currentPhraseIndex + 1;
      const nextPhrase = mockState.selectedPhrases[nextPhraseIndex];

      console.log('📝 AUTO-ADVANCE: Next phrase details:', {
        nextPhraseIndex,
        nextPhrase: nextPhrase?.phrase,
        nextCategory: nextPhrase?.category
      });

      // Update phrase index
      mockDispatch({
        type: 'SET_CURRENT_PHRASE_INDEX',
        payload: nextPhraseIndex
      });
    }
  } else {
    console.log('⏳ AUTO-ADVANCE: Not enough recordings yet');
  }
};

// Simulate recording completion
const simulateRecordingCompletion = (phrase, category) => {
  console.log(`\n🎬 SIMULATING RECORDING COMPLETION: "${phrase}" (${category})`);
  
  const phraseKey = `${category}:${phrase}`;
  const currentCount = mockState.recordingsCount[phraseKey] || 0;
  const newCount = currentCount + 1;

  console.log('📹 Recording completed:', {
    phrase,
    category,
    phraseKey,
    previousCount: currentCount,
    newCount
  });

  // Update recording count
  mockDispatch({
    type: 'UPDATE_RECORDINGS_COUNT',
    payload: { phraseKey, count: newCount }
  });

  // Test auto-advance logic
  testAutoAdvance();
};

// Run the test
console.log('🚀 STARTING AUTO-ADVANCE TEST\n');

// Test 1: Record 3 videos for first phrase
console.log('=== TEST 1: First phrase (3 recordings) ===');
simulateRecordingCompletion('Hello', 'Greetings');
simulateRecordingCompletion('Hello', 'Greetings');
simulateRecordingCompletion('Hello', 'Greetings'); // Should trigger auto-advance

// Test 2: Record 3 videos for second phrase
console.log('\n=== TEST 2: Second phrase (3 recordings) ===');
simulateRecordingCompletion('Thank you', 'Greetings');
simulateRecordingCompletion('Thank you', 'Greetings');
simulateRecordingCompletion('Thank you', 'Greetings'); // Should trigger auto-advance

// Test 3: Record 3 videos for third phrase (last phrase)
console.log('\n=== TEST 3: Third phrase (3 recordings) ===');
simulateRecordingCompletion('I need help', 'Medical');
simulateRecordingCompletion('I need help', 'Medical');
simulateRecordingCompletion('I need help', 'Medical'); // Should trigger completion

console.log('\n🏁 TEST COMPLETED');
console.log('📊 Final state:', {
  currentPhraseIndex: mockState.currentPhraseIndex,
  recordingsCount: mockState.recordingsCount
});

// Export for browser testing
if (typeof window !== 'undefined') {
  window.testAutoAdvance = {
    mockState,
    simulateRecordingCompletion,
    testAutoAdvance
  };
}
