<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 Real Auto-Advancement Fix Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .header {
            background: #dc3545;
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .step {
            background: #e9ecef;
            padding: 15px;
            margin: 10px 0;
            border-left: 4px solid #007bff;
            border-radius: 4px;
        }
        .console-output {
            background: #1e1e1e;
            color: #00ff00;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 11px;
            margin: 10px 0;
            max-height: 200px;
            overflow-y: auto;
        }
        .button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            text-decoration: none;
            display: inline-block;
            font-size: 16px;
        }
        .button.danger { background: #dc3545; }
        .button.success { background: #28a745; }
        .critical-fix {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔧 Testing Real Auto-Advancement Fix</h1>
        <p><strong>Issue:</strong> Recording page still skipping to completion page</p>
        <p><strong>Root Cause:</strong> handleNextPhrase called during initialization</p>
        <p><strong>Fix Applied:</strong> Added initialization guard to handleNextPhrase function</p>
    </div>

    <div class="test-container">
        <h2>🎯 What Was Actually Fixed This Time</h2>
        
        <div class="critical-fix">
            <h3>Critical Discovery:</h3>
            <p><strong>Real Problem:</strong> The <code>handleNextPhrase</code> function was being called during initialization and setting <code>showCompletionPrompt: true</code> when it found existing recording counts in localStorage.</p>
            
            <p><strong>Real Solution:</strong> Added initialization guard directly to <code>handleNextPhrase</code> function to prevent it from setting completion prompt during initialization.</p>
            
            <p><strong>Code Change:</strong></p>
            <div class="console-output">
// CRITICAL FIX: Prevent handleNextPhrase during initialization
if (state.isInitializing) {
  console.log('🔄 HANDLE NEXT PHRASE: Blocked during initialization - preventing false completion prompt');
  return;
}
            </div>
        </div>
    </div>

    <div class="test-container">
        <h2>🧪 Test the REAL Fix</h2>
        
        <div class="step">
            <h3>Step 1: Clear All Data</h3>
            <p>Start completely fresh to test the fix:</p>
            <button class="button danger" onclick="clearAllData()">🗑️ Clear All Data</button>
            <div id="clear-output" class="console-output">Click "Clear All Data" to reset...</div>
        </div>

        <div class="step">
            <h3>Step 2: Test with Fresh State</h3>
            <p>Test the application with no existing localStorage data:</p>
            <a href="http://localhost:3003" target="_blank" class="button success">🚀 Open Application (Fresh)</a>
            
            <div class="critical-fix">
                <h4>Expected Behavior (Fresh State):</h4>
                <ol>
                    <li>Demographics → Phrases → Recording page loads</li>
                    <li>Recording page stays visible (no auto-skip)</li>
                    <li>Console shows initialization guard messages</li>
                    <li>Manual recording interaction required</li>
                </ol>
            </div>
        </div>

        <div class="step">
            <h3>Step 3: Test with Existing Data</h3>
            <p>Add some test recording counts and test again:</p>
            <button class="button" onclick="addTestRecordingCounts()">📊 Add Test Recording Counts</button>
            <a href="http://localhost:3003" target="_blank" class="button success">🚀 Open Application (With Data)</a>
            
            <div class="critical-fix">
                <h4>Expected Behavior (With Existing Data):</h4>
                <ol>
                    <li>Application loads with existing recording counts</li>
                    <li>Initialization guard prevents handleNextPhrase</li>
                    <li>Recording page still loads and stays visible</li>
                    <li>No immediate completion prompt</li>
                </ol>
            </div>
        </div>
    </div>

    <div class="test-container">
        <h2>📊 Console Messages to Watch For</h2>
        
        <div class="step">
            <h3>Good Messages (Should See):</h3>
            <div class="console-output">
🔄 HANDLE NEXT PHRASE: Blocked during initialization - preventing false completion prompt
🔄 AUTO-ADVANCE: Blocked during initialization - preventing false positive advancement
✅ INITIALIZATION COMPLETE: Auto-advancement now enabled
✅ COMPLETION PROMPT: Reset to false to ensure recording page is visible
            </div>
        </div>

        <div class="step">
            <h3>Bad Messages (Should NOT See):</h3>
            <div class="console-output">
🏁 ALL PHRASES COMPLETED: Showing completion prompt
🚀 === HANDLE NEXT PHRASE CALLED === (during initialization)
🎯 AUTO-ADVANCE: Phrase completion detected (during initialization)
            </div>
        </div>
    </div>

    <div class="test-container">
        <h2>🔍 Debugging Tools</h2>
        
        <div class="step">
            <h3>Check Current State:</h3>
            <button class="button" onclick="checkCurrentState()">🔍 Check App State</button>
            <button class="button" onclick="checkRecordingCounts()">📈 Check Recording Counts</button>
            <div id="debug-output" class="console-output">Debug information will appear here...</div>
        </div>
    </div>

    <div class="test-container">
        <h2>✅ Success Criteria</h2>
        
        <div class="critical-fix">
            <h3>Fix is Working If:</h3>
            <ul>
                <li>☐ Recording page loads and stays visible (no auto-skip)</li>
                <li>☐ Console shows initialization guard messages</li>
                <li>☐ No "ALL PHRASES COMPLETED" message during initialization</li>
                <li>☐ Completion prompt is reset to false after initialization</li>
                <li>☐ Manual recording interaction is required</li>
                <li>☐ Works with both fresh state and existing localStorage data</li>
            </ul>
        </div>
    </div>

    <script>
        function clearAllData() {
            const output = document.getElementById('clear-output');
            output.innerHTML = '🗑️ Clearing all data...\n';
            
            // Clear localStorage
            const keys = ['icuAppRecordingsCount', 'icu_selected_phrases', 'icu_demographics'];
            keys.forEach(key => {
                if (localStorage.getItem(key)) {
                    localStorage.removeItem(key);
                    output.innerHTML += `✅ Removed: ${key}\n`;
                }
            });
            
            // Clear sessionStorage
            sessionStorage.clear();
            output.innerHTML += '✅ Cleared sessionStorage\n';
            
            output.innerHTML += '\n🎉 All data cleared! Ready for fresh test.\n';
        }

        function addTestRecordingCounts() {
            const output = document.getElementById('clear-output');
            output.innerHTML = '📊 Adding test recording counts...\n';
            
            // Add some test recording counts that would normally trigger completion
            const testCounts = {
                'Test Category:Test Phrase 1': 3,
                'Test Category:Test Phrase 2': 2
            };
            
            localStorage.setItem('icuAppRecordingsCount', JSON.stringify(testCounts));
            output.innerHTML += '✅ Added test recording counts\n';
            output.innerHTML += JSON.stringify(testCounts, null, 2) + '\n';
            output.innerHTML += '\n🧪 Test data added! This would normally trigger completion prompt.\n';
            output.innerHTML += 'If fix works, recording page should still be visible.\n';
        }

        function checkCurrentState() {
            const output = document.getElementById('debug-output');
            output.innerHTML = '🔍 Checking current application state...\n';
            
            // Check localStorage
            const recordingCounts = localStorage.getItem('icuAppRecordingsCount');
            const selectedPhrases = localStorage.getItem('icu_selected_phrases');
            
            output.innerHTML += '\n📱 localStorage Data:\n';
            output.innerHTML += `Recording Counts: ${recordingCounts || 'None'}\n`;
            output.innerHTML += `Selected Phrases: ${selectedPhrases || 'None'}\n`;
            
            // Check sessionStorage
            output.innerHTML += '\n🔄 sessionStorage Data:\n';
            for (let i = 0; i < sessionStorage.length; i++) {
                const key = sessionStorage.key(i);
                output.innerHTML += `${key}: ${sessionStorage.getItem(key)}\n`;
            }
        }

        function checkRecordingCounts() {
            const output = document.getElementById('debug-output');
            output.innerHTML = '📈 Analyzing recording counts...\n';
            
            const recordingCounts = localStorage.getItem('icuAppRecordingsCount');
            if (recordingCounts) {
                try {
                    const counts = JSON.parse(recordingCounts);
                    output.innerHTML += '\n📊 Current Recording Counts:\n';
                    for (const [phraseKey, count] of Object.entries(counts)) {
                        const status = count >= 3 ? 'COMPLETE ✅' : `${count}/3`;
                        output.innerHTML += `${phraseKey}: ${status}\n`;
                    }
                    
                    const completedCount = Object.values(counts).filter(count => count >= 3).length;
                    output.innerHTML += `\n📈 Summary: ${completedCount} completed phrases\n`;
                    
                    if (completedCount > 0) {
                        output.innerHTML += '⚠️ These completed phrases would normally trigger completion prompt\n';
                        output.innerHTML += 'If fix works, initialization guard should prevent this\n';
                    }
                } catch (e) {
                    output.innerHTML += `❌ Error parsing recording counts: ${e.message}\n`;
                }
            } else {
                output.innerHTML += 'No recording counts found\n';
            }
        }

        // Auto-check state on load
        window.addEventListener('load', () => {
            setTimeout(checkCurrentState, 500);
        });
    </script>
</body>
</html>
