<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>ICU Communication App</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      margin: 0;
      padding: 0;
      background-color: #f5f5f5;
      color: #333;
    }
    .container {
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
    }
    header {
      background-color: #009688;
      color: white;
      padding: 20px 0;
      text-align: center;
      margin-bottom: 20px;
    }
    h1 {
      margin: 0;
      font-size: 24px;
    }
    .subtitle {
      font-size: 16px;
      opacity: 0.9;
      margin-top: 5px;
    }
    .card {
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      padding: 20px;
      margin-bottom: 20px;
    }
    .categories {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: 15px;
      margin-top: 20px;
    }
    .category {
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      padding: 15px;
      cursor: pointer;
      transition: transform 0.2s, box-shadow 0.2s;
    }
    .category:hover {
      transform: translateY(-3px);
      box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    }
    .category h3 {
      margin-top: 0;
      color: #009688;
      border-bottom: 1px solid #eee;
      padding-bottom: 10px;
    }
    .category p {
      color: #666;
      font-size: 14px;
    }
    .phrase-display {
      font-size: 24px;
      text-align: center;
      padding: 30px 20px;
      background-color: #e0f2f1;
      border-radius: 8px;
      margin: 20px 0;
      border-left: 5px solid #009688;
    }
    .phrases-list {
      list-style: none;
      padding: 0;
      margin: 20px 0;
    }
    .phrase-item {
      padding: 12px 15px;
      border-bottom: 1px solid #eee;
      cursor: pointer;
    }
    .phrase-item:hover {
      background-color: #f9f9f9;
    }
    .phrase-item.active {
      background-color: #e0f2f1;
      border-left: 3px solid #009688;
    }
    .video-container {
      margin: 20px 0;
      text-align: center;
    }
    #video {
      width: 100%;
      max-width: 500px;
      border-radius: 8px;
      background-color: #000;
    }
    .controls {
      display: flex;
      justify-content: center;
      margin-top: 15px;
      gap: 10px;
    }
    button {
      background-color: #009688;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 16px;
      transition: background-color 0.2s;
    }
    button:hover {
      background-color: #00796b;
    }
    button.secondary {
      background-color: #f5f5f5;
      color: #333;
      border: 1px solid #ddd;
    }
    button.danger {
      background-color: #f44336;
    }
    button.danger:hover {
      background-color: #d32f2f;
    }
    button:disabled {
      background-color: #cccccc;
      cursor: not-allowed;
    }
    .recording-indicator {
      display: inline-block;
      width: 12px;
      height: 12px;
      background-color: #f44336;
      border-radius: 50%;
      margin-right: 8px;
      animation: pulse 1.5s infinite;
    }
    @keyframes pulse {
      0% { opacity: 1; }
      50% { opacity: 0.5; }
      100% { opacity: 1; }
    }
    .hidden {
      display: none;
    }
    .back-button {
      margin-bottom: 20px;
    }
    .consent-form {
      line-height: 1.6;
    }
    .consent-checkbox {
      margin: 20px 0;
    }
    .demographic-form {
      max-width: 600px;
      margin: 0 auto;
    }
    .form-group {
      margin-bottom: 20px;
    }
    .form-group label {
      display: block;
      margin-bottom: 8px;
      font-weight: bold;
    }
    .radio-group {
      display: flex;
      flex-direction: column;
      gap: 10px;
    }
    .radio-option {
      display: flex;
      align-items: center;
      padding: 10px;
      border: 1px solid #ddd;
      border-radius: 4px;
    }
    .radio-option:hover {
      background-color: #f9f9f9;
    }
    .radio-option input {
      margin-right: 10px;
    }
    .steps {
      display: flex;
      margin-bottom: 20px;
    }
    .step {
      flex: 1;
      text-align: center;
      padding: 10px;
      background-color: #f5f5f5;
      border-right: 1px solid white;
    }
    .step.active {
      background-color: #009688;
      color: white;
    }
    .error-message {
      color: #f44336;
      font-size: 14px;
      margin-top: 5px;
    }
    @media (max-width: 600px) {
      .container {
        padding: 10px;
      }
      h1 {
        font-size: 20px;
      }
      .phrase-display {
        font-size: 20px;
        padding: 20px 15px;
      }
      .categories {
        grid-template-columns: 1fr;
      }
    }
  </style>
</head>
<body>
  <header>
    <div class="container">
      <h1>ICU Communication Phrase Collection</h1>
      <div class="subtitle">Help train a lip-reading application for ICU patients</div>
    </div>
  </header>

  <div class="container" id="app">
    <!-- Consent Page -->
    <div id="consent-page">
      <div class="card consent-form">
        <h2>Participant Consent Form</h2>
        <h3>Research Purpose</h3>
        <p>Thank you for your interest in participating in our research project. The purpose of this study is to collect video samples of ICU-related phrases to train a lip-reading model based on the LipNet technology. This model will be used to assist patients in intensive care units who cannot use their voice to communicate.</p>
        
        <h3>What Your Participation Involves</h3>
        <p>You will be asked to record videos of yourself speaking specific phrases that are commonly used in intensive care settings. Each phrase will need to be recorded 5 times to provide sufficient training data for the model. The application will guide you through this process.</p>
        
        <h3>Data Usage and Privacy</h3>
        <p>The video recordings collected will be used solely for the purpose of training and improving the lip-reading model. Your recordings will be stored securely in an AWS cloud storage system and will only be accessible to the research team. All video recordings are anonymised and non-identifiable, focusing on the mouth region only. Personal identifiers will not be published in any research outputs.</p>
        
        <h3>Voluntary Participation</h3>
        <p>Your participation in this research is entirely voluntary. You must be 18 years old or over to participate. You may withdraw your consent at any time by contacting the research team, and your data will be removed from the study.</p>
        
        <div class="consent-checkbox">
          <label>
            <input type="checkbox" id="consent-checkbox"> I have read and understood the information provided above. I consent to my video recordings being used for training the lip-reading model for ICU applications.
          </label>
        </div>
        
        <button id="consent-button" disabled>I Consent - Proceed</button>
      </div>
    </div>
    
    <!-- Demographics Page -->
    <div id="demographics-page" class="hidden">
      <div class="card demographic-form">
        <h2>Demographic Information</h2>
        <p>Please provide the following information to help us categorise the data</p>
        
        <div class="steps">
          <div class="step active" id="step-1">Gender and Age</div>
          <div class="step" id="step-2">Ethnic Background</div>
        </div>
        
        <!-- Step 1: Gender and Age -->
        <div id="demographics-step-1">
          <div class="form-group">
            <label>Gender</label>
            <div class="radio-group" id="gender-options">
              <label class="radio-option">
                <input type="radio" name="gender" value="male"> Male
              </label>
              <label class="radio-option">
                <input type="radio" name="gender" value="female"> Female
              </label>
              <label class="radio-option">
                <input type="radio" name="gender" value="non-binary"> Non-binary
              </label>
            </div>
            <div class="error-message" id="gender-error"></div>
          </div>
          
          <div class="form-group">
            <label>Age Group</label>
            <div class="radio-group" id="age-options">
              <label class="radio-option">
                <input type="radio" name="age" value="18-49"> 18-49
              </label>
              <label class="radio-option">
                <input type="radio" name="age" value="50-69"> 50-69
              </label>
              <label class="radio-option">
                <input type="radio" name="age" value="70+"> 70+
              </label>
            </div>
            <div class="error-message" id="age-error"></div>
          </div>
          
          <div class="controls">
            <button class="secondary" id="back-to-consent">Back</button>
            <button id="next-to-step-2">Next</button>
          </div>
        </div>
        
        <!-- Step 2: Ethnicity -->
        <div id="demographics-step-2" class="hidden">
          <div class="form-group">
            <label>Ethnic Background</label>
            <div class="radio-group" id="ethnicity-options">
              <label class="radio-option">
                <input type="radio" name="ethnicity" value="caucasian"> Caucasian / White
              </label>
              <label class="radio-option">
                <input type="radio" name="ethnicity" value="asian"> Asian
              </label>
              <label class="radio-option">
                <input type="radio" name="ethnicity" value="african"> African / Black
              </label>
              <label class="radio-option">
                <input type="radio" name="ethnicity" value="hispanic"> Hispanic / Latino
              </label>
              <label class="radio-option">
                <input type="radio" name="ethnicity" value="middle_eastern"> Middle Eastern / North African
              </label>
              <label class="radio-option">
                <input type="radio" name="ethnicity" value="pacific_islander"> Pacific Islander
              </label>
              <label class="radio-option">
                <input type="radio" name="ethnicity" value="aboriginal_torres"> Aboriginal / Torres Strait Islander
              </label>
              <label class="radio-option">
                <input type="radio" name="ethnicity" value="mixed"> Mixed / Multiple ethnicities
              </label>
            </div>
            <div class="error-message" id="ethnicity-error"></div>
          </div>
          
          <div class="controls">
            <button class="secondary" id="back-to-step-1">Back</button>
            <button id="submit-demographics">Submit</button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Main Application Page -->
    <div id="main-app" class="hidden">
      <!-- Categories Selection -->
      <div id="categories-section">
        <div class="card">
          <h2>Select a Phrase Category</h2>
          <div class="categories">
            <div class="category" data-category="pain">
              <h3>Pain & Physical Discomfort</h3>
              <p>Phrases related to pain and physical discomfort</p>
            </div>
            <div class="category" data-category="respiratory">
              <h3>Respiratory & Airway</h3>
              <p>Phrases related to breathing and airway management</p>
            </div>
            <div class="category" data-category="medical">
              <h3>Medical & Treatment</h3>
              <p>Phrases related to medical treatment requests</p>
            </div>
            <div class="category" data-category="positioning">
              <h3>Positioning & Mobility</h3>
              <p>Phrases related to positioning and mobility</p>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Phrase Recording Section -->
      <div id="recording-section" class="hidden">
        <button class="secondary back-button" id="back-to-categories">← Back to Categories</button>
        
        <div class="card">
          <h2 id="category-title">Category Name</h2>
          <div class="phrase-display" id="current-phrase">Phrase will appear here</div>
          
          <div class="video-container">
            <video id="video" autoplay playsinline muted></video>
            <div class="controls">
              <button id="record-button">Start Recording</button>
              <button class="secondary" id="next-phrase" disabled>Next Phrase</button>
            </div>
          </div>
          
          <h3>All Phrases:</h3>
          <ul class="phrases-list" id="phrases-list"></ul>
        </div>
      </div>
    </div>
  </div>

  <script>
    // Phrase data
    const phrasesData = {
      pain: [
        "I'm in pain.",
        "My back hurts.",
        "My chest hurts.",
        "My neck hurts.",
        "I feel sick.",
        "I'm cold.",
        "I'm hot.",
        "I'm tired."
      ],
      respiratory: [
        "I need to cough.",
        "I need suctioning.",
        "I can't breathe."
      ],
      medical: [
        "I need a medication.",
        "I want something for the pain.",
        "I need mouth care."
      ],
      positioning: [
        "I need to move.",
        "I need to sit up.",
        "I need to lie down.",
        "I need to use the toilet.",
        "I need help going to the toilet."
      ]
    };
    
    // DOM Elements
    const consentPage = document.getElementById('consent-page');
    const demographicsPage = document.getElementById('demographics-page');
    const mainApp = document.getElementById('main-app');
    const categoriesSection = document.getElementById('categories-section');
    const recordingSection = document.getElementById('recording-section');
    
    const consentCheckbox = document.getElementById('consent-checkbox');
    const consentButton = document.getElementById('consent-button');
    
    const demographicsStep1 = document.getElementById('demographics-step-1');
    const demographicsStep2 = document.getElementById('demographics-step-2');
    const step1Indicator = document.getElementById('step-1');
    const step2Indicator = document.getElementById('step-2');
    
    const nextToStep2Button = document.getElementById('next-to-step-2');
    const backToStep1Button = document.getElementById('back-to-step-1');
    const backToConsentButton = document.getElementById('back-to-consent');
    const submitDemographicsButton = document.getElementById('submit-demographics');
    
    const categoryTitle = document.getElementById('category-title');
    const currentPhrase = document.getElementById('current-phrase');
    const phrasesList = document.getElementById('phrases-list');
    const backToCategoriesButton = document.getElementById('back-to-categories');
    
    const videoElement = document.getElementById('video');
    const recordButton = document.getElementById('record-button');
    const nextPhraseButton = document.getElementById('next-phrase');
    
    // State variables
    let demographics = {
      gender: '',
      age: '',
      ethnicity: ''
    };
    
    let currentCategory = '';
    let currentPhraseIndex = 0;
    let stream = null;
    let mediaRecorder = null;
    let recordedChunks = [];
    let isRecording = false;
    
    // Initialize the application
    function init() {
      // Set up event listeners
      consentCheckbox.addEventListener('change', function() {
        consentButton.disabled = !this.checked;
      });
      
      consentButton.addEventListener('click', function() {
        consentPage.classList.add('hidden');
        demographicsPage.classList.remove('hidden');
      });
      
      backToConsentButton.addEventListener('click', function() {
        demographicsPage.classList.add('hidden');
        consentPage.classList.remove('hidden');
      });
      
      nextToStep2Button.addEventListener('click', function() {
        if (validateStep1()) {
          demographicsStep1.classList.add('hidden');
          demographicsStep2.classList.remove('hidden');
          step1Indicator.classList.remove('active');
          step2Indicator.classList.add('active');
        }
      });
      
      backToStep1Button.addEventListener('click', function() {
        demographicsStep2.classList.add('hidden');
        demographicsStep1.classList.remove('hidden');
        step2Indicator.classList.remove('active');
        step1Indicator.classList.add('active');
      });
      
      submitDemographicsButton.addEventListener('click', function() {
        if (validateStep2()) {
          demographicsPage.classList.add('hidden');
          mainApp.classList.remove('hidden');
        }
      });
      
      // Set up category selection
      document.querySelectorAll('.category').forEach(category => {
        category.addEventListener('click', function() {
          const categoryId = this.getAttribute('data-category');
          selectCategory(categoryId);
        });
      });
      
      backToCategoriesButton.addEventListener('click', function() {
        stopCamera();
        recordingSection.classList.add('hidden');
        categoriesSection.classList.remove('hidden');
      });
      
      recordButton.addEventListener('click', toggleRecording);
      
      nextPhraseButton.addEventListener('click', function() {
        if (currentPhraseIndex < phrasesData[currentCategory].length - 1) {
          currentPhraseIndex++;
          updatePhraseDisplay();
        }
      });
      
      // Set up radio button listeners
      document.querySelectorAll('input[name="gender"]').forEach(radio => {
        radio.addEventListener('change', function() {
          demographics.gender = this.value;
          document.getElementById('gender-error').textContent = '';
        });
      });
      
      document.querySelectorAll('input[name="age"]').forEach(radio => {
        radio.addEventListener('change', function() {
          demographics.age = this.value;
          document.getElementById('age-error').textContent = '';
        });
      });
      
      document.querySelectorAll('input[name="ethnicity"]').forEach(radio => {
        radio.addEventListener('change', function() {
          demographics.ethnicity = this.value;
          document.getElementById('ethnicity-error').textContent = '';
        });
      });
    }
    
    // Validate demographics step 1
    function validateStep1() {
      let isValid = true;
      
      if (!demographics.gender) {
        document.getElementById('gender-error').textContent = 'Please select a gender option';
        isValid = false;
      }
      
      if (!demographics.age) {
        document.getElementById('age-error').textContent = 'Please select an age group';
        isValid = false;
      }
      
      return isValid;
    }
    
    // Validate demographics step 2
    function validateStep2() {
      let isValid = true;
      
      if (!demographics.ethnicity) {
        document.getElementById('ethnicity-error').textContent = 'Please select an ethnicity option';
        isValid = false;
      }
      
      return isValid;
    }
    
    // Select a category
    function selectCategory(categoryId) {
      console.log('Category selected:', categoryId);
      currentCategory = categoryId;
      currentPhraseIndex = 0;
      
      // Update UI
      categoriesSection.classList.add('hidden');
      recordingSection.classList.remove('hidden');
      
      // Set category title
      const titles = {
        pain: 'Pain & Physical Discomfort',
        respiratory: 'Respiratory & Airway',
        medical: 'Medical & Treatment',
        positioning: 'Positioning & Mobility'
      };
      categoryTitle.textContent = titles[categoryId];
      
      // Update phrase display
      updatePhraseDisplay();
      
      // Initialize camera
      initCamera();
    }
    
    // Update phrase display
    function updatePhraseDisplay() {
      // Display current phrase
      currentPhrase.textContent = phrasesData[currentCategory][currentPhraseIndex];
      
      // Update phrases list
      phrasesList.innerHTML = '';
      phrasesData[currentCategory].forEach((phrase, index) => {
        const li = document.createElement('li');
        li.className = 'phrase-item' + (index === currentPhraseIndex ? ' active' : '');
        li.textContent = phrase;
        li.addEventListener('click', () => {
          currentPhraseIndex = index;
          updatePhraseDisplay();
        });
        phrasesList.appendChild(li);
      });
      
      // Update next button state
      nextPhraseButton.disabled = currentPhraseIndex >= phrasesData[currentCategory].length - 1;
    }
    
    // Initialize camera
    async function initCamera() {
      try {
        // Check if we're in a secure context
        if (!window.isSecureContext) {
          console.warn('Not in a secure context. Camera access may be restricted.');
        }
        
        // Check if mediaDevices is available
        if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
          alert('Your browser does not support camera access or you need to use HTTPS');
          return;
        }
        
        stream = await navigator.mediaDevices.getUserMedia({ 
          video: {
            width: { ideal: 1280 },
            height: { ideal: 720 },
            facingMode: 'user'
          }, 
          audio: true 
        });
        
        videoElement.srcObject = stream;
        console.log('Camera initialized successfully');
      } catch (err) {
        console.error('Camera error:', err);
        alert('Camera error: ' + (err.message || 'Unknown error') + '\n\nPlease make sure you are using HTTPS and have granted camera permissions.');
      }
    }
    
    // Stop camera
    function stopCamera() {
      if (stream) {
        stream.getTracks().forEach(track => track.stop());
        stream = null;
        videoElement.srcObject = null;
      }
      
      if (isRecording) {
        stopRecording();
      }
    }
    
    // Toggle recording
    function toggleRecording() {
      if (isRecording) {
        stopRecording();
      } else {
        startRecording();
      }
    }
    
    // Start recording
    function startRecording() {
      if (!stream) {
        alert('Camera is not initialized. Please refresh the page and try again.');
        return;
      }
      
      recordedChunks = [];
      try {
        mediaRecorder = new MediaRecorder(stream, { mimeType: 'video/webm' });
      } catch (e) {
        console.error('MediaRecorder error:', e);
        alert('Error creating MediaRecorder: ' + e.message);
        return;
      }
      
      mediaRecorder.addEventListener('dataavailable', event => {
        if (event.data.size > 0) {
          recordedChunks.push(event.data);
        }
      });
      
      mediaRecorder.addEventListener('stop', () => {
        const blob = new Blob(recordedChunks, { type: 'video/webm' });
        console.log('Recording completed, size:', blob.size, 'bytes');
        
        // Here you would normally upload the video
        // For this demo, we'll just create a download link
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = `phrase_${currentCategory}_${currentPhraseIndex}.webm`;
        document.body.appendChild(a);
        a.click();
        setTimeout(() => {
          document.body.removeChild(a);
          URL.revokeObjectURL(url);
        }, 100);
        
        // Enable next phrase button
        nextPhraseButton.disabled = false;
      });
      
      mediaRecorder.start();
      isRecording = true;
      recordButton.innerHTML = '<span class="recording-indicator"></span> Stop Recording';
      recordButton.classList.add('danger');
      
      // Automatically stop after 5 seconds
      setTimeout(() => {
        if (mediaRecorder && mediaRecorder.state === 'recording') {
          stopRecording();
        }
      }, 5000);
    }
    
    // Stop recording
    function stopRecording() {
      if (mediaRecorder && mediaRecorder.state === 'recording') {
        mediaRecorder.stop();
      }
      
      isRecording = false;
      recordButton.textContent = 'Start Recording';
      recordButton.classList.remove('danger');
    }
    
    // Initialize the application
    init();
  </script>
</body>
</html>
