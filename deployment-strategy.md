# 🎯 ICU Dataset Application Deployment Strategy

## 📊 Current Situation Analysis

### ✅ What's Working:
- Backend API serving real S3 data (127 recordings)
- S3 progress tracking implemented and functional
- AWS credentials configured and working
- Frontend completion page integrated with S3ProgressDisplay

### ❌ Critical Issues for Week-Long Operation:
- Current processes will die when terminal closes
- No automatic restart on crashes
- No monitoring or alerting
- Single points of failure

## 🚀 Immediate Action Plan (Next 30 minutes)

### Step 1: Install PM2 Process Manager
```bash
npm install -g pm2
```

### Step 2: Stop Current Processes
```bash
# Kill current terminal processes
# Terminal 271 (backend) and Terminal 273 (frontend)
```

### Step 3: Start with PM2
```bash
# Create logs directory
mkdir -p logs

# Start services with PM2
pm2 start ecosystem.config.js

# Save configuration
pm2 save

# Enable startup on boot
pm2 startup
```

### Step 4: Verify Services
```bash
# Check status
pm2 status

# Test endpoints
curl http://localhost:5000/health
curl http://localhost:3000
curl http://localhost:5000/api/sample-counts
```

### Step 5: Start Health Monitor
```bash
# Run in background
nohup ./health-monitor.sh &
```

## 🌐 Production Deployment Options

### Option A: Hybrid (Frontend Cloud + Backend Local)
**Recommended for this week**

**Frontend (Netlify):**
- Deploy React build to Netlify
- Update environment variables for production
- Enable automatic deployments from Git

**Backend (Local with PM2):**
- Keep backend running locally with PM2
- Update CORS to allow Netlify domain
- Ensure stable internet connection

**Pros:** Quick setup, minimal changes
**Cons:** Backend still local dependency

### Option B: Full Cloud Deployment
**Recommended for long-term**

**Backend (Heroku):**
```bash
# Create Heroku app
heroku create icu-dataset-backend

# Set environment variables
heroku config:set AWS_ACCESS_KEY_ID=your_key
heroku config:set AWS_SECRET_ACCESS_KEY=your_secret
heroku config:set AWS_REGION=ap-southeast-2
heroku config:set AWS_S3_BUCKET=icudatasetphrasesfortesting

# Deploy
git subtree push --prefix server heroku main
```

**Frontend (Netlify):**
- Update REACT_APP_BACKEND_URL to Heroku URL
- Deploy to Netlify

**Pros:** Professional hosting, 99.9% uptime
**Cons:** Requires Heroku account setup

## 🔧 CORS Configuration Updates

### For Netlify Deployment:
Update `server/server.js` CORS configuration:

```javascript
const allowedOrigins = [
  'http://localhost:3000',
  'https://your-netlify-app.netlify.app',
  'https://icuphrasecollection.com'  // If custom domain
];
```

### Environment Variables for Production:
```bash
# .env.production
REACT_APP_BACKEND_URL=https://your-backend-url.herokuapp.com
REACT_APP_AWS_IDENTITY_POOL_ID=ap-southeast-2:59baeaf9-a80c-4afc-8f27-f4d6191242dd
REACT_APP_AWS_REGION=ap-southeast-2
REACT_APP_S3_BUCKET=icudatasetphrasesfortesting
```

## 📋 Pre-Deployment Checklist

### ✅ Backend Stability:
- [ ] PM2 configuration tested
- [ ] Health monitoring active
- [ ] S3 API responding correctly
- [ ] AWS credentials valid
- [ ] CORS policy updated

### ✅ Frontend Preparation:
- [ ] Production build successful
- [ ] Environment variables configured
- [ ] S3 progress tracking tested
- [ ] Completion page functional

### ✅ Infrastructure:
- [ ] Logging configured
- [ ] Monitoring active
- [ ] Backup plan documented
- [ ] Rollback procedure ready

## 🚨 Risk Mitigation

### High Priority Risks:
1. **AWS Credential Expiry**
   - Monitor: Check S3 API daily
   - Mitigation: Have backup credentials ready

2. **S3 Bucket Access**
   - Monitor: Automated health checks
   - Mitigation: Verify permissions weekly

3. **Process Crashes**
   - Monitor: PM2 + health-monitor.sh
   - Mitigation: Automatic restart configured

4. **Network Connectivity**
   - Monitor: Ping tests in health monitor
   - Mitigation: Multiple internet connections

### Medium Priority Risks:
1. **Disk Space**
   - Monitor: Log rotation configured
   - Mitigation: Weekly cleanup scripts

2. **Memory Leaks**
   - Monitor: PM2 memory limits
   - Mitigation: Automatic restart on high memory

## 📞 Support & Monitoring

### Daily Checks:
- `pm2 status` - Process health
- `curl http://localhost:5000/health` - Backend API
- `curl http://localhost:5000/api/sample-counts` - S3 connectivity
- Check `./logs/health-monitor.log` for issues

### Weekly Maintenance:
- Review log files for errors
- Verify AWS credentials still valid
- Test S3 bucket access
- Update dependencies if needed

### Emergency Procedures:
1. **Complete Service Failure:**
   ```bash
   pm2 kill
   pm2 start ecosystem.config.js
   ```

2. **S3 API Issues:**
   ```bash
   pm2 restart icu-backend
   # Check AWS credentials in .env
   ```

3. **Frontend Issues:**
   ```bash
   pm2 restart icu-frontend
   # Check browser console for errors
   ```

## 🎯 Success Metrics

### Week 1 Goals:
- [ ] 99%+ uptime for both services
- [ ] S3 progress tracking functional
- [ ] Zero data loss
- [ ] Completion page working correctly

### Performance Targets:
- Backend response time: <500ms
- Frontend load time: <3 seconds
- S3 API response: <2 seconds
- Zero critical errors in logs

## 📈 Next Steps After Week 1

1. **Evaluate Performance**
   - Review logs and metrics
   - Identify any stability issues
   - Document lessons learned

2. **Plan Cloud Migration**
   - Set up Heroku/AWS accounts
   - Test cloud deployment
   - Migrate gradually

3. **Enhance Monitoring**
   - Add email/SMS alerts
   - Implement uptime monitoring
   - Create dashboard for metrics
