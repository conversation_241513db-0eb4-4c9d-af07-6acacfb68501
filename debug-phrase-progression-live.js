#!/usr/bin/env node

/**
 * Live Debugging Script for Phrase Progression Issues
 * This script helps identify why the fixes aren't working
 */

console.log('🔍 === LIVE DEBUGGING: PHRASE PROGRESSION ISSUES ===\n');

console.log('📊 ANALYSIS OF BACKEND LOGS:');
console.log('✅ Backend uploads are working (multiple "Call the doctor." uploads seen)');
console.log('❌ No App.js debug messages visible in backend logs');
console.log('❌ No "🎯 Third recording completed" messages');
console.log('❌ No "🚀 Auto-navigation timeout triggered" messages');
console.log('');

console.log('🎯 LIKELY ROOT CAUSES:');
console.log('');
console.log('1. **RECORDING COUNT TRACKING ISSUE**:');
console.log('   • App.js may not be properly tracking 3 recordings per phrase');
console.log('   • recordingsCount state may not be updating correctly');
console.log('   • currentRecordingNumber may not be incrementing');
console.log('');
console.log('2. **STATE SYNCHRONIZATION PROBLEM**:');
console.log('   • VideoRecorder and App.js may be out of sync');
console.log('   • Props may not be updating correctly');
console.log('   • React state updates may be batched/delayed');
console.log('');
console.log('3. **CONSOLE LOG FILTERING**:');
console.log('   • Browser console may be filtering out debug messages');
console.log('   • Console may be cleared between recordings');
console.log('   • Messages may be logged but not visible');
console.log('');

console.log('🔧 IMMEDIATE DEBUGGING STEPS:');
console.log('');
console.log('STEP 1: Check Browser Console');
console.log('1. Open http://localhost:3000');
console.log('2. Open DevTools (F12) → Console');
console.log('3. Clear console (Ctrl+L or Cmd+K)');
console.log('4. Record ONE video');
console.log('5. Look for these messages:');
console.log('   • "🎯 === APP: handleVideoRecorded called ==="');
console.log('   • "📊 VideoRecorder: recordingCount changed to:"');
console.log('   • "📝 VideoRecorder: Phrase changed to:"');
console.log('');
console.log('STEP 2: Check Recording Count Logic');
console.log('1. After first recording, look for:');
console.log('   • "Recording 1/3 uploaded successfully!"');
console.log('   • Progress dots showing 1 green dot');
console.log('2. After second recording, look for:');
console.log('   • "Recording 2/3 uploaded successfully!"');
console.log('   • Progress dots showing 2 green dots');
console.log('3. After third recording, look for:');
console.log('   • "Recording 3/3 uploaded successfully!"');
console.log('   • "🎯 Third recording completed, preparing for auto-navigation"');
console.log('   • 1.5 second delay, then phrase change');
console.log('');
console.log('STEP 3: Manual State Inspection');
console.log('1. In browser console, type: window.React = React');
console.log('2. Use React DevTools to inspect App component state');
console.log('3. Check these values:');
console.log('   • currentPhraseIndex');
console.log('   • currentRecordingNumber');
console.log('   • recordingsCount');
console.log('   • selectedPhrases');
console.log('');

console.log('🚨 CRITICAL ISSUES TO CHECK:');
console.log('');
console.log('1. **Recording Number Logic**:');
console.log('   • All uploads show recordingNumber: "1" in backend logs');
console.log('   • This suggests currentRecordingNumber is not incrementing');
console.log('   • App.js may not be updating this value correctly');
console.log('');
console.log('2. **Phrase Key Format**:');
console.log('   • Backend shows phrase: "Call the doctor."');
console.log('   • App.js uses phraseKey format: "category:phrase"');
console.log('   • Check if key format matches between components');
console.log('');
console.log('3. **State Update Timing**:');
console.log('   • handleVideoRecorded may not be called correctly');
console.log('   • VideoRecorder onRecordingComplete may have wrong signature');
console.log('   • State updates may be asynchronous and not completing');
console.log('');

console.log('🔍 SPECIFIC DEBUGGING COMMANDS:');
console.log('');
console.log('In browser console, run these commands:');
console.log('');
console.log('// Check current App state');
console.log('console.log("Current phrase index:", window.appState?.currentPhraseIndex);');
console.log('console.log("Current recording number:", window.appState?.currentRecordingNumber);');
console.log('console.log("Recordings count:", window.appState?.recordingsCount);');
console.log('');
console.log('// Force trigger next phrase (for testing)');
console.log('window.handleNextPhrase?.();');
console.log('');
console.log('// Check VideoRecorder state');
console.log('console.log("VideoRecorder recording count:", window.videoRecorderState?.recordingCount);');
console.log('');

console.log('🛠️ POTENTIAL FIXES TO TRY:');
console.log('');
console.log('1. **Add Global State Debugging**:');
console.log('   • Expose App state to window object');
console.log('   • Add more console.log statements');
console.log('   • Use React DevTools');
console.log('');
console.log('2. **Check onRecordingComplete Signature**:');
console.log('   • Verify VideoRecorder calls the right function');
console.log('   • Check parameter passing');
console.log('   • Ensure handleVideoRecorded receives correct data');
console.log('');
console.log('3. **Force State Updates**:');
console.log('   • Use useEffect with dependencies');
console.log('   • Add key prop to VideoRecorder');
console.log('   • Force re-render on phrase change');
console.log('');

console.log('📋 TESTING PROTOCOL:');
console.log('');
console.log('1. **Single Recording Test**:');
console.log('   • Record 1 video');
console.log('   • Check if progress shows 1/3');
console.log('   • Verify console logs appear');
console.log('');
console.log('2. **Three Recording Test**:');
console.log('   • Record 3 videos of same phrase');
console.log('   • Watch for auto-navigation after 3rd');
console.log('   • Check if phrase text changes');
console.log('   • Verify progress resets to 0/3');
console.log('');
console.log('3. **Manual Navigation Test**:');
console.log('   • Use phrase navigation buttons');
console.log('   • Check if phrase text updates');
console.log('   • Verify progress shows correct count');
console.log('');

console.log('🎬 READY FOR LIVE DEBUGGING!');
console.log('');
console.log('Please follow the steps above and report back with:');
console.log('1. What console messages you see (or don\'t see)');
console.log('2. Current values of App state variables');
console.log('3. Whether progress dots update correctly');
console.log('4. Whether phrase text changes at all');
console.log('');
console.log('This will help identify the exact point of failure!');
