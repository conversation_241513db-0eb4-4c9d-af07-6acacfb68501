#!/usr/bin/env node

/**
 * Comprehensive Upload Debugging Script
 * Tests all components of the video upload pipeline
 */

const { execSync } = require('child_process');
const fs = require('fs');

console.log('🔍 === ICU DATASET APPLICATION UPLOAD DEBUGGING ===\n');

// Test 1: Check if backend server is running
console.log('1️⃣ Testing Backend Server Connectivity...');
try {
  const healthResponse = execSync('curl -s http://localhost:5000/health', { encoding: 'utf8' });
  const healthData = JSON.parse(healthResponse);
  console.log('✅ Backend server is running');
  console.log(`   Status: ${healthData.status}`);
  console.log(`   AWS: ${healthData.services.aws}`);
  console.log(`   Storage: ${healthData.services.storage}`);
} catch (error) {
  console.log('❌ Backend server is not responding');
  console.log(`   Error: ${error.message}`);
  process.exit(1);
}

// Test 2: Check frontend server
console.log('\n2️⃣ Testing Frontend Server...');
try {
  const frontendResponse = execSync('curl -s -I http://localhost:3000', { encoding: 'utf8' });
  if (frontendResponse.includes('200 OK')) {
    console.log('✅ Frontend server is running on port 3000');
  } else {
    console.log('⚠️ Frontend server response unexpected');
  }
} catch (error) {
  console.log('❌ Frontend server is not responding on port 3000');
  console.log(`   Error: ${error.message}`);
}

// Test 3: Test upload endpoint with mock data
console.log('\n3️⃣ Testing Upload Endpoint...');
try {
  const uploadResponse = execSync(`curl -s -X POST http://localhost:5000/upload \\
    -F "video=@/dev/null" \\
    -F "phrase=debug test" \\
    -F "category=debug" \\
    -F "recordingNumber=1" \\
    -F 'demographics={"userId":"debug01","ageGroup":"40to64","gender":"female","ethnicity":"not_specified"}'`, 
    { encoding: 'utf8' });
  
  const uploadData = JSON.parse(uploadResponse);
  if (uploadData.success) {
    console.log('✅ Upload endpoint is working');
    console.log(`   File path: ${uploadData.filePath}`);
    console.log(`   URL: ${uploadData.url}`);
  } else {
    console.log('❌ Upload endpoint failed');
    console.log(`   Error: ${uploadData.error}`);
  }
} catch (error) {
  console.log('❌ Upload endpoint test failed');
  console.log(`   Error: ${error.message}`);
}

// Test 4: Check AWS credentials
console.log('\n4️⃣ Testing AWS Configuration...');
const awsAccessKey = process.env.AWS_ACCESS_KEY_ID;
const awsSecretKey = process.env.AWS_SECRET_ACCESS_KEY;
const awsRegion = process.env.AWS_REGION;
const s3Bucket = process.env.AWS_S3_BUCKET;

console.log(`   AWS_ACCESS_KEY_ID: ${awsAccessKey ? '✅ Set' : '❌ Missing'}`);
console.log(`   AWS_SECRET_ACCESS_KEY: ${awsSecretKey ? '✅ Set' : '❌ Missing'}`);
console.log(`   AWS_REGION: ${awsRegion || 'Not set'}`);
console.log(`   AWS_S3_BUCKET: ${s3Bucket || 'Not set'}`);

// Test 5: Check .env file
console.log('\n5️⃣ Checking Environment Configuration...');
try {
  const envContent = fs.readFileSync('.env', 'utf8');
  const envLines = envContent.split('\n');
  
  const reactAppBackendUrl = envLines.find(line => line.startsWith('REACT_APP_BACKEND_URL='));
  const reactAppS3Bucket = envLines.find(line => line.startsWith('REACT_APP_S3_BUCKET='));
  const reactAppAwsRegion = envLines.find(line => line.startsWith('REACT_APP_AWS_REGION='));
  const reactAppIdentityPool = envLines.find(line => line.startsWith('REACT_APP_AWS_IDENTITY_POOL_ID='));
  
  console.log(`   REACT_APP_BACKEND_URL: ${reactAppBackendUrl ? reactAppBackendUrl.split('=')[1] : 'Not set'}`);
  console.log(`   REACT_APP_S3_BUCKET: ${reactAppS3Bucket ? reactAppS3Bucket.split('=')[1] : 'Not set'}`);
  console.log(`   REACT_APP_AWS_REGION: ${reactAppAwsRegion ? reactAppAwsRegion.split('=')[1] : 'Not set'}`);
  console.log(`   REACT_APP_AWS_IDENTITY_POOL_ID: ${reactAppIdentityPool ? '✅ Set' : '❌ Missing'}`);
} catch (error) {
  console.log('❌ Could not read .env file');
  console.log(`   Error: ${error.message}`);
}

// Test 6: Network connectivity test
console.log('\n6️⃣ Testing Network Connectivity...');
try {
  // Test connection to AWS S3
  const s3Response = execSync('curl -s -I https://s3.ap-southeast-2.amazonaws.com/', { encoding: 'utf8' });
  if (s3Response.includes('200 OK') || s3Response.includes('403 Forbidden')) {
    console.log('✅ AWS S3 endpoint is reachable');
  } else {
    console.log('⚠️ AWS S3 endpoint response unexpected');
  }
} catch (error) {
  console.log('❌ Cannot reach AWS S3 endpoint');
  console.log(`   Error: ${error.message}`);
}

console.log('\n🎯 === DEBUGGING RECOMMENDATIONS ===');
console.log('1. Make sure both servers are running:');
console.log('   - Backend: http://localhost:5000');
console.log('   - Frontend: http://localhost:3000');
console.log('2. Check browser console for detailed error messages');
console.log('3. Use the S3 Test Panel in the application (🧪 Test S3 Upload button)');
console.log('4. Check network tab in browser DevTools during upload');
console.log('5. Verify AWS credentials are correctly configured');

console.log('\n📋 === NEXT STEPS ===');
console.log('1. Open http://localhost:3000 in your browser');
console.log('2. Navigate to the recording page');
console.log('3. Click "🧪 Test S3 Upload" button to run built-in diagnostics');
console.log('4. Check browser console for detailed error logs');
console.log('5. If issues persist, check the upload debugging tools in the app');
