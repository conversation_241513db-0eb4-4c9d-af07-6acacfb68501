/**
 * S3 Progress Tracking Verification Script
 * Run this in the browser console to verify the completion page progress tracking
 */

console.log('🎯 === S3 PROGRESS TRACKING VERIFICATION ===');

// Test the S3 API directly
async function testS3API() {
  console.log('\n🔄 Step 1: Testing S3 API...');
  
  try {
    const response = await fetch('http://localhost:5000/api/sample-counts');
    const data = await response.json();
    
    if (data.success) {
      console.log('✅ S3 API working correctly');
      console.log(`📊 Total recordings: ${data.counts.total}`);
      console.log(`📝 Unique phrases: ${Object.keys(data.counts.byPhrase).length}`);
      
      // Show top phrases
      const topPhrases = Object.entries(data.counts.byPhrase)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 10);
      
      console.log('🏆 Top phrases by count:');
      topPhrases.forEach(([phrase, count]) => {
        console.log(`   ${phrase}: ${count}`);
      });
      
      return data;
    } else {
      console.error('❌ S3 API error:', data.error);
      return null;
    }
  } catch (error) {
    console.error('❌ S3 API test failed:', error);
    return null;
  }
}

// Test phrase normalization
function testPhraseNormalization() {
  console.log('\n🔄 Step 2: Testing phrase normalization...');
  
  // Function from s3ProgressService.js
  function normalizePhraseKey(phrase) {
    return phrase
      .toLowerCase()
      .replace(/[^a-z0-9\s]/g, '') // Remove special characters except spaces
      .replace(/\s+/g, '_') // Replace spaces with underscores
      .trim();
  }
  
  const testPhrases = [
    'Call the doctor.',
    'Call the nurse.',
    'I need water.',
    'Thank you.',
    'Please.',
    'Zero',
    'One',
    'Two',
    'Eight'
  ];
  
  console.log('🧪 Testing phrase normalization:');
  testPhrases.forEach(phrase => {
    const normalized = normalizePhraseKey(phrase);
    console.log(`   "${phrase}" → "${normalized}"`);
  });
}

// Test the completion page trigger
function testCompletionPage() {
  console.log('\n🔄 Step 3: Testing completion page...');
  
  if (typeof window.testCompletionPage === 'function') {
    console.log('✅ testCompletionPage function found');
    console.log('🚀 Triggering completion page...');
    window.testCompletionPage();
    
    // Wait a moment then check for S3ProgressDisplay
    setTimeout(() => {
      const progressElements = document.querySelectorAll('[class*="S3Progress"], [class*="progress"]');
      console.log(`📊 Found ${progressElements.length} progress-related elements`);
      
      // Look for specific progress indicators
      const progressText = document.body.innerText;
      if (progressText.includes('recordings collected') || progressText.includes('Project Progress')) {
        console.log('✅ Progress display found on completion page');
      } else {
        console.log('⚠️ Progress display not found - check if completion page loaded');
      }
    }, 2000);
    
  } else {
    console.log('❌ testCompletionPage function not found');
    console.log('💡 Make sure you\'re on the ICU dataset application page');
  }
}

// Test S3ProgressService directly
async function testS3ProgressService() {
  console.log('\n🔄 Step 4: Testing S3ProgressService...');
  
  // Check if the service is available
  if (typeof window !== 'undefined' && window.React) {
    console.log('✅ React environment detected');
    
    // Try to access the service through the global scope
    // Note: This might not work if the service isn't exposed globally
    console.log('⚠️ S3ProgressService is not globally accessible');
    console.log('💡 Check browser console for S3ProgressService debug logs when completion page loads');
  } else {
    console.log('❌ React environment not detected');
  }
}

// Main verification function
async function verifyS3ProgressTracking() {
  console.log('🎯 Starting S3 Progress Tracking Verification...\n');
  
  // Step 1: Test S3 API
  const s3Data = await testS3API();
  if (!s3Data) {
    console.log('❌ Cannot continue - S3 API not working');
    return;
  }
  
  // Step 2: Test phrase normalization
  testPhraseNormalization();
  
  // Step 3: Test completion page
  testCompletionPage();
  
  // Step 4: Test S3ProgressService
  await testS3ProgressService();
  
  console.log('\n📋 === VERIFICATION SUMMARY ===');
  console.log('✅ S3 API: Working');
  console.log('✅ Phrase normalization: Tested');
  console.log('✅ Completion page trigger: Attempted');
  console.log('');
  console.log('🔍 Next steps:');
  console.log('1. Check browser console for S3ProgressService debug logs');
  console.log('2. Verify progress bar shows real data (not 0%)');
  console.log('3. Test refresh button functionality');
  console.log('4. Confirm no error messages appear');
  
  return s3Data;
}

// Make functions available globally
window.verifyS3ProgressTracking = verifyS3ProgressTracking;
window.testS3API = testS3API;
window.testPhraseNormalization = testPhraseNormalization;

// Auto-run if this script is loaded directly
if (typeof window !== 'undefined') {
  console.log('🚀 S3 Progress Tracking Verification Script Loaded');
  console.log('📋 Run window.verifyS3ProgressTracking() to start verification');
  console.log('📋 Or run individual tests: window.testS3API(), window.testPhraseNormalization()');
}
