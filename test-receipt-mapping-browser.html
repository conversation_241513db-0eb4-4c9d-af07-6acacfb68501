<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Receipt Mapping AWS Test - ICU Dataset Application</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-title {
            color: #009688;
            border-bottom: 2px solid #009688;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success {
            background-color: #e8f5e8;
            border-left: 4px solid #4caf50;
        }
        .error {
            background-color: #ffeaea;
            border-left: 4px solid #f44336;
        }
        .info {
            background-color: #e3f2fd;
            border-left: 4px solid #2196f3;
        }
        .warning {
            background-color: #fff3e0;
            border-left: 4px solid #ff9800;
        }
        button {
            background-color: #009688;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #00796b;
        }
        button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        .loading {
            opacity: 0.6;
        }
    </style>
</head>
<body>
    <h1>🧪 Receipt Mapping AWS Test - ICU Dataset Application</h1>
    <p>This page tests the AWS S3 connectivity and receipt mapping functionality to identify why receipt-log.json is not being created.</p>

    <div class="test-section">
        <h2 class="test-title">🔧 Environment Configuration</h2>
        <div id="config-results">
            <div class="info result">Loading environment configuration...</div>
        </div>
    </div>

    <div class="test-section">
        <h2 class="test-title">📡 Test 1: AWS S3 Connectivity</h2>
        <p>Test basic AWS S3 connectivity using the same configuration as the receipt mapping service</p>
        <button onclick="testAWSConnectivity()" id="aws-test-btn">Test AWS Connectivity</button>
        <div id="aws-test-results"></div>
    </div>

    <div class="test-section">
        <h2 class="test-title">📋 Test 2: Receipt Mapping Service</h2>
        <p>Test the receipt mapping service directly to see if it can create receipt-log.json</p>
        <button onclick="testReceiptMapping()" id="receipt-test-btn">Test Receipt Mapping</button>
        <div id="receipt-test-results"></div>
    </div>

    <div class="test-section">
        <h2 class="test-title">🔍 Test 3: Check Existing Receipt Log</h2>
        <p>Check if receipt-log.json already exists in the S3 bucket</p>
        <button onclick="checkExistingLog()" id="check-log-btn">Check Existing Log</button>
        <div id="check-log-results"></div>
    </div>

    <div class="test-section">
        <h2 class="test-title">💾 Test 4: Create Receipt Log</h2>
        <p>Attempt to create the initial receipt-log.json file with receipt 000004</p>
        <button onclick="createReceiptLog()" id="create-log-btn">Create Receipt Log</button>
        <div id="create-log-results"></div>
    </div>

    <div class="test-section">
        <h2 class="test-title">🎯 Quick Access</h2>
        <p>Access the ICU Dataset Application to test receipt generation</p>
        <button onclick="openApplication()">Open ICU Dataset Application</button>
        <div class="info result">
            <strong>Testing Instructions:</strong>
            1. Complete a recording session in the application
            2. Check if receipt numbers are generated (should be 000005 or higher)
            3. Look for console errors related to receipt mapping
            4. Verify if receipt-log.json is created in S3 after this test
        </div>
    </div>

    <script type="module">
        // Import AWS SDK modules (this will only work if the page is served from the React app)
        let S3Client, GetObjectCommand, PutObjectCommand, ListObjectsV2Command;
        let fromCognitoIdentityPool, CognitoIdentityClient;
        
        // Configuration
        const AWS_REGION = 'ap-southeast-2';
        const BUCKET_NAME = 'icudatasetphrasesfortesting';
        const RECEIPT_LOG_KEY = 'receipt-numbers/receipt-log.json';
        
        // Display configuration
        function displayConfig() {
            const configDiv = document.getElementById('config-results');
            configDiv.innerHTML = `
                <div class="info result">
                    <strong>Configuration:</strong>
                    AWS Region: ${AWS_REGION}
                    S3 Bucket: ${BUCKET_NAME}
                    Receipt Log Key: ${RECEIPT_LOG_KEY}
                    
                    <strong>Environment Variables (from React app):</strong>
                    REACT_APP_AWS_REGION: ${window.location.hostname === 'localhost' ? 'Available in React app' : 'Not accessible from file://'}
                    REACT_APP_S3_BUCKET: ${window.location.hostname === 'localhost' ? 'Available in React app' : 'Not accessible from file://'}
                    REACT_APP_AWS_IDENTITY_POOL_ID: ${window.location.hostname === 'localhost' ? 'Available in React app' : 'Not accessible from file://'}
                </div>
            `;
        }

        // Test AWS connectivity
        window.testAWSConnectivity = async function() {
            const btn = document.getElementById('aws-test-btn');
            const resultsDiv = document.getElementById('aws-test-results');
            
            btn.disabled = true;
            btn.textContent = 'Testing...';
            resultsDiv.innerHTML = '<div class="info result">Testing AWS S3 connectivity...</div>';
            
            try {
                // This test will only work if we're running from the React app context
                if (window.location.hostname !== 'localhost') {
                    throw new Error('This test must be run from the React development server (localhost:3004)');
                }
                
                // Try to access the receipt mapping service from the React app
                const response = await fetch('/api/test-aws-connectivity', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ test: 'connectivity' })
                });
                
                if (response.ok) {
                    resultsDiv.innerHTML = '<div class="success result">✅ AWS connectivity test passed</div>';
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="error result">❌ AWS connectivity test failed: ${error.message}</div>
                    <div class="warning result">
                        <strong>Alternative Test Method:</strong>
                        1. Open browser console (F12)
                        2. Go to the ICU Dataset Application (localhost:3004)
                        3. Run: console.log('Testing AWS...'); 
                        4. Check for AWS-related errors in console
                    </div>
                `;
            } finally {
                btn.disabled = false;
                btn.textContent = 'Test AWS Connectivity';
            }
        };

        // Test receipt mapping service
        window.testReceiptMapping = async function() {
            const btn = document.getElementById('receipt-test-btn');
            const resultsDiv = document.getElementById('receipt-test-results');
            
            btn.disabled = true;
            btn.textContent = 'Testing...';
            resultsDiv.innerHTML = '<div class="info result">Testing receipt mapping service...</div>';
            
            try {
                // Generate a test receipt number
                const testReceiptNumber = '000004';
                const testVideoUrls = [
                    `https://s3.amazonaws.com/${BUCKET_NAME}/icu-videos/25to40/female/caucasian/hello/hello__user01__25to40__female__caucasian__${Date.now()}.webm`
                ];
                const testDemographics = {
                    age: '25to40',
                    gender: 'female',
                    ethnicity: 'caucasian'
                };
                const testSessionId = 'test-session-001';
                
                resultsDiv.innerHTML = `
                    <div class="info result">
                        <strong>Test Parameters:</strong>
                        Receipt Number: ${testReceiptNumber}
                        Video URLs: ${testVideoUrls.length} video(s)
                        Demographics: ${JSON.stringify(testDemographics)}
                        Session ID: ${testSessionId}
                        
                        <strong>Note:</strong> This test requires access to the React app's receipt mapping service.
                        If running from file://, this test will not work.
                    </div>
                `;
                
                // This would need to be implemented as an API endpoint in the React app
                throw new Error('Receipt mapping test requires React app context');
                
            } catch (error) {
                resultsDiv.innerHTML += `
                    <div class="error result">❌ Receipt mapping test failed: ${error.message}</div>
                    <div class="warning result">
                        <strong>Manual Test Instructions:</strong>
                        1. Open ICU Dataset Application (localhost:3004)
                        2. Open browser console (F12)
                        3. Complete a recording session
                        4. Look for console messages starting with "📋 Adding receipt mapping..."
                        5. Check for any error messages related to S3 or AWS
                    </div>
                `;
            } finally {
                btn.disabled = false;
                btn.textContent = 'Test Receipt Mapping';
            }
        };

        // Check existing log
        window.checkExistingLog = async function() {
            const btn = document.getElementById('check-log-btn');
            const resultsDiv = document.getElementById('check-log-results');
            
            btn.disabled = true;
            btn.textContent = 'Checking...';
            resultsDiv.innerHTML = '<div class="info result">Checking for existing receipt-log.json...</div>';
            
            try {
                resultsDiv.innerHTML = `
                    <div class="warning result">
                        <strong>Manual Check Required:</strong>
                        
                        1. <strong>AWS CLI Method:</strong>
                           aws s3 ls s3://${BUCKET_NAME}/receipt-numbers/
                           
                        2. <strong>AWS Console Method:</strong>
                           - Go to AWS S3 Console
                           - Navigate to bucket: ${BUCKET_NAME}
                           - Check folder: receipt-numbers/
                           - Look for file: receipt-log.json
                           
                        3. <strong>Browser Console Method:</strong>
                           - Open ICU Dataset Application
                           - Open browser console (F12)
                           - Look for messages about receipt log retrieval
                    </div>
                `;
                
            } finally {
                btn.disabled = false;
                btn.textContent = 'Check Existing Log';
            }
        };

        // Create receipt log
        window.createReceiptLog = async function() {
            const btn = document.getElementById('create-log-btn');
            const resultsDiv = document.getElementById('create-log-results');
            
            btn.disabled = true;
            btn.textContent = 'Creating...';
            resultsDiv.innerHTML = '<div class="info result">Attempting to create receipt-log.json...</div>';
            
            try {
                const testReceiptLog = {
                    "000004": {
                        timestamp: new Date().toISOString(),
                        videos: [
                            `https://s3.amazonaws.com/${BUCKET_NAME}/icu-videos/25to40/female/caucasian/hello/hello__user01__25to40__female__caucasian__${Date.now()}.webm`
                        ],
                        demographics: {
                            age: "25to40",
                            gender: "female",
                            ethnicity: "caucasian"
                        },
                        sessionId: "test-user-001",
                        assignmentType: "manual-test",
                        recordingCount: 1,
                        createdBy: "browser-test",
                        testEntry: true
                    }
                };
                
                resultsDiv.innerHTML = `
                    <div class="info result">
                        <strong>Test Receipt Log Structure:</strong>
                        ${JSON.stringify(testReceiptLog, null, 2)}
                        
                        <strong>Manual Creation Instructions:</strong>
                        1. Use AWS CLI: aws s3 cp receipt-log.json s3://${BUCKET_NAME}/receipt-numbers/
                        2. Or use the ICU Dataset Application to complete a recording session
                        3. The receipt mapping service should automatically create this file
                    </div>
                `;
                
            } finally {
                btn.disabled = false;
                btn.textContent = 'Create Receipt Log';
            }
        };

        // Open application
        window.openApplication = function() {
            window.open('http://localhost:3004', '_blank');
        };

        // Initialize
        displayConfig();
    </script>
</body>
</html>
