<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ICU Dataset Application - Final Deployment Verification</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .header {
            background: linear-gradient(135deg, #009688, #26a69a);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-item {
            margin: 15px 0;
            padding: 15px;
            border-left: 4px solid #009688;
            background-color: #f8f9fa;
        }
        .status {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-weight: bold;
            margin-left: 10px;
        }
        .pass { background-color: #4caf50; color: white; }
        .fail { background-color: #f44336; color: white; }
        .pending { background-color: #ff9800; color: white; }
        .testing { background-color: #2196f3; color: white; }
        .link-button {
            display: inline-block;
            background-color: #009688;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 5px;
            margin: 5px;
            transition: background-color 0.3s;
        }
        .link-button:hover {
            background-color: #00796b;
        }
        .test-button {
            background-color: #2196f3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background-color: #1976d2;
        }
        .instructions {
            background-color: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .code {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            margin: 10px 0;
        }
        .results {
            background-color: #f9f9f9;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border: 1px solid #ddd;
        }
        .critical {
            border-left-color: #f44336;
            background-color: #ffebee;
        }
        .success-summary {
            background-color: #e8f5e8;
            border: 2px solid #4caf50;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔍 ICU Dataset Application - Final Deployment Verification</h1>
        <p>Comprehensive testing before Netlify deployment</p>
        <p><strong>Test Date:</strong> <span id="currentDate"></span></p>
    </div>

    <div class="test-section">
        <h2>🚀 Server Status Verification</h2>
        <div class="test-item">
            <h3>Backend Server Health</h3>
            <button class="test-button" onclick="testBackendHealth()">Test Backend Health</button>
            <div id="backend-health-result" class="results"></div>
        </div>
        <div class="test-item">
            <h3>Frontend Server Access</h3>
            <button class="test-button" onclick="testFrontendAccess()">Test Frontend Access</button>
            <div id="frontend-access-result" class="results"></div>
        </div>
        <div class="test-item">
            <h3>AWS S3 Connectivity</h3>
            <button class="test-button" onclick="testS3Connectivity()">Test S3 Connection</button>
            <div id="s3-connectivity-result" class="results"></div>
        </div>
    </div>

    <div class="test-section">
        <h2>🧪 Core Functionality Testing</h2>
        <div class="test-item">
            <h3>1. Application Loading & Navigation</h3>
            <div class="instructions">
                <p><strong>Manual Test:</strong> Open application and verify initial load</p>
                <ol>
                    <li>Click the link below to open the application</li>
                    <li>Verify the consent page loads without errors</li>
                    <li>Check browser console for any JavaScript errors</li>
                    <li>Confirm all styling and layout appears correctly</li>
                </ol>
            </div>
            <a href="http://localhost:3000" class="link-button" target="_blank">🔗 Open ICU Dataset Application</a>
            <span class="status pending">MANUAL TEST REQUIRED</span>
        </div>

        <div class="test-item">
            <h3>2. Consent Form Functionality</h3>
            <div class="instructions">
                <p><strong>Test Steps:</strong></p>
                <ol>
                    <li>Read through the consent information</li>
                    <li>Try submitting without checking consent boxes (should show validation)</li>
                    <li>Check all required consent boxes</li>
                    <li>Click "I Agree and Continue" button</li>
                    <li>Verify navigation to demographics page</li>
                </ol>
            </div>
            <span class="status pending">MANUAL TEST REQUIRED</span>
        </div>

        <div class="test-item">
            <h3>3. Demographics Form Validation</h3>
            <div class="instructions">
                <p><strong>Test Steps:</strong></p>
                <ol>
                    <li>Try submitting form with missing fields (should show validation)</li>
                    <li>Fill in all required demographic information</li>
                    <li>Verify form accepts valid data</li>
                    <li>Click "Continue to Phrase Selection"</li>
                    <li>Confirm navigation to phrase selection page</li>
                </ol>
            </div>
            <span class="status pending">MANUAL TEST REQUIRED</span>
        </div>

        <div class="test-item critical">
            <h3>4. Loading Spinner Fix Verification</h3>
            <div class="instructions">
                <p><strong>Critical Test:</strong> Verify no persistent loading spinners</p>
                <ol>
                    <li>Navigate to phrase selection page</li>
                    <li>Observe category loading behavior</li>
                    <li><strong>VERIFY:</strong> Loading spinners disappear within 5 seconds</li>
                    <li><strong>VERIFY:</strong> No "loading real time progress data" messages persist</li>
                    <li><strong>VERIFY:</strong> Category items display actual progress data</li>
                </ol>
            </div>
            <span class="status pending">CRITICAL TEST REQUIRED</span>
        </div>

        <div class="test-item">
            <h3>5. Category and Phrase Selection</h3>
            <div class="instructions">
                <p><strong>Test Steps:</strong></p>
                <ol>
                    <li>Select multiple categories from the available options</li>
                    <li>Choose phrases from each selected category</li>
                    <li>Verify phrase selection interface works smoothly</li>
                    <li>Click "Start Recording" to proceed</li>
                </ol>
            </div>
            <span class="status pending">MANUAL TEST REQUIRED</span>
        </div>
    </div>

    <div class="test-section">
        <h2>📹 Video Recording & AWS Integration</h2>
        <div class="test-item critical">
            <h3>6. Camera Access and Recording</h3>
            <div class="instructions">
                <p><strong>Critical Test:</strong> Video recording functionality</p>
                <ol>
                    <li>Allow camera permissions when prompted</li>
                    <li>Verify camera feed appears in oval viewport</li>
                    <li>Check that 5-second countdown timer works</li>
                    <li>Record a complete 5-second video</li>
                    <li><strong>VERIFY:</strong> No debug elements visible (red boxes, debug info)</li>
                </ol>
            </div>
            <span class="status pending">CRITICAL TEST REQUIRED</span>
        </div>

        <div class="test-item critical">
            <h3>7. Real AWS S3 Upload Verification</h3>
            <div class="instructions">
                <p><strong>Critical Test:</strong> Actual S3 upload (not simulated)</p>
                <ol>
                    <li>Complete a video recording</li>
                    <li>Monitor browser Network tab for S3 upload requests</li>
                    <li>Verify upload completes successfully (no CORS errors)</li>
                    <li>Check AWS S3 bucket for new video file</li>
                    <li><strong>VERIFY:</strong> File appears with correct naming format</li>
                </ol>
            </div>
            <button class="test-button" onclick="checkS3Uploads()">Check Recent S3 Uploads</button>
            <div id="s3-uploads-result" class="results"></div>
            <span class="status pending">CRITICAL TEST REQUIRED</span>
        </div>

        <div class="test-item">
            <h3>8. Progress Tracking and Auto-Advance</h3>
            <div class="instructions">
                <p><strong>Test Steps:</strong></p>
                <ol>
                    <li>Record 3 videos for the same phrase</li>
                    <li>Verify auto-advance to next phrase after 3rd recording</li>
                    <li>Check localStorage persistence of recording counts</li>
                    <li>Verify progress indicators update correctly</li>
                </ol>
            </div>
            <span class="status pending">MANUAL TEST REQUIRED</span>
        </div>

        <div class="test-item">
            <h3>9. Completion Page and Share Link</h3>
            <div class="instructions">
                <p><strong>Test Steps:</strong></p>
                <ol>
                    <li>Complete all selected phrases (3 recordings each)</li>
                    <li>Verify completion page appears</li>
                    <li>Check reference number generation</li>
                    <li><strong>VERIFY:</strong> Share link shows "XXXXXX" placeholder</li>
                    <li>Test copy to clipboard functionality</li>
                </ol>
            </div>
            <span class="status pending">MANUAL TEST REQUIRED</span>
        </div>
    </div>

    <div class="test-section">
        <h2>🔒 Security and Environment Verification</h2>
        <div class="test-item">
            <h3>Environment Variables Check</h3>
            <button class="test-button" onclick="checkEnvironmentVars()">Check Environment Configuration</button>
            <div id="env-vars-result" class="results"></div>
        </div>
        <div class="test-item">
            <h3>Production Build Verification</h3>
            <button class="test-button" onclick="testProductionBuild()">Test Production Build</button>
            <div id="production-build-result" class="results"></div>
        </div>
    </div>

    <div class="test-section">
        <h2>✅ Deployment Readiness Checklist</h2>
        <div class="test-item">
            <div class="instructions">
                <h3>Pre-Deployment Requirements:</h3>
                <ul>
                    <li>✅ Backend server healthy and operational</li>
                    <li>✅ AWS S3 connectivity verified (115 recordings)</li>
                    <li>⏳ No persistent loading spinners</li>
                    <li>⏳ Debug elements completely removed</li>
                    <li>⏳ Real video uploads working</li>
                    <li>⏳ Complete user workflow functional</li>
                    <li>⏳ Share link shows XXXXXX placeholder</li>
                    <li>⏳ Production build generated successfully</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        document.getElementById('currentDate').textContent = new Date().toLocaleString();

        async function testBackendHealth() {
            const resultDiv = document.getElementById('backend-health-result');
            resultDiv.innerHTML = '<p>Testing backend health...</p>';
            
            try {
                const response = await fetch('http://localhost:5000/health');
                const data = await response.json();
                resultDiv.innerHTML = `
                    <h4>✅ Backend Health: ${data.status}</h4>
                    <p><strong>Uptime:</strong> ${Math.round(data.uptime)} seconds</p>
                    <p><strong>Environment:</strong> ${data.environment}</p>
                    <p><strong>Services:</strong> ${JSON.stringify(data.services)}</p>
                `;
            } catch (error) {
                resultDiv.innerHTML = `<h4>❌ Backend Health Check Failed</h4><p>Error: ${error.message}</p>`;
            }
        }

        async function testFrontendAccess() {
            const resultDiv = document.getElementById('frontend-access-result');
            resultDiv.innerHTML = '<p>Testing frontend access...</p>';
            
            try {
                const response = await fetch('http://localhost:3000');
                if (response.ok) {
                    resultDiv.innerHTML = `
                        <h4>✅ Frontend Access: OK</h4>
                        <p><strong>Status:</strong> ${response.status} ${response.statusText}</p>
                        <p><strong>Content-Type:</strong> ${response.headers.get('content-type')}</p>
                    `;
                } else {
                    resultDiv.innerHTML = `<h4>❌ Frontend Access Failed</h4><p>Status: ${response.status}</p>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<h4>❌ Frontend Access Failed</h4><p>Error: ${error.message}</p>`;
            }
        }

        async function testS3Connectivity() {
            const resultDiv = document.getElementById('s3-connectivity-result');
            resultDiv.innerHTML = '<p>Testing S3 connectivity...</p>';
            
            try {
                const response = await fetch('http://localhost:5000/api/test-s3');
                const data = await response.json();
                resultDiv.innerHTML = `
                    <h4>✅ S3 Connectivity: ${data.success ? 'SUCCESS' : 'FAILED'}</h4>
                    <p><strong>Bucket:</strong> ${data.bucket}</p>
                    <p><strong>Region:</strong> ${data.region}</p>
                    <p><strong>Object Count:</strong> ${data.objectCount}</p>
                    <p><strong>Sample Files:</strong> ${data.sampleFiles ? data.sampleFiles.length : 0} files found</p>
                `;
            } catch (error) {
                resultDiv.innerHTML = `<h4>❌ S3 Connectivity Failed</h4><p>Error: ${error.message}</p>`;
            }
        }

        async function checkS3Uploads() {
            const resultDiv = document.getElementById('s3-uploads-result');
            resultDiv.innerHTML = '<p>Checking recent S3 uploads...</p>';
            
            try {
                const response = await fetch('http://localhost:5000/api/sample-counts');
                const data = await response.json();
                resultDiv.innerHTML = `
                    <h4>✅ S3 Upload Status</h4>
                    <p><strong>Total Recordings:</strong> ${data.counts.total}</p>
                    <p><strong>By Gender:</strong> Male: ${data.counts.byGender.male}, Female: ${data.counts.byGender.female}</p>
                    <p><strong>By Age Group:</strong> 18-39: ${data.counts.byAgeGroup['18to39']}, 40-64: ${data.counts.byAgeGroup['40to64']}, 65+: ${data.counts.byAgeGroup['65plus']}</p>
                    <p><strong>Last Updated:</strong> ${data.lastUpdated}</p>
                `;
            } catch (error) {
                resultDiv.innerHTML = `<h4>❌ S3 Upload Check Failed</h4><p>Error: ${error.message}</p>`;
            }
        }

        async function checkEnvironmentVars() {
            const resultDiv = document.getElementById('env-vars-result');
            resultDiv.innerHTML = `
                <h4>Environment Variables Status</h4>
                <p><strong>NODE_ENV:</strong> ${process.env.NODE_ENV || 'development'}</p>
                <p><strong>AWS Region:</strong> ${process.env.REACT_APP_AWS_REGION || 'Not set'}</p>
                <p><strong>S3 Bucket:</strong> ${process.env.REACT_APP_S3_BUCKET || 'Not set'}</p>
                <p><strong>Identity Pool:</strong> ${process.env.REACT_APP_AWS_IDENTITY_POOL_ID ? 'Configured' : 'Not set'}</p>
                <p><strong>Debug Mode:</strong> ${process.env.REACT_APP_DEBUG || 'false'}</p>
            `;
        }

        async function testProductionBuild() {
            const resultDiv = document.getElementById('production-build-result');
            resultDiv.innerHTML = '<p>Checking production build status...</p>';
            
            // Check if build directory exists by trying to access a build file
            try {
                const response = await fetch('/static/js/main.252a1f50.js', { method: 'HEAD' });
                if (response.ok) {
                    resultDiv.innerHTML = `
                        <h4>✅ Production Build: Ready</h4>
                        <p>Build files are accessible and optimized for deployment</p>
                        <p><strong>Build Directory:</strong> /build (ready for Netlify drag-and-drop)</p>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <h4>⚠️ Production Build: Needs Verification</h4>
                        <p>Run 'npm run build' to generate production build</p>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <h4>⚠️ Production Build: Needs Generation</h4>
                    <p>Run 'npm run build' to create deployment-ready files</p>
                `;
            }
        }

        // Auto-run server tests on page load
        window.onload = function() {
            testBackendHealth();
            testFrontendAccess();
            testS3Connectivity();
            checkEnvironmentVars();
        };
    </script>
</body>
</html>
