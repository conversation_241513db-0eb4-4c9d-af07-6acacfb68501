/**
 * Test Receipt Mapping AWS Connectivity
 * 
 * This script tests the AWS S3 connectivity for the receipt mapping service
 * to identify why receipt-log.json is not being created in the S3 bucket.
 */

const { S3Client, GetObjectCommand, PutObjectCommand, ListObjectsV2Command } = require('@aws-sdk/client-s3');
const { fromCognitoIdentityPool } = require('@aws-sdk/credential-provider-cognito-identity');
const { CognitoIdentityClient } = require('@aws-sdk/client-cognito-identity');

// Load environment variables
require('dotenv').config();

console.log('🧪 === RECEIPT MAPPING AWS CONNECTIVITY TEST ===');
console.log('');

// Configuration
const AWS_REGION = process.env.REACT_APP_AWS_REGION || 'ap-southeast-2';
const BUCKET_NAME = process.env.REACT_APP_S3_BUCKET || 'icudatasetphrasesfortesting';
const IDENTITY_POOL_ID = process.env.REACT_APP_AWS_IDENTITY_POOL_ID;
const RECEIPT_LOG_KEY = 'receipt-numbers/receipt-log.json';

console.log('🔧 Configuration:');
console.log(`  AWS Region: ${AWS_REGION}`);
console.log(`  S3 Bucket: ${BUCKET_NAME}`);
console.log(`  Identity Pool ID: ${IDENTITY_POOL_ID ? IDENTITY_POOL_ID.substring(0, 20) + '...' : 'NOT SET'}`);
console.log(`  Receipt Log Key: ${RECEIPT_LOG_KEY}`);
console.log('');

// Test 1: Initialize S3 Client
console.log('📡 Test 1: Initialize S3 Client');
console.log('================================');

let s3Client = null;

try {
  if (!IDENTITY_POOL_ID) {
    throw new Error('REACT_APP_AWS_IDENTITY_POOL_ID not configured');
  }

  s3Client = new S3Client({
    region: AWS_REGION,
    credentials: fromCognitoIdentityPool({
      client: new CognitoIdentityClient({ region: AWS_REGION }),
      identityPoolId: IDENTITY_POOL_ID,
    }),
  });
  
  console.log('✅ S3 Client initialized successfully');
} catch (error) {
  console.error('❌ Failed to initialize S3 client:', error.message);
  process.exit(1);
}

console.log('');

// Test 2: Check if receipt-numbers folder exists
console.log('📁 Test 2: Check receipt-numbers folder');
console.log('=======================================');

async function checkReceiptFolder() {
  try {
    const listCommand = new ListObjectsV2Command({
      Bucket: BUCKET_NAME,
      Prefix: 'receipt-numbers/',
      MaxKeys: 10
    });

    const response = await s3Client.send(listCommand);
    
    console.log(`✅ receipt-numbers/ folder accessible`);
    console.log(`📊 Objects found: ${response.Contents ? response.Contents.length : 0}`);
    
    if (response.Contents && response.Contents.length > 0) {
      console.log('📄 Existing files:');
      response.Contents.forEach(obj => {
        console.log(`  - ${obj.Key} (${obj.Size} bytes, ${obj.LastModified})`);
      });
    } else {
      console.log('📄 No files found in receipt-numbers/ folder');
    }
    
    return true;
  } catch (error) {
    console.error('❌ Error checking receipt-numbers folder:', error.message);
    return false;
  }
}

// Test 3: Try to read existing receipt log
console.log('📋 Test 3: Check for existing receipt-log.json');
console.log('==============================================');

async function checkExistingLog() {
  try {
    const getCommand = new GetObjectCommand({
      Bucket: BUCKET_NAME,
      Key: RECEIPT_LOG_KEY
    });

    const response = await s3Client.send(getCommand);
    const logData = await response.Body.transformToString();
    const receiptLog = JSON.parse(logData);
    
    console.log('✅ receipt-log.json exists');
    console.log(`📊 Receipts found: ${Object.keys(receiptLog).length}`);
    console.log('📄 Receipt numbers:', Object.keys(receiptLog).join(', '));
    
    return receiptLog;
  } catch (error) {
    if (error.name === 'NoSuchKey') {
      console.log('📋 receipt-log.json does not exist (this is expected for first run)');
      return {};
    }
    console.error('❌ Error reading receipt-log.json:', error.message);
    return null;
  }
}

// Test 4: Create test receipt log
console.log('💾 Test 4: Create test receipt log');
console.log('==================================');

async function createTestLog() {
  try {
    // Create a test receipt log with receipt 000004 (as mentioned by user)
    const testReceiptLog = {
      "000004": {
        timestamp: new Date().toISOString(),
        videos: [
          `https://s3.amazonaws.com/${BUCKET_NAME}/icu-videos/25to40/female/caucasian/hello/hello__user01__25to40__female__caucasian__${new Date().toISOString().replace(/[:.]/g, '')}.webm`
        ],
        demographics: {
          age: "25to40",
          gender: "female",
          ethnicity: "caucasian"
        },
        sessionId: "user01",
        assignmentType: "prospective",
        recordingCount: 1,
        testEntry: true
      }
    };

    const putCommand = new PutObjectCommand({
      Bucket: BUCKET_NAME,
      Key: RECEIPT_LOG_KEY,
      Body: JSON.stringify(testReceiptLog, null, 2),
      ContentType: 'application/json',
      Metadata: {
        'created-by': 'test-script',
        'test-timestamp': new Date().toISOString(),
        'total-receipts': '1'
      }
    });

    await s3Client.send(putCommand);
    console.log('✅ Test receipt log created successfully');
    console.log('📄 Created receipt 000004 with test data');
    
    return true;
  } catch (error) {
    console.error('❌ Error creating test receipt log:', error.message);
    console.error('Full error:', error);
    return false;
  }
}

// Test 5: Verify creation
console.log('🔍 Test 5: Verify receipt log creation');
console.log('======================================');

async function verifyCreation() {
  try {
    const getCommand = new GetObjectCommand({
      Bucket: BUCKET_NAME,
      Key: RECEIPT_LOG_KEY
    });

    const response = await s3Client.send(getCommand);
    const logData = await response.Body.transformToString();
    const receiptLog = JSON.parse(logData);
    
    console.log('✅ receipt-log.json verified');
    console.log('📊 Content preview:');
    console.log(JSON.stringify(receiptLog, null, 2));
    
    return true;
  } catch (error) {
    console.error('❌ Error verifying receipt log:', error.message);
    return false;
  }
}

// Run all tests
async function runTests() {
  try {
    console.log('');
    
    const folderCheck = await checkReceiptFolder();
    console.log('');
    
    const existingLog = await checkExistingLog();
    console.log('');
    
    if (existingLog !== null && Object.keys(existingLog).length === 0) {
      console.log('🔧 No existing receipt log found, creating test log...');
      const created = await createTestLog();
      console.log('');
      
      if (created) {
        await verifyCreation();
      }
    } else if (existingLog !== null) {
      console.log('📋 Existing receipt log found, no need to create test log');
    }
    
    console.log('');
    console.log('🎯 SUMMARY');
    console.log('==========');
    console.log('✅ AWS S3 connectivity: Working');
    console.log('✅ receipt-numbers/ folder: Accessible');
    console.log('✅ Receipt log operations: Functional');
    console.log('');
    console.log('💡 If receipt mapping is still not working in the app, the issue is likely:');
    console.log('   1. Receipt mapping service not being called during video upload');
    console.log('   2. Error handling preventing S3 writes');
    console.log('   3. Frontend/backend upload mode configuration');
    
  } catch (error) {
    console.error('❌ Test execution failed:', error);
  }
}

// Execute tests
runTests();
