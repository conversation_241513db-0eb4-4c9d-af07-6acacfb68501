<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Callback Chain</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .debug { background-color: #fff3cd; border: 2px solid #ffc107; color: #856404; }
        .success { background-color: #d4edda; border: 2px solid #28a745; color: #155724; }
        .error { background-color: #f8d7da; border: 2px solid #dc3545; color: #721c24; }
        .info { background-color: #d1ecf1; border: 2px solid #17a2b8; color: #0c5460; }
        .code {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            font-size: 11px;
            max-height: 300px;
            overflow-y: auto;
        }
        .step {
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
            border-left: 4px solid #007bff;
            background-color: #f8f9fa;
        }
        button {
            background-color: #ffc107;
            color: #212529;
            border: none;
            padding: 15px 30px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px;
            font-size: 16px;
            font-weight: bold;
        }
        button:hover { background-color: #e0a800; }
        .test-button {
            background-color: #17a2b8;
            color: white;
        }
        .test-button:hover { background-color: #138496; }
    </style>
</head>
<body>
    <div class="container debug">
        <h1>🔍 Debug Auto-Advance Callback Chain</h1>
        <p><strong>Purpose:</strong> Trace the recording completion flow to identify where auto-advance is failing</p>
    </div>

    <div class="container info">
        <h2>📋 Expected Callback Chain</h2>
        <div class="step">
            <strong>Step 1:</strong> VideoRecorder.handleRecordingComplete() → calls onRecordingComplete(savedData, metadata, qualityCheck)
        </div>
        <div class="step">
            <strong>Step 2:</strong> RecordingSessionManager.handleVideoRecorded() → receives the callback
        </div>
        <div class="step">
            <strong>Step 3:</strong> RecordingSessionManager calls recordingCompleted(metadata)
        </div>
        <div class="step">
            <strong>Step 4:</strong> RecordingSessionProvider.recordingCompleted() → updates state
        </div>
        <div class="step">
            <strong>Step 5:</strong> useEffect in RecordingSessionProvider → detects state change
        </div>
        <div class="step">
            <strong>Step 6:</strong> handleNextPhrase() → advances to next phrase
        </div>
    </div>

    <div class="container">
        <h2>🧪 Debug Test</h2>
        <p>Complete a recording and monitor the console for these specific logs:</p>
        
        <button class="test-button" onclick="startDebugTest()">
            🔍 START DEBUG TEST
        </button>
        
        <button onclick="clearConsole()">
            🧹 Clear Console
        </button>
    </div>

    <div class="container">
        <h2>🔍 Expected Console Logs (In Order)</h2>
        <div class="code">1. VideoRecorder logs:
   🚀 Calling onRecordingComplete...
   📊 VideoRecorder calling parent with data:
   ✅ onRecordingComplete called successfully

2. RecordingSessionManager logs:
   🎯 === RECORDING SESSION MANAGER: handleVideoRecorded called ===
   🔗 RECORDING SESSION MANAGER: About to call recordingCompleted with metadata:
   🔗 RECORDING SESSION MANAGER: recordingCompleted call completed

3. RecordingSessionProvider logs:
   📹 RECORDING COMPLETED FUNCTION CALLED:
   📹 RECORDING COMPLETED - COUNT UPDATE:
   ✅ Recording X/3 completed for phrase: [phrase name]
   🎯 PHRASE COMPLETION DETECTED - 3 recordings completed (after 3rd recording)

4. Auto-advance logs:
   🔄 AUTO-ADVANCE EFFECT TRIGGERED:
   🎯 AUTO-ADVANCE: Phrase completion detected, calling handleNextPhrase
   🚀 === HANDLE NEXT PHRASE CALLED ===
   📝 ADVANCING TO NEXT PHRASE</div>
    </div>

    <div class="container error">
        <h2>🚨 Missing Logs Indicate Issues</h2>
        <ul>
            <li><strong>Missing VideoRecorder logs:</strong> onRecordingComplete not being called</li>
            <li><strong>Missing RecordingSessionManager logs:</strong> handleVideoRecorded not receiving callback</li>
            <li><strong>Missing RecordingSessionProvider logs:</strong> recordingCompleted not being called</li>
            <li><strong>Missing auto-advance logs:</strong> useEffect not triggering or handleNextPhrase not working</li>
        </ul>
    </div>

    <div id="debugResults" class="container" style="display: none;">
        <h2>📊 Debug Results</h2>
        <div id="results"></div>
    </div>

    <div class="container">
        <h2>🔧 Debugging Steps</h2>
        <ol>
            <li><strong>Open Application:</strong> Navigate to recording interface</li>
            <li><strong>Open Console:</strong> Press F12 and go to Console tab</li>
            <li><strong>Clear Console:</strong> Click "Clear Console" button above</li>
            <li><strong>Record Video:</strong> Complete one recording</li>
            <li><strong>Check Logs:</strong> Compare actual logs with expected logs above</li>
            <li><strong>Identify Gap:</strong> Find where the callback chain breaks</li>
        </ol>
    </div>

    <div class="container success">
        <h2>✅ Success Criteria</h2>
        <p>All expected logs should appear in sequence. If any are missing, that indicates where the callback chain is broken.</p>
    </div>

    <script>
        let logBuffer = [];
        let originalConsoleLog = console.log;
        let originalConsoleError = console.error;
        let originalConsoleWarn = console.warn;

        function startDebugTest() {
            console.log('🔍 STARTING DEBUG TEST FOR AUTO-ADVANCE CALLBACK CHAIN');
            console.log('=======================================================');
            
            // Override console methods to capture logs
            console.log = function(...args) {
                originalConsoleLog.apply(console, args);
                const message = args.join(' ');
                logBuffer.push(`[LOG] ${new Date().toLocaleTimeString()}: ${message}`);
                updateDebugDisplay();
            };

            console.error = function(...args) {
                originalConsoleError.apply(console, args);
                const message = args.join(' ');
                logBuffer.push(`[ERROR] ${new Date().toLocaleTimeString()}: ${message}`);
                updateDebugDisplay();
            };

            console.warn = function(...args) {
                originalConsoleWarn.apply(console, args);
                const message = args.join(' ');
                logBuffer.push(`[WARN] ${new Date().toLocaleTimeString()}: ${message}`);
                updateDebugDisplay();
            };

            // Open the application
            const appWindow = window.open('http://localhost:3000', '_blank');
            
            // Show debug results
            document.getElementById('debugResults').style.display = 'block';
            const results = document.getElementById('results');
            
            results.innerHTML = `
                <div class="info" style="padding: 15px; border-radius: 5px;">
                    <h3>🔍 Debug Test Started</h3>
                    <p><strong>Application opened in new tab</strong></p>
                    <p>📋 Complete a recording and watch for callback chain logs</p>
                    <p>🔍 Logs will appear below as they are captured</p>
                    <div id="liveLog" class="code" style="margin-top: 10px; min-height: 100px;">
                        Waiting for logs...
                    </div>
                </div>
            `;
            
            console.log('🎯 Debug test ready - complete a recording to trace the callback chain');
        }

        function updateDebugDisplay() {
            const liveLog = document.getElementById('liveLog');
            if (liveLog) {
                // Filter for relevant logs
                const relevantLogs = logBuffer.filter(log => 
                    log.includes('onRecordingComplete') ||
                    log.includes('handleVideoRecorded') ||
                    log.includes('recordingCompleted') ||
                    log.includes('RECORDING COMPLETED') ||
                    log.includes('AUTO-ADVANCE') ||
                    log.includes('HANDLE NEXT PHRASE') ||
                    log.includes('VideoRecorder calling parent') ||
                    log.includes('RECORDING SESSION MANAGER')
                );
                
                liveLog.textContent = relevantLogs.slice(-20).join('\n') || 'No relevant logs captured yet...';
                liveLog.scrollTop = liveLog.scrollHeight;
            }
        }

        function clearConsole() {
            logBuffer = [];
            console.clear();
            originalConsoleLog('🧹 Console cleared - ready for fresh debug session');
            
            const liveLog = document.getElementById('liveLog');
            if (liveLog) {
                liveLog.textContent = 'Console cleared - waiting for new logs...';
            }
        }

        // Auto-start monitoring
        window.addEventListener('load', () => {
            console.log('🔍 DEBUG CALLBACK CHAIN PAGE LOADED');
            console.log('====================================');
            console.log('Ready to trace auto-advance callback chain');
            console.log('Click "START DEBUG TEST" to begin monitoring');
        });
    </script>
</body>
</html>
