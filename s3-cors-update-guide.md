# 🚀 AWS S3 CORS Policy Update Guide

## 📋 Overview
Update the AWS S3 bucket CORS policy to allow video uploads from your production domain "icuphrasecollection.com".

## ✅ Updated CORS Policy
The `s3-cors-policy.json` file has been updated with your production domain and includes:
- ✅ `https://icuphrasecollection.com` (primary HTTPS)
- ✅ `http://icuphrasecollection.com` (HTTP fallback)
- ✅ `https://www.icuphrasecollection.com` (www subdomain HTTPS)
- ✅ `http://www.icuphrasecollection.com` (www subdomain HTTP)
- ✅ Development domains (localhost)
- ✅ Netlify wildcard domain (backup)

## 🔧 Step-by-Step Instructions

### Step 1: Access AWS S3 Console
1. Go to [AWS S3 Console](https://s3.console.aws.amazon.com/)
2. Sign in with your AWS credentials
3. Locate and click on your bucket: **`icudatasetphrasesfortesting`**

### Step 2: Navigate to CORS Settings
1. Click on the **`icudatasetphrasesfortesting`** bucket name
2. Click on the **"Permissions"** tab
3. Scroll down to find **"Cross-origin resource sharing (CORS)"** section
4. Click the **"Edit"** button

### Step 3: Apply Updated CORS Policy
1. **Clear existing CORS configuration** (if any)
2. **Copy the entire contents** of `s3-cors-policy.json`
3. **Paste into the CORS configuration editor**
4. **Click "Save changes"**

### Step 4: Verify CORS Policy Applied
- You should see a green success message
- The CORS configuration should show your updated policy
- Wait 2-3 minutes for changes to propagate globally

## 📝 CORS Policy Explanation

### Allowed Origins
- **Production domains:** Both HTTP and HTTPS versions of your domain
- **Development domains:** localhost ports for testing
- **Netlify backup:** Wildcard for any Netlify subdomain

### Allowed Methods
- **GET:** Download files and check bucket access
- **PUT:** Upload video files directly from browser
- **POST:** Alternative upload method
- **DELETE:** File management (if needed)
- **HEAD:** Check file existence and metadata

### Exposed Headers
- **ETag:** File integrity verification
- **x-amz-server-side-encryption:** Encryption status
- **x-amz-request-id:** Request tracking
- **x-amz-id-2:** Extended request ID
- **x-amz-version-id:** Object versioning

### MaxAgeSeconds
- **3000 seconds (50 minutes):** Browser cache duration for CORS preflight

## ⚠️ Important Notes

### Security Considerations
- Only your specific domains are allowed
- No wildcard (*) origins for security
- All necessary headers are exposed for upload functionality

### Browser Compatibility
- Supports all modern browsers
- Handles both simple and preflight CORS requests
- Compatible with AWS SDK and direct fetch requests

## 🧪 Testing After CORS Update

### 1. Test Video Upload
1. Go to `https://icuphrasecollection.com`
2. Complete consent and demographics
3. Record a test video
4. Verify upload completes without CORS errors

### 2. Check Browser DevTools
1. Open DevTools → Network tab
2. Look for any CORS-related errors
3. Verify S3 upload requests show status 200

### 3. Verify in S3 Bucket
1. Check that new videos appear in bucket
2. Confirm proper folder structure
3. Verify file permissions and metadata

## 🚨 Troubleshooting

### If CORS Errors Persist:
1. **Wait 5-10 minutes** for global propagation
2. **Clear browser cache** completely
3. **Try incognito/private browsing** mode
4. **Check exact domain spelling** in CORS policy

### Common Issues:
- **Mixed content:** Ensure HTTPS is used consistently
- **Subdomain mismatch:** Both www and non-www versions included
- **Cache issues:** Clear browser and CDN caches

## ✅ Success Criteria
Your CORS update is successful when:
- ✅ Video uploads work from production domain
- ✅ No CORS errors in browser console
- ✅ Videos appear in S3 bucket after upload
- ✅ Application functions normally on icuphrasecollection.com

---

**Next Step:** Apply the CORS policy to your S3 bucket using the instructions above.
