<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ICU App Comprehensive Diagnostic</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 { color: #2c3e50; text-align: center; }
        .test-result {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .success { background: #d5f4e6; color: #27ae60; border: 1px solid #27ae60; }
        .error { background: #fadbd8; color: #e74c3c; border: 1px solid #e74c3c; }
        .warning { background: #fef9e7; color: #f39c12; border: 1px solid #f39c12; }
        .info { background: #d6eaf8; color: #2980b9; border: 1px solid #2980b9; }
        button {
            background: #3498db;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #2980b9; }
        .critical { background: #e74c3c !important; }
        #app-preview {
            width: 100%;
            height: 400px;
            border: 2px solid #ddd;
            margin: 20px 0;
            background: white;
        }
        .code-block {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            margin: 10px 0;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 ICU Dataset Application - Comprehensive Diagnostic</h1>
        
        <div id="test-results"></div>
        
        <div style="text-align: center; margin: 20px 0;">
            <button onclick="runFullDiagnostic()" class="critical">🚨 RUN FULL DIAGNOSTIC</button>
            <button onclick="testDirectAccess()">Test Direct Access</button>
            <button onclick="testJavaScriptExecution()">Test JavaScript</button>
            <button onclick="clearResults()">Clear Results</button>
        </div>
        
        <h2>🖼️ Application Preview (What User Actually Sees):</h2>
        <iframe id="app-preview" src="about:blank"></iframe>
        
        <div id="manual-instructions" style="display: none;">
            <h2>📋 Manual Verification Steps</h2>
            <ol>
                <li><strong>Open New Tab:</strong> Navigate to <code>http://localhost:3000</code></li>
                <li><strong>Check What You See:</strong>
                    <ul>
                        <li>✅ ICU Dataset Application interface with consent page</li>
                        <li>❌ Blank white page</li>
                        <li>❌ Loading spinner that never stops</li>
                        <li>❌ Error message</li>
                    </ul>
                </li>
                <li><strong>Open Browser Console (F12):</strong> Look for red error messages</li>
                <li><strong>Check Network Tab:</strong> Look for failed requests (red entries)</li>
            </ol>
        </div>
    </div>

    <script>
        function addResult(message, type = 'info') {
            const results = document.getElementById('test-results');
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = message;
            results.appendChild(div);
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function clearResults() {
            document.getElementById('test-results').innerHTML = '';
        }

        async function testDirectAccess() {
            addResult('🔍 Testing direct application access...', 'info');
            
            try {
                const response = await fetch('http://localhost:3000');
                const html = await response.text();
                
                if (response.ok) {
                    addResult(`✅ HTTP ${response.status}: Application server responding`, 'success');
                    
                    // Check HTML structure
                    if (html.includes('<div id="root">')) {
                        addResult('✅ HTML: Root div found', 'success');
                    } else {
                        addResult('❌ HTML: No root div found', 'error');
                    }
                    
                    if (html.includes('bundle.js')) {
                        addResult('✅ HTML: JavaScript bundle referenced', 'success');
                    } else {
                        addResult('❌ HTML: No JavaScript bundle found', 'error');
                    }
                    
                    // Test JavaScript bundle
                    const jsResponse = await fetch('http://localhost:3000/static/js/bundle.js');
                    if (jsResponse.ok) {
                        addResult(`✅ JS Bundle: Accessible (${(jsResponse.headers.get('content-length')/1024/1024).toFixed(1)}MB)`, 'success');
                    } else {
                        addResult(`❌ JS Bundle: HTTP ${jsResponse.status}`, 'error');
                    }
                    
                } else {
                    addResult(`❌ HTTP ${response.status}: Server error`, 'error');
                }
            } catch (error) {
                addResult(`❌ Network Error: ${error.message}`, 'error');
            }
        }

        async function testJavaScriptExecution() {
            addResult('🔍 Testing JavaScript execution in iframe...', 'info');
            
            const iframe = document.getElementById('app-preview');
            iframe.src = 'http://localhost:3000';
            
            return new Promise((resolve) => {
                iframe.onload = function() {
                    setTimeout(() => {
                        try {
                            const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                            const root = iframeDoc.getElementById('root');
                            
                            if (root) {
                                if (root.innerHTML.trim()) {
                                    addResult('✅ React App: Content rendered in root div', 'success');
                                    addResult(`📝 Content Preview: ${root.innerHTML.substring(0, 100)}...`, 'info');
                                } else {
                                    addResult('❌ React App: Root div is empty (JavaScript not executing)', 'error');
                                }
                            } else {
                                addResult('❌ React App: Root div not found', 'error');
                            }
                            
                            // Check for React
                            const hasReact = iframe.contentWindow.React !== undefined;
                            if (hasReact) {
                                addResult('✅ React: Library loaded', 'success');
                            } else {
                                addResult('❌ React: Library not loaded', 'error');
                            }
                            
                        } catch (e) {
                            addResult('⚠️ Cannot inspect iframe content (CORS restriction)', 'warning');
                            addResult('💡 Please manually check http://localhost:3000 in a new tab', 'info');
                        }
                        resolve();
                    }, 3000); // Wait 3 seconds for React to load
                };
                
                iframe.onerror = function() {
                    addResult('❌ Failed to load application in iframe', 'error');
                    resolve();
                };
            });
        }

        async function testBackendConnectivity() {
            addResult('🔍 Testing backend connectivity...', 'info');
            
            try {
                const response = await fetch('http://localhost:5000/health');
                if (response.ok) {
                    const data = await response.json();
                    addResult(`✅ Backend: ${data.status} (uptime: ${Math.round(data.uptime)}s)`, 'success');
                } else {
                    addResult(`❌ Backend: HTTP ${response.status}`, 'error');
                }
            } catch (error) {
                addResult(`❌ Backend: ${error.message}`, 'error');
            }
        }

        async function runFullDiagnostic() {
            clearResults();
            addResult('🚨 STARTING COMPREHENSIVE DIAGNOSTIC', 'info');
            addResult('This will test what the user actually sees when accessing the application', 'info');
            
            await testDirectAccess();
            await testBackendConnectivity();
            await testJavaScriptExecution();
            
            addResult('🏁 DIAGNOSTIC COMPLETE', 'info');
            addResult('📋 If the application is not working, check the results above for specific issues', 'warning');
            
            // Show manual instructions
            document.getElementById('manual-instructions').style.display = 'block';
        }

        // Auto-run basic tests
        window.addEventListener('load', function() {
            setTimeout(runFullDiagnostic, 1000);
        });
    </script>
</body>
</html>
