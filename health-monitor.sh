#!/bin/bash

# ICU Dataset Application Health Monitor
# Monitors both backend and frontend services and restarts if needed

LOG_FILE="./logs/health-monitor.log"
BACKEND_URL="http://localhost:5000/health"
FRONTEND_URL="http://localhost:3000"
CHECK_INTERVAL=300  # 5 minutes

# Create logs directory if it doesn't exist
mkdir -p logs

# Function to log with timestamp
log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S'): $1" | tee -a "$LOG_FILE"
}

# Function to check backend health
check_backend() {
    if curl -f -s --max-time 10 "$BACKEND_URL" > /dev/null 2>&1; then
        log_message "✅ Backend health check passed"
        return 0
    else
        log_message "❌ Backend health check failed"
        return 1
    fi
}

# Function to check frontend health
check_frontend() {
    if curl -f -s --max-time 10 "$FRONTEND_URL" > /dev/null 2>&1; then
        log_message "✅ Frontend health check passed"
        return 0
    else
        log_message "❌ Frontend health check failed"
        return 1
    fi
}

# Function to check S3 API specifically
check_s3_api() {
    if curl -f -s --max-time 15 "http://localhost:5000/api/sample-counts" > /dev/null 2>&1; then
        log_message "✅ S3 API health check passed"
        return 0
    else
        log_message "❌ S3 API health check failed"
        return 1
    fi
}

# Function to restart backend
restart_backend() {
    log_message "🔄 Restarting backend service..."
    pm2 restart icu-backend
    sleep 10
    if check_backend; then
        log_message "✅ Backend restart successful"
    else
        log_message "❌ Backend restart failed"
    fi
}

# Function to restart frontend
restart_frontend() {
    log_message "🔄 Restarting frontend service..."
    pm2 restart icu-frontend
    sleep 30  # Frontend takes longer to start
    if check_frontend; then
        log_message "✅ Frontend restart successful"
    else
        log_message "❌ Frontend restart failed"
    fi
}

# Function to send alert (placeholder for future notification system)
send_alert() {
    local message="$1"
    log_message "🚨 ALERT: $message"
    # Future: Add email/Slack/SMS notifications here
}

# Main monitoring loop
log_message "🚀 Starting ICU Dataset Application Health Monitor"
log_message "📊 Monitoring backend: $BACKEND_URL"
log_message "📊 Monitoring frontend: $FRONTEND_URL"
log_message "⏰ Check interval: ${CHECK_INTERVAL} seconds"

while true; do
    log_message "🔍 Starting health check cycle..."
    
    # Check backend
    if ! check_backend; then
        send_alert "Backend service is down"
        restart_backend
    fi
    
    # Check S3 API specifically (critical for progress tracking)
    if ! check_s3_api; then
        send_alert "S3 API is not responding"
        restart_backend
    fi
    
    # Check frontend
    if ! check_frontend; then
        send_alert "Frontend service is down"
        restart_frontend
    fi
    
    # Check PM2 process status
    if ! pm2 list | grep -q "icu-backend.*online"; then
        send_alert "Backend PM2 process not running"
        pm2 start icu-backend
    fi
    
    if ! pm2 list | grep -q "icu-frontend.*online"; then
        send_alert "Frontend PM2 process not running"
        pm2 start icu-frontend
    fi
    
    log_message "✅ Health check cycle completed"
    log_message "⏳ Waiting ${CHECK_INTERVAL} seconds until next check..."
    
    sleep $CHECK_INTERVAL
done
