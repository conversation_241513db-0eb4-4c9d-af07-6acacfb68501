<!DOCTYPE html>
<html>
<head>
    <title>Backend Connection Test</title>
</head>
<body>
    <h1>Backend Connection Test</h1>
    <button onclick="testConnection()">Test Backend Connection</button>
    <div id="result"></div>

    <script>
        async function testConnection() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Testing...';
            
            try {
                console.log('Testing backend connection...');
                const backendUrl = 'http://localhost:5000';
                
                // Test health endpoint
                const response = await fetch(`${backendUrl}/health`, {
                    method: 'GET'
                });
                
                if (response.ok) {
                    const data = await response.json();
                    resultDiv.innerHTML = `
                        <h2>✅ Backend Connection Successful!</h2>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                    console.log('✅ Backend connectivity test passed:', data);
                } else {
                    resultDiv.innerHTML = `
                        <h2>❌ Backend Connection Failed</h2>
                        <p>Status: ${response.status}</p>
                    `;
                    console.warn('⚠️ Backend health check failed:', response.status);
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <h2>❌ Backend Connection Error</h2>
                    <p>Error: ${error.message}</p>
                `;
                console.error('❌ Backend connectivity test failed:', error);
            }
        }
        
        // Auto-test on page load
        window.onload = testConnection;
    </script>
</body>
</html>
