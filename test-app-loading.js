const puppeteer = require('puppeteer');

async function testAppLoading() {
    console.log('🔍 Testing ICU Dataset Application Loading...');
    
    let browser;
    try {
        // Launch browser
        browser = await puppeteer.launch({ 
            headless: true,
            args: ['--no-sandbox', '--disable-setuid-sandbox']
        });
        
        const page = await browser.newPage();
        
        // Listen for console messages
        page.on('console', msg => {
            console.log(`📝 BROWSER CONSOLE [${msg.type()}]:`, msg.text());
        });
        
        // Listen for errors
        page.on('error', err => {
            console.log('❌ PAGE ERROR:', err.message);
        });
        
        page.on('pageerror', err => {
            console.log('❌ PAGE SCRIPT ERROR:', err.message);
        });
        
        // Navigate to the application
        console.log('🌐 Navigating to http://localhost:3000...');
        await page.goto('http://localhost:3000', { 
            waitUntil: 'networkidle0',
            timeout: 30000 
        });
        
        // Wait for React to load
        console.log('⏳ Waiting for React application to load...');
        await page.waitForTimeout(3000);
        
        // Check if root div has content
        const rootContent = await page.evaluate(() => {
            const root = document.getElementById('root');
            return {
                exists: !!root,
                hasContent: root ? root.innerHTML.length > 0 : false,
                innerHTML: root ? root.innerHTML.substring(0, 200) : null
            };
        });
        
        console.log('📊 Root div analysis:', rootContent);
        
        // Check for specific ICU app elements
        const appElements = await page.evaluate(() => {
            return {
                hasConsentPage: document.querySelector('[data-testid="consent-page"]') !== null,
                hasAppTitle: document.title.includes('ICU'),
                hasButtons: document.querySelectorAll('button').length,
                hasInputs: document.querySelectorAll('input').length,
                bodyText: document.body.innerText.substring(0, 500)
            };
        });
        
        console.log('🎯 App elements found:', appElements);
        
        // Take a screenshot
        await page.screenshot({ path: 'app-screenshot.png', fullPage: true });
        console.log('📸 Screenshot saved as app-screenshot.png');
        
        // Check network requests
        const responses = [];
        page.on('response', response => {
            responses.push({
                url: response.url(),
                status: response.status(),
                ok: response.ok()
            });
        });
        
        // Reload to capture network requests
        await page.reload({ waitUntil: 'networkidle0' });
        
        console.log('🌐 Network requests:');
        responses.forEach(resp => {
            const status = resp.ok ? '✅' : '❌';
            console.log(`  ${status} ${resp.status} ${resp.url}`);
        });
        
        console.log('✅ Test completed successfully');
        
    } catch (error) {
        console.error('❌ Test failed:', error.message);
    } finally {
        if (browser) {
            await browser.close();
        }
    }
}

// Check if puppeteer is available
try {
    testAppLoading();
} catch (error) {
    console.log('⚠️ Puppeteer not available, using alternative test...');
    console.log('Please manually check:');
    console.log('1. Open http://localhost:3000 in browser');
    console.log('2. Check browser console (F12) for errors');
    console.log('3. Verify if ICU Dataset Application interface appears');
}
