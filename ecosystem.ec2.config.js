// PM2 Configuration for EC2 Production Deployment
// This file configures the ICU Dataset Application backend for production on EC2

module.exports = {
  apps: [
    {
      name: 'icu-backend-production',
      script: 'server/server.js',
      cwd: '/home/<USER>/icu-dataset-app',
      env: {
        NODE_ENV: 'production',
        PORT: 5000,
        // AWS credentials will be loaded from .env.production file
        AWS_ACCESS_KEY_ID: process.env.AWS_ACCESS_KEY_ID,
        AWS_SECRET_ACCESS_KEY: process.env.AWS_SECRET_ACCESS_KEY,
        AWS_REGION: process.env.AWS_REGION,
        AWS_S3_BUCKET: process.env.AWS_S3_BUCKET,
        REACT_APP_AWS_IDENTITY_POOL_ID: process.env.REACT_APP_AWS_IDENTITY_POOL_ID,
        REACT_APP_AWS_REGION: process.env.REACT_APP_AWS_REGION,
        REACT_APP_S3_BUCKET: process.env.REACT_APP_S3_BUCKET,
        ALLOWED_ORIGINS: process.env.ALLOWED_ORIGINS,
        REACT_APP_BACKEND_URL: process.env.REACT_APP_BACKEND_URL
      },
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: '1G',
      error_file: '/home/<USER>/logs/backend-error.log',
      out_file: '/home/<USER>/logs/backend-out.log',
      log_file: '/home/<USER>/logs/backend-combined.log',
      time: true,
      restart_delay: 1000,
      max_restarts: 10,
      min_uptime: '10s',
      
      // Additional production settings
      kill_timeout: 5000,
      listen_timeout: 3000,
      
      // Environment-specific settings
      env_production: {
        NODE_ENV: 'production',
        PORT: 5000,
        LOG_LEVEL: 'info'
      }
    }
  ],

  // Deployment configuration
  deploy: {
    production: {
      user: 'ubuntu',
      host: 'YOUR_EC2_PUBLIC_IP', // Replace with your EC2 public IP
      ref: 'origin/main',
      repo: 'YOUR_REPOSITORY_URL', // Replace with your repository URL
      path: '/home/<USER>/icu-dataset-app',
      'pre-deploy-local': '',
      'post-deploy': 'npm install && pm2 reload ecosystem.ec2.config.js --env production',
      'pre-setup': ''
    }
  }
};
