<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AWS S3 CORS Policy Update - Visual Guide</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 1000px; margin: 0 auto; padding: 20px; }
        .step { border: 2px solid #007bff; margin: 20px 0; padding: 20px; border-radius: 10px; }
        .step-header { background-color: #007bff; color: white; padding: 10px; margin: -20px -20px 20px -20px; border-radius: 8px 8px 0 0; }
        .step-number { background-color: white; color: #007bff; border-radius: 50%; width: 30px; height: 30px; display: inline-flex; align-items: center; justify-content: center; margin-right: 10px; font-weight: bold; }
        .code-block { background-color: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace; margin: 10px 0; border-left: 4px solid #007bff; }
        .warning { background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .success { background-color: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .screenshot-placeholder { background-color: #e9ecef; border: 2px dashed #6c757d; padding: 40px; text-align: center; margin: 15px 0; border-radius: 5px; }
        .button-example { background-color: #007bff; color: white; padding: 8px 16px; border: none; border-radius: 4px; font-weight: bold; }
        .aws-console-link { background-color: #ff9900; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; font-weight: bold; }
    </style>
</head>
<body>
    <h1>🚀 AWS S3 CORS Policy Update - Visual Guide</h1>
    <p><strong>Objective:</strong> Update CORS policy for bucket "icudatasetphrasesfortesting" to allow uploads from "icuphrasecollection.com"</p>

    <div class="step">
        <div class="step-header">
            <span class="step-number">1</span>
            <strong>Access AWS S3 Console</strong>
        </div>
        <p><strong>Action:</strong> Navigate to AWS S3 Console and locate your bucket</p>
        <ol>
            <li>Go to: <a href="https://s3.console.aws.amazon.com/" target="_blank" class="aws-console-link">AWS S3 Console</a></li>
            <li>Sign in with your AWS credentials</li>
            <li>Look for bucket named: <code>icudatasetphrasesfortesting</code></li>
            <li>Click on the bucket name to open it</li>
        </ol>
        <div class="screenshot-placeholder">
            📸 You should see a list of S3 buckets. Look for "icudatasetphrasesfortesting" and click on it.
        </div>
    </div>

    <div class="step">
        <div class="step-header">
            <span class="step-number">2</span>
            <strong>Navigate to Permissions Tab</strong>
        </div>
        <p><strong>Action:</strong> Find the CORS configuration section</p>
        <ol>
            <li>Inside your bucket, you'll see several tabs at the top</li>
            <li>Click on the <span class="button-example">Permissions</span> tab</li>
            <li>Scroll down to find "Cross-origin resource sharing (CORS)" section</li>
            <li>Click the <span class="button-example">Edit</span> button in the CORS section</li>
        </ol>
        <div class="screenshot-placeholder">
            📸 The Permissions tab contains various security settings. The CORS section is usually near the bottom.
        </div>
    </div>

    <div class="step">
        <div class="step-header">
            <span class="step-number">3</span>
            <strong>Update CORS Configuration</strong>
        </div>
        <p><strong>Action:</strong> Replace existing CORS policy with updated version</p>
        <div class="warning">
            ⚠️ <strong>Important:</strong> Copy the ENTIRE contents of s3-cors-policy.json file
        </div>
        <ol>
            <li>Clear any existing CORS configuration in the text editor</li>
            <li>Copy the complete contents from your <code>s3-cors-policy.json</code> file</li>
            <li>Paste into the CORS configuration editor</li>
            <li>Verify the JSON formatting looks correct</li>
        </ol>
        <div class="code-block">
The policy should include these domains:
- https://icuphrasecollection.com
- http://icuphrasecollection.com  
- https://www.icuphrasecollection.com
- http://www.icuphrasecollection.com
        </div>
    </div>

    <div class="step">
        <div class="step-header">
            <span class="step-number">4</span>
            <strong>Save Changes</strong>
        </div>
        <p><strong>Action:</strong> Apply the new CORS policy</p>
        <ol>
            <li>Review the CORS policy one more time</li>
            <li>Click <span class="button-example">Save changes</span> button</li>
            <li>Wait for the green success message</li>
            <li>Note: Changes may take 2-3 minutes to propagate globally</li>
        </ol>
        <div class="success">
            ✅ <strong>Success:</strong> You should see a confirmation message that CORS configuration has been updated.
        </div>
    </div>

    <div class="step">
        <div class="step-header">
            <span class="step-number">5</span>
            <strong>Test the Configuration</strong>
        </div>
        <p><strong>Action:</strong> Verify video uploads work from your production site</p>
        <ol>
            <li>Go to <a href="https://icuphrasecollection.com" target="_blank">https://icuphrasecollection.com</a></li>
            <li>Complete the consent and demographics forms</li>
            <li>Record a test video</li>
            <li>Verify the upload completes without errors</li>
            <li>Check your S3 bucket to confirm the video was uploaded</li>
        </ol>
        <div class="warning">
            ⚠️ <strong>If uploads still fail:</strong>
            <ul>
                <li>Wait 5-10 minutes for CORS changes to propagate</li>
                <li>Clear your browser cache completely</li>
                <li>Try using incognito/private browsing mode</li>
                <li>Check browser DevTools Console for specific error messages</li>
            </ul>
        </div>
    </div>

    <div class="step">
        <div class="step-header">
            <span class="step-number">✅</span>
            <strong>Verification Checklist</strong>
        </div>
        <p><strong>Your CORS update is successful when:</strong></p>
        <ul>
            <li>✅ Video uploads work from icuphrasecollection.com</li>
            <li>✅ No CORS errors appear in browser console</li>
            <li>✅ Videos appear in your S3 bucket after upload</li>
            <li>✅ Application functions normally on production domain</li>
            <li>✅ Both HTTP and HTTPS versions work (if applicable)</li>
        </ul>
    </div>

    <div style="background-color: #f8f9fa; padding: 20px; border-radius: 10px; margin-top: 30px;">
        <h3>📋 Quick Reference</h3>
        <p><strong>Bucket Name:</strong> <code>icudatasetphrasesfortesting</code></p>
        <p><strong>Production Domain:</strong> <code>icuphrasecollection.com</code></p>
        <p><strong>CORS File:</strong> <code>s3-cors-policy.json</code></p>
        <p><strong>AWS Console:</strong> <a href="https://s3.console.aws.amazon.com/" target="_blank">https://s3.console.aws.amazon.com/</a></p>
    </div>

    <script>
        // Add click handlers to copy code blocks
        document.querySelectorAll('.code-block').forEach(block => {
            block.style.cursor = 'pointer';
            block.title = 'Click to copy';
            block.addEventListener('click', function() {
                navigator.clipboard.writeText(this.textContent);
                const original = this.style.backgroundColor;
                this.style.backgroundColor = '#d4edda';
                setTimeout(() => {
                    this.style.backgroundColor = original;
                }, 1000);
            });
        });
    </script>
</body>
</html>
