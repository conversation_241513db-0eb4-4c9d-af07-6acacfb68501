# 🚀 Quick Deployment Guide - ICU Dataset Application

## ⚡ 5-Minute Deployment Checklist

### **✅ Prerequisites Complete**
- [x] EC2 server running at `*************:5000`
- [x] Security group configured (port 5000 open)
- [x] AWS credentials configured on EC2
- [x] CORS configured for Netlify domains
- [x] All dependencies installed

---

## 🌐 **Step 1: Netlify Deployment (2 minutes)**

### **1.1 Connect Repository**
1. Go to [Netlify Dashboard](https://app.netlify.com/)
2. Click "New site from Git"
3. Connect to GitHub repository: `ICU_dataset_application_21.6.25`
4. Set build settings:
   - **Build command**: `npm run build`
   - **Publish directory**: `build`

### **1.2 Environment Variables**
Add these **exact** variables in Netlify Dashboard → Site Settings → Environment Variables:

```
REACT_APP_AWS_IDENTITY_POOL_ID=ap-southeast-2:59baeaf9-a80c-4afc-8f27-f4d6191242dd
REACT_APP_AWS_REGION=ap-southeast-2
REACT_APP_S3_BUCKET=icudatasetphrasesfortesting
REACT_APP_BACKEND_URL=http://*************:5000
REACT_APP_NAME=ICU Dataset Application
REACT_APP_VERSION=1.0.0
NODE_ENV=production
REACT_APP_DEBUG=false
```

### **1.3 Deploy**
1. Click "Deploy site"
2. Wait for build to complete (~2-3 minutes)
3. Note your Netlify URL (e.g., `https://amazing-app-123456.netlify.app`)

---

## 🖥️ **Step 2: EC2 Server Status Check (1 minute)**

### **2.1 Verify Server Running**
```bash
curl http://*************:5000/health
```
**Expected Response**: `{"status":"healthy",...}`

### **2.2 If Server Not Running**
```bash
# SSH into EC2
ssh -i icu-dataset-key.pem ec2-user@*************

# Navigate to app directory
cd /home/<USER>/icu-app

# Start server
node server/server.js
```

---

## 🧪 **Step 3: Test Deployment (2 minutes)**

### **3.1 Open Your Netlify App**
1. Go to your Netlify URL
2. Complete consent form
3. Fill demographics
4. Try recording a video

### **3.2 Verify Backend Connectivity**
Open browser console and check for:
- ✅ No CORS errors
- ✅ Successful API calls to EC2 server
- ✅ Receipt generation working

### **3.3 Test Video Upload**
1. Record a 5-second video
2. Check for receipt number generation
3. Verify no upload errors in console

---

## 🔧 **Troubleshooting (If Needed)**

### **Common Issues & Quick Fixes**

#### **❌ CORS Error**
```
Access to fetch at 'http://*************:5000/...' from origin 'https://your-app.netlify.app' has been blocked by CORS policy
```
**Fix**: Add your Netlify domain to server CORS config and restart EC2 server.

#### **❌ Connection Refused**
```
Failed to fetch: TypeError: Failed to fetch
```
**Fix**: Check EC2 server is running: `curl http://*************:5000/health`

#### **❌ AWS Credential Error**
```
{"success":false,"error":"Could not load credentials from any providers"}
```
**Fix**: Restart EC2 server to reload environment variables.

#### **❌ Build Failed on Netlify**
```
Build failed: Command failed with exit code 1
```
**Fix**: Check all environment variables are set correctly in Netlify dashboard.

---

## 📋 **Post-Deployment Checklist**

### **✅ Functionality Tests**
- [ ] Consent page loads correctly
- [ ] Demographics form works
- [ ] Camera access granted
- [ ] Video recording works (5-second countdown)
- [ ] Receipt numbers generate sequentially
- [ ] Page refresh clears user data (privacy)
- [ ] Multiple users can use same device

### **✅ Performance Tests**
- [ ] Page loads in under 3 seconds
- [ ] Video upload completes successfully
- [ ] No console errors during normal use
- [ ] Mobile responsive design works

### **✅ Security Tests**
- [ ] HTTPS enabled (Netlify automatic)
- [ ] No sensitive data in browser storage
- [ ] CORS properly configured
- [ ] AWS credentials not exposed in frontend

---

## 🎯 **Success Criteria**

### **Application is Ready When:**
1. **✅ Netlify URL accessible** and loads consent page
2. **✅ Video recording works** with 5-second countdown
3. **✅ Receipt generation** produces sequential numbers
4. **✅ Data privacy** - page refresh clears all user data
5. **✅ No console errors** during normal operation
6. **✅ Mobile compatibility** - works on phones/tablets

---

## 📞 **Need Help?**

### **Quick Diagnostics**
1. **Test EC2 Server**: Open `test-ec2-server-endpoints.html` in browser
2. **Check Console**: F12 → Console tab for error messages
3. **Verify Environment**: Check Netlify environment variables match exactly

### **Emergency Contacts**
- **Server Issues**: Check EC2 server status and restart if needed
- **Deployment Issues**: Verify Netlify environment variables
- **CORS Issues**: Restart EC2 server after adding Netlify domain

---

## 🎉 **Deployment Complete!**

Your ICU Dataset Application is now live and ready for data collection!

**🔗 Your Application URLs:**
- **Frontend**: `https://your-app.netlify.app` (your Netlify URL)
- **Backend**: `http://*************:5000`
- **Health Check**: `http://*************:5000/health`

**📱 Ready for Multi-User Sessions:**
- Each page refresh provides a clean slate for new users
- Sequential receipt numbers track all recordings
- Complete privacy protection between users

---

**🏥 Supporting ICU Patient Care Through Technology**

*Your application is now contributing to medical research that could help save lives!*
