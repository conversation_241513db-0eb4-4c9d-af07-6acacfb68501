<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Backend Connectivity Test - Fixed</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background: #0056b3;
        }
        .console-output {
            background: #1e1e1e;
            color: #00ff00;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>🔧 Backend Connectivity Test - Fixed Configuration</h1>
    
    <div class="test-container">
        <h2>Current Server Status</h2>
        <div class="info">
            <strong>Frontend:</strong> http://localhost:3003 (React - Restarted with new env vars)<br>
            <strong>Backend:</strong> http://localhost:5001 (Node.js - Running)<br>
            <strong>Environment:</strong> REACT_APP_BACKEND_URL=http://localhost:5001
        </div>
        
        <button class="button" onclick="testConnectivity()">🧪 Test Backend Connectivity</button>
        <button class="button" onclick="testFromReactApp()">🔗 Test from React App Context</button>
        
        <div id="test-results"></div>
    </div>

    <div class="test-container">
        <h2>Test Results</h2>
        <div id="console-output" class="console-output">
            Click "Test Backend Connectivity" to start testing...
        </div>
    </div>

    <div class="test-container">
        <h2>Next Steps</h2>
        <div class="info">
            <strong>If tests pass:</strong>
            <ol>
                <li>Go to <a href="http://localhost:3003" target="_blank">http://localhost:3003</a></li>
                <li>Open Developer Tools (F12) → Console</li>
                <li>Follow the comprehensive testing guide</li>
                <li>Test the VideoRecorder recording functionality</li>
            </ol>
        </div>
        
        <div class="info">
            <strong>If tests still fail:</strong>
            <ol>
                <li>Check if there are CORS issues</li>
                <li>Verify both servers are running</li>
                <li>Try restarting both servers</li>
                <li>Check browser network tab for detailed error info</li>
            </ol>
        </div>
    </div>

    <script>
        function log(message) {
            const output = document.getElementById('console-output');
            output.innerHTML += message + '\n';
            output.scrollTop = output.scrollHeight;
        }

        function clearLog() {
            document.getElementById('console-output').innerHTML = '';
        }

        async function testConnectivity() {
            clearLog();
            log('🧪 Starting Backend Connectivity Test...');
            log('=' .repeat(50));
            
            const tests = [
                {
                    name: 'Direct Health Check',
                    url: 'http://localhost:5001/health',
                    method: 'GET'
                },
                {
                    name: 'CORS Preflight Test',
                    url: 'http://localhost:5001/upload',
                    method: 'OPTIONS'
                },
                {
                    name: 'Sample Counts API',
                    url: 'http://localhost:5001/api/sample-counts',
                    method: 'GET'
                }
            ];

            for (const test of tests) {
                try {
                    log(`\n🔍 Testing: ${test.name}`);
                    log(`   URL: ${test.url}`);
                    
                    const response = await fetch(test.url, {
                        method: test.method,
                        headers: {
                            'Content-Type': 'application/json',
                        }
                    });
                    
                    log(`   ✅ Status: ${response.status} ${response.statusText}`);
                    
                    if (response.ok) {
                        try {
                            const data = await response.json();
                            log(`   📄 Response: ${JSON.stringify(data, null, 2)}`);
                        } catch (e) {
                            log(`   📄 Response: (non-JSON response)`);
                        }
                    } else {
                        log(`   ❌ Error: ${response.status} ${response.statusText}`);
                    }
                    
                } catch (error) {
                    log(`   ❌ Network Error: ${error.message}`);
                    log(`   💡 This might be a CORS issue or server not running`);
                }
            }
            
            log('\n🏁 Backend connectivity test completed');
            log('\n📋 Summary:');
            log('   - If all tests show ✅, backend connectivity is working');
            log('   - If tests show ❌, there may be CORS or server issues');
            log('   - Proceed to React app testing if connectivity is confirmed');
        }

        async function testFromReactApp() {
            clearLog();
            log('🔗 Testing from React App Context...');
            log('Opening React app in new tab for testing...');
            
            // Open React app in new tab
            window.open('http://localhost:3003', '_blank');
            
            log('\n📋 Manual Test Instructions:');
            log('1. In the new React app tab, open Developer Tools (F12)');
            log('2. Go to Console tab');
            log('3. Run this command:');
            log('   fetch(process.env.REACT_APP_BACKEND_URL + "/health").then(r => r.json()).then(console.log)');
            log('4. Check if it returns backend health data');
            log('5. If successful, proceed with VideoRecorder testing');
        }

        // Auto-run test on page load
        window.addEventListener('load', () => {
            setTimeout(testConnectivity, 1000);
        });
    </script>
</body>
</html>
