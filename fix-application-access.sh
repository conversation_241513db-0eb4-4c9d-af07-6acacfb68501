#!/bin/bash

echo "🔧 ICU Dataset Application - Fix Access Issues"
echo "=============================================="

# Kill all existing React development servers
echo "🛑 Stopping all React development servers..."
pkill -f "react-scripts/scripts/start.js"
sleep 2

# Kill any processes on ports 3000-3003
echo "🛑 Freeing up ports 3000-3003..."
lsof -ti:3000,3001,3002,3003 | xargs kill -9 2>/dev/null || true
sleep 2

# Navigate to project directory
cd "/Users/<USER>/Desktop/ICU dataset application 21.6.25"

echo "🚀 Starting fresh React development server..."
npm start &

# Wait for server to start
echo "⏳ Waiting for server to start..."
sleep 10

# Check if server is running
if curl -s http://localhost:3000 > /dev/null 2>&1; then
    echo "✅ Server started successfully on http://localhost:3000"
    open http://localhost:3000
elif curl -s http://localhost:3001 > /dev/null 2>&1; then
    echo "✅ Server started successfully on http://localhost:3001"
    open http://localhost:3001
else
    echo "❌ Server failed to start. Please check the terminal output."
fi

echo ""
echo "📋 If the application still doesn't load:"
echo "1. Check browser console for JavaScript errors (F12)"
echo "2. Try incognito/private browsing mode"
echo "3. Clear browser cache and cookies"
echo "4. Try a different browser"
echo ""
echo "🔍 For debugging, check:"
echo "- Browser console (F12 → Console tab)"
echo "- Network tab for failed requests"
echo "- Terminal output for compilation errors"
