/**
 * Verification Script for Indigenous Ethnicity Category Update
 * 
 * This script verifies that the demographic form has been correctly updated
 * to show "Indigenous from overseas (i.e. Canadian Inuit)" as the ethnicity option.
 * 
 * Usage: Run this in the browser console on the demographic form page
 */

console.log('🧪 Starting Indigenous Ethnicity Category Verification...');

// Function to verify the ethnicity options
function verifyIndigenousEthnicityUpdate() {
    console.log('\n📋 Checking demographic form ethnicity options...');
    
    // Look for the ethnicity radio buttons
    const ethnicityRadios = document.querySelectorAll('input[name="ethnicity"]');
    
    if (ethnicityRadios.length === 0) {
        console.error('❌ No ethnicity radio buttons found. Make sure you are on the demographic form page (step 2).');
        return false;
    }
    
    console.log(`✅ Found ${ethnicityRadios.length} ethnicity options`);
    
    // Check for the indigenous option specifically
    let indigenousOption = null;
    let indigenousLabel = null;
    
    ethnicityRadios.forEach((radio, index) => {
        const label = radio.closest('label') || radio.parentElement.querySelector('label');
        const labelText = label ? label.textContent.trim() : 'No label found';
        
        console.log(`   ${index + 1}. Value: "${radio.value}" | Label: "${labelText}"`);
        
        if (radio.value === 'indigenous') {
            indigenousOption = radio;
            indigenousLabel = labelText;
        }
    });
    
    // Verify the indigenous option
    if (!indigenousOption) {
        console.error('❌ Indigenous option not found with value "indigenous"');
        return false;
    }
    
    console.log(`\n🎯 Indigenous option found:`);
    console.log(`   Value: "${indigenousOption.value}"`);
    console.log(`   Label: "${indigenousLabel}"`);
    
    // Check if the label matches the expected text
    const expectedLabel = 'Indigenous from overseas (i.e. Canadian Inuit)';
    
    if (indigenousLabel === expectedLabel) {
        console.log('✅ Indigenous label matches expected text exactly!');
        return true;
    } else {
        console.error(`❌ Indigenous label does not match expected text:`);
        console.error(`   Expected: "${expectedLabel}"`);
        console.error(`   Actual:   "${indigenousLabel}"`);
        return false;
    }
}

// Function to test selection and localStorage saving
function testIndigenousSelection() {
    console.log('\n🧪 Testing Indigenous option selection...');
    
    const indigenousRadio = document.querySelector('input[name="ethnicity"][value="indigenous"]');
    
    if (!indigenousRadio) {
        console.error('❌ Indigenous radio button not found');
        return false;
    }
    
    // Simulate selection
    indigenousRadio.click();
    
    // Check if it's selected
    if (indigenousRadio.checked) {
        console.log('✅ Indigenous option successfully selected');
        
        // Trigger change event to ensure React state updates
        const changeEvent = new Event('change', { bubbles: true });
        indigenousRadio.dispatchEvent(changeEvent);
        
        console.log('✅ Change event dispatched');
        return true;
    } else {
        console.error('❌ Indigenous option could not be selected');
        return false;
    }
}

// Function to check localStorage after form submission
function checkLocalStorageData() {
    console.log('\n💾 Checking localStorage data...');
    
    const demographicsData = localStorage.getItem('demographics');
    
    if (!demographicsData) {
        console.warn('⚠️ No demographics data found in localStorage yet');
        console.log('   This is normal if the form hasn\'t been submitted yet');
        return null;
    }
    
    try {
        const parsedData = JSON.parse(demographicsData);
        console.log('✅ Demographics data found in localStorage:');
        console.log('   ', parsedData);
        
        if (parsedData.ethnicity === 'indigenous') {
            console.log('✅ Ethnicity value correctly saved as "indigenous"');
            return true;
        } else {
            console.error(`❌ Unexpected ethnicity value: "${parsedData.ethnicity}"`);
            return false;
        }
    } catch (error) {
        console.error('❌ Error parsing demographics data from localStorage:', error);
        return false;
    }
}

// Main verification function
function runFullVerification() {
    console.log('🚀 Running full Indigenous ethnicity verification...\n');
    
    const results = {
        labelCheck: verifyIndigenousEthnicityUpdate(),
        selectionTest: testIndigenousSelection(),
        localStorageCheck: checkLocalStorageData()
    };
    
    console.log('\n📊 Verification Results:');
    console.log(`   Label Display: ${results.labelCheck ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`   Selection Test: ${results.selectionTest ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`   localStorage: ${results.localStorageCheck === true ? '✅ PASS' : results.localStorageCheck === false ? '❌ FAIL' : '⚠️ PENDING'}`);
    
    const overallSuccess = results.labelCheck && results.selectionTest;
    
    console.log(`\n🎯 Overall Status: ${overallSuccess ? '✅ SUCCESS' : '❌ NEEDS ATTENTION'}`);
    
    if (overallSuccess) {
        console.log('\n🎉 Indigenous ethnicity category update verification completed successfully!');
        console.log('   The demographic form correctly displays "Indigenous from overseas (i.e. Canadian Inuit)"');
        console.log('   and the selection functionality works as expected.');
    } else {
        console.log('\n⚠️ Some verification checks failed. Please review the results above.');
    }
    
    return overallSuccess;
}

// Instructions for manual testing
console.log('\n📖 Manual Testing Instructions:');
console.log('1. Navigate to the ICU dataset application');
console.log('2. Complete the consent process');
console.log('3. Go through the demographic form to step 2 (Ethnic Background)');
console.log('4. Run: runFullVerification()');
console.log('5. Optionally test selection: testIndigenousSelection()');
console.log('6. After form submission, check: checkLocalStorageData()');

// Export functions for manual use
window.verifyIndigenousEthnicityUpdate = verifyIndigenousEthnicityUpdate;
window.testIndigenousSelection = testIndigenousSelection;
window.checkLocalStorageData = checkLocalStorageData;
window.runFullVerification = runFullVerification;

console.log('\n✨ Verification script loaded! Use runFullVerification() to start testing.');
