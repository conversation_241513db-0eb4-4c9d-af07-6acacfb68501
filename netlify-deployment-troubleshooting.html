<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ICU Dataset Application - Netlify Deployment Troubleshooting</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 1200px; margin: 0 auto; padding: 20px; }
        .test-section { border: 1px solid #ddd; margin: 20px 0; padding: 20px; border-radius: 8px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button { padding: 10px 20px; margin: 10px 5px; border: none; border-radius: 4px; cursor: pointer; }
        .btn-primary { background-color: #007bff; color: white; }
        .btn-success { background-color: #28a745; color: white; }
        .btn-warning { background-color: #ffc107; color: black; }
        .btn-danger { background-color: #dc3545; color: white; }
        pre { background-color: #f8f9fa; padding: 15px; border-radius: 4px; overflow-x: auto; }
        .status-indicator { display: inline-block; width: 12px; height: 12px; border-radius: 50%; margin-right: 8px; }
        .status-success { background-color: #28a745; }
        .status-error { background-color: #dc3545; }
        .status-warning { background-color: #ffc107; }
        .status-info { background-color: #17a2b8; }
    </style>
</head>
<body>
    <h1>🚀 ICU Dataset Application - Netlify Deployment Troubleshooting</h1>
    
    <div class="test-section info">
        <h2>📋 Deployment Checklist</h2>
        <p>Follow these steps to resolve the "network connection error" in your Netlify deployment:</p>
        <ol>
            <li><strong>Backend Server Status:</strong> ✅ Running on localhost:5000</li>
            <li><strong>AWS S3 Connectivity:</strong> ✅ Working (5 files in bucket)</li>
            <li><strong>Environment Configuration:</strong> ⚠️ Needs Netlify setup</li>
            <li><strong>Upload Mode:</strong> 🔄 Fixed to support both frontend and backend</li>
        </ol>
    </div>

    <div class="test-section">
        <h2>🔧 1. Environment Configuration Test</h2>
        <button class="btn-primary" onclick="testEnvironmentConfig()">Test Environment Variables</button>
        <div id="env-test-result"></div>
    </div>

    <div class="test-section">
        <h2>🌐 2. Backend Connectivity Test</h2>
        <button class="btn-primary" onclick="testBackendConnectivity()">Test Backend Connection</button>
        <div id="backend-test-result"></div>
    </div>

    <div class="test-section">
        <h2>☁️ 3. AWS S3 Direct Upload Test</h2>
        <button class="btn-primary" onclick="testAWSConfiguration()">Test AWS Configuration</button>
        <div id="aws-test-result"></div>
    </div>

    <div class="test-section">
        <h2>📤 4. Video Upload Pipeline Test</h2>
        <button class="btn-primary" onclick="testVideoUpload()">Test Video Upload</button>
        <div id="upload-test-result"></div>
    </div>

    <div class="test-section warning">
        <h2>⚙️ Netlify Environment Variables Setup</h2>
        <p>Add these environment variables in your Netlify dashboard:</p>
        <pre>
REACT_APP_AWS_IDENTITY_POOL_ID=ap-southeast-2:59baeaf9-a80c-4afc-8f27-f4d6191242dd
REACT_APP_AWS_REGION=ap-southeast-2
REACT_APP_S3_BUCKET=icudatasetphrasesfortesting
REACT_APP_NAME=ICU Dataset Application
REACT_APP_VERSION=1.0.0
NODE_ENV=production
REACT_APP_DEBUG=false
        </pre>
        <p><strong>Steps:</strong></p>
        <ol>
            <li>Go to Netlify site dashboard</li>
            <li>Click "Site settings" → "Environment variables"</li>
            <li>Add each variable above</li>
            <li>Redeploy your site</li>
        </ol>
    </div>

    <div class="test-section success">
        <h2>✅ Local Backend Server Status</h2>
        <p><span class="status-indicator status-success"></span>Backend server is running on localhost:5000</p>
        <p><span class="status-indicator status-success"></span>AWS S3 connectivity verified</p>
        <p><span class="status-indicator status-success"></span>Upload endpoint operational</p>
    </div>

    <script>
        function testEnvironmentConfig() {
            const resultDiv = document.getElementById('env-test-result');
            resultDiv.innerHTML = '<p>Testing environment configuration...</p>';
            
            const envVars = {
                'AWS Identity Pool': process.env.REACT_APP_AWS_IDENTITY_POOL_ID,
                'AWS Region': process.env.REACT_APP_AWS_REGION,
                'S3 Bucket': process.env.REACT_APP_S3_BUCKET,
                'Backend URL': process.env.REACT_APP_BACKEND_URL,
                'Debug Mode': process.env.REACT_APP_DEBUG,
                'Node Environment': process.env.NODE_ENV
            };
            
            let html = '<h4>Environment Variables:</h4><ul>';
            let allConfigured = true;
            
            for (const [key, value] of Object.entries(envVars)) {
                const status = value ? 'success' : 'error';
                const indicator = value ? 'status-success' : 'status-error';
                html += `<li><span class="status-indicator ${indicator}"></span>${key}: ${value || 'NOT SET'}</li>`;
                if (!value && key !== 'Backend URL') allConfigured = false;
            }
            
            html += '</ul>';
            
            if (allConfigured) {
                html += '<p class="success">✅ Environment configuration looks good for frontend-only mode!</p>';
            } else {
                html += '<p class="error">❌ Missing required environment variables. Add them in Netlify dashboard.</p>';
            }
            
            resultDiv.innerHTML = html;
        }
        
        async function testBackendConnectivity() {
            const resultDiv = document.getElementById('backend-test-result');
            resultDiv.innerHTML = '<p>Testing backend connectivity...</p>';
            
            try {
                const backendUrl = process.env.REACT_APP_BACKEND_URL || 'http://localhost:5000';
                const response = await fetch(`${backendUrl}/health`);
                const data = await response.json();
                
                resultDiv.innerHTML = `
                    <h4>✅ Backend Connection Successful</h4>
                    <p><strong>URL:</strong> ${backendUrl}</p>
                    <p><strong>Status:</strong> ${data.status}</p>
                    <p><strong>AWS:</strong> ${data.services.aws}</p>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <h4>❌ Backend Connection Failed</h4>
                    <p><strong>Error:</strong> ${error.message}</p>
                    <p><strong>Note:</strong> This is expected on Netlify. The app will use frontend-only mode.</p>
                `;
            }
        }
        
        function testAWSConfiguration() {
            const resultDiv = document.getElementById('aws-test-result');
            
            const hasIdentityPool = !!process.env.REACT_APP_AWS_IDENTITY_POOL_ID;
            const hasRegion = !!process.env.REACT_APP_AWS_REGION;
            const hasBucket = !!process.env.REACT_APP_S3_BUCKET;
            
            const isConfigured = hasIdentityPool && hasRegion && hasBucket;
            
            if (isConfigured) {
                resultDiv.innerHTML = `
                    <h4>✅ AWS Configuration Valid</h4>
                    <p>Frontend direct upload mode will be used.</p>
                    <ul>
                        <li><span class="status-indicator status-success"></span>Identity Pool: ${process.env.REACT_APP_AWS_IDENTITY_POOL_ID}</li>
                        <li><span class="status-indicator status-success"></span>Region: ${process.env.REACT_APP_AWS_REGION}</li>
                        <li><span class="status-indicator status-success"></span>Bucket: ${process.env.REACT_APP_S3_BUCKET}</li>
                    </ul>
                `;
            } else {
                resultDiv.innerHTML = `
                    <h4>⚠️ AWS Configuration Incomplete</h4>
                    <p>Backend upload mode will be attempted.</p>
                    <ul>
                        <li><span class="status-indicator ${hasIdentityPool ? 'status-success' : 'status-error'}"></span>Identity Pool: ${hasIdentityPool ? 'Configured' : 'Missing'}</li>
                        <li><span class="status-indicator ${hasRegion ? 'status-success' : 'status-error'}"></span>Region: ${hasRegion ? 'Configured' : 'Missing'}</li>
                        <li><span class="status-indicator ${hasBucket ? 'status-success' : 'status-error'}"></span>Bucket: ${hasBucket ? 'Configured' : 'Missing'}</li>
                    </ul>
                `;
            }
        }
        
        async function testVideoUpload() {
            const resultDiv = document.getElementById('upload-test-result');
            resultDiv.innerHTML = '<p>Testing video upload pipeline...</p>';
            
            try {
                // Create a mock video blob
                const mockVideoData = new Uint8Array(1024);
                const mockBlob = new Blob([mockVideoData], { type: 'video/webm' });
                
                // Test the upload logic (this will show which path is taken)
                console.log('🧪 Testing upload pipeline...');
                
                resultDiv.innerHTML = `
                    <h4>📤 Upload Pipeline Test</h4>
                    <p>Check browser console for detailed upload flow.</p>
                    <p><strong>Mock video created:</strong> 1KB WebM blob</p>
                    <p><strong>Upload mode:</strong> Will be determined by AWS configuration</p>
                    <p><strong>Next step:</strong> Try recording a real video in the application</p>
                `;
                
            } catch (error) {
                resultDiv.innerHTML = `
                    <h4>❌ Upload Test Failed</h4>
                    <p><strong>Error:</strong> ${error.message}</p>
                `;
            }
        }
        
        // Auto-run environment test on page load
        window.onload = function() {
            testEnvironmentConfig();
        };
    </script>
</body>
</html>
