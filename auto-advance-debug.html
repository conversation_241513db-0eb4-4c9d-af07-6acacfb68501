<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Auto-Advance Debug Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-section {
            border: 1px solid #ddd;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        .log-output {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .status {
            font-weight: bold;
            padding: 5px 10px;
            border-radius: 3px;
            display: inline-block;
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Auto-Advance Debug Test</h1>
        <p>This page helps debug the auto-advance functionality in the ICU Dataset Application.</p>
        
        <div class="test-section info">
            <h3>📋 Test Instructions</h3>
            <ol>
                <li>Open the main application at <a href="http://localhost:3000" target="_blank">http://localhost:3000</a></li>
                <li>Complete the consent and demographics forms</li>
                <li>Select 2-3 phrases for recording</li>
                <li>Record 3 videos for the first phrase</li>
                <li>Check if auto-advance to the next phrase occurs</li>
                <li>Monitor the console logs below</li>
            </ol>
        </div>

        <div class="test-section">
            <h3>🔍 Console Log Monitor</h3>
            <button onclick="startMonitoring()">Start Monitoring Console</button>
            <button onclick="clearLogs()">Clear Logs</button>
            <button onclick="checkAutoAdvanceState()">Check Auto-Advance State</button>
            <div id="logOutput" class="log-output">Console logs will appear here...</div>
        </div>

        <div class="test-section">
            <h3>📊 Current State</h3>
            <div id="stateOutput" class="log-output">State information will appear here...</div>
        </div>

        <div class="test-section">
            <h3>🎯 Expected Behavior</h3>
            <ul>
                <li>After 3 recordings for a phrase, auto-advance should trigger</li>
                <li>Console should show "🔄 AUTO-ADVANCE CHECK" logs</li>
                <li>Console should show "🎯 AUTO-ADVANCE: Phrase completion detected"</li>
                <li>Console should show "📝 AUTO-ADVANCE: Moving to next phrase"</li>
                <li>The UI should automatically switch to the next phrase</li>
            </ul>
        </div>
    </div>

    <script>
        let logBuffer = [];
        let originalConsoleLog = console.log;
        let originalConsoleError = console.error;
        let originalConsoleWarn = console.warn;

        function startMonitoring() {
            // Override console methods to capture logs
            console.log = function(...args) {
                originalConsoleLog.apply(console, args);
                logBuffer.push(`[LOG] ${new Date().toLocaleTimeString()}: ${args.join(' ')}`);
                updateLogDisplay();
            };

            console.error = function(...args) {
                originalConsoleError.apply(console, args);
                logBuffer.push(`[ERROR] ${new Date().toLocaleTimeString()}: ${args.join(' ')}`);
                updateLogDisplay();
            };

            console.warn = function(...args) {
                originalConsoleWarn.apply(console, args);
                logBuffer.push(`[WARN] ${new Date().toLocaleTimeString()}: ${args.join(' ')}`);
                updateLogDisplay();
            };

            document.getElementById('logOutput').textContent = 'Console monitoring started...\n';
        }

        function updateLogDisplay() {
            const logOutput = document.getElementById('logOutput');
            // Only show auto-advance related logs
            const relevantLogs = logBuffer.filter(log => 
                log.includes('AUTO-ADVANCE') || 
                log.includes('RECORDING COMPLETED') ||
                log.includes('recordingCompleted') ||
                log.includes('useEffect')
            );
            logOutput.textContent = relevantLogs.slice(-50).join('\n'); // Show last 50 relevant logs
            logOutput.scrollTop = logOutput.scrollHeight;
        }

        function clearLogs() {
            logBuffer = [];
            document.getElementById('logOutput').textContent = 'Logs cleared...\n';
        }

        function checkAutoAdvanceState() {
            // Try to access the React app state through the window object
            const stateOutput = document.getElementById('stateOutput');
            
            try {
                // Check if we can access React DevTools or any exposed state
                const reactRoot = document.querySelector('#root');
                if (reactRoot && reactRoot._reactInternalFiber) {
                    stateOutput.textContent = 'React state access detected, but requires DevTools for detailed inspection.';
                } else {
                    stateOutput.textContent = `
Current URL: ${window.location.href}
Local Storage Keys: ${Object.keys(localStorage).join(', ')}
Session Storage Keys: ${Object.keys(sessionStorage).join(', ')}

Recording Counts from localStorage:
${localStorage.getItem('icuAppRecordingsCount') || 'No recording counts found'}

Session Data:
${localStorage.getItem('icuAppSessionData') || 'No session data found'}
                    `;
                }
            } catch (error) {
                stateOutput.textContent = `Error accessing state: ${error.message}`;
            }
        }

        // Auto-start monitoring
        window.addEventListener('load', () => {
            setTimeout(startMonitoring, 1000);
        });
    </script>
</body>
</html>
