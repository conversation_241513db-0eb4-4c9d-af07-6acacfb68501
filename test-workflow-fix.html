<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ICU Dataset Application - Workflow Fix Test</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        h1 {
            color: #009688;
            text-align: center;
            margin-bottom: 30px;
        }
        h2 {
            color: #00796b;
            border-bottom: 2px solid #e0f2f1;
            padding-bottom: 10px;
        }
        .button-group {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        button {
            background: #009688;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: background-color 0.3s;
        }
        button:hover {
            background: #00796b;
        }
        button.danger {
            background: #f44336;
        }
        button.danger:hover {
            background: #d32f2f;
        }
        button.success {
            background: #4caf50;
        }
        button.success:hover {
            background: #388e3c;
        }
        .output {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.4;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
            margin-top: 10px;
        }
        .test-step {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
        }
        .test-step h3 {
            margin: 0 0 10px 0;
            color: #1976d2;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-good { background: #4caf50; }
        .status-warning { background: #ff9800; }
        .status-error { background: #f44336; }
    </style>
</head>
<body>
    <h1>🔧 ICU Dataset Application - Workflow Fix Test</h1>
    
    <div class="container">
        <h2>🧪 Test Preparation</h2>
        <div class="button-group">
            <button onclick="clearAllData()" class="danger">🗑️ Clear All Data</button>
            <button onclick="checkCurrentState()">📊 Check Current State</button>
            <button onclick="openApp()" class="success">🚀 Open App (Port 3003)</button>
        </div>
        <div id="prep-output" class="output"></div>
    </div>

    <div class="container">
        <h2>📋 Test Steps</h2>
        
        <div class="test-step">
            <h3>Step 1: Clear Data & Start Fresh</h3>
            <p>Click "Clear All Data" above, then "Open App" to start with a clean state.</p>
        </div>
        
        <div class="test-step">
            <h3>Step 2: Complete Initial Flow</h3>
            <p>Go through: Consent → Demographics → Training → Phrase Selection</p>
            <p><strong>Expected:</strong> Should reach phrase selection page without issues</p>
        </div>
        
        <div class="test-step">
            <h3>Step 3: Select Phrases</h3>
            <p>Select 1-3 phrases from any category and click "Start Recording"</p>
            <p><strong>Expected:</strong> Should navigate to recording page, NOT completion page</p>
        </div>
        
        <div class="test-step">
            <h3>Step 4: Verify Recording Interface</h3>
            <p>Check that you see: Camera viewport, phrase text, recording controls</p>
            <p><strong>Expected:</strong> Full recording interface visible, no completion message</p>
        </div>
        
        <div class="test-step">
            <h3>Step 5: Make Test Recording</h3>
            <p>Record a video to test the workflow progression</p>
            <p><strong>Expected:</strong> Recording works normally, auto-advance after 3 recordings</p>
        </div>
    </div>

    <div class="container">
        <h2>🔍 Debug Information</h2>
        <div class="button-group">
            <button onclick="checkWorkflowState()">🔄 Check Workflow State</button>
            <button onclick="monitorConsole()">📝 Monitor Console</button>
            <button onclick="inspectProviderState()">🏗️ Inspect Provider State</button>
        </div>
        <div id="debug-output" class="output"></div>
    </div>

    <div class="container">
        <h2>✅ Test Results</h2>
        <div class="button-group">
            <button onclick="recordTestResult('pass')" class="success">✅ Test PASSED</button>
            <button onclick="recordTestResult('fail')" class="danger">❌ Test FAILED</button>
            <button onclick="recordTestResult('partial')">⚠️ Partial Success</button>
        </div>
        <div id="results-output" class="output"></div>
    </div>

    <script>
        function log(outputId, message) {
            const output = document.getElementById(outputId);
            output.textContent += message + '\n';
        }

        function clearLog(outputId) {
            document.getElementById(outputId).textContent = '';
        }

        function clearAllData() {
            clearLog('prep-output');
            log('prep-output', '🗑️ Clearing all application data for clean test...\n');
            
            const keysToRemove = [
                'icuAppRecordingsCount',
                'icu_selected_phrases',
                'icuAppDemographics',
                'icuAppSelectedPhrases',
                'icuAppCompletedPhrases',
                'icuAppConsent',
                'testing_mode',
                'mock_recordings'
            ];
            
            keysToRemove.forEach(key => {
                if (localStorage.getItem(key)) {
                    localStorage.removeItem(key);
                    log('prep-output', `✅ Removed: ${key}`);
                }
            });
            
            sessionStorage.clear();
            log('prep-output', '✅ Cleared sessionStorage');
            
            log('prep-output', '\n🎉 All data cleared! Ready for clean test.');
            log('prep-output', 'Click "Open App" to start testing.');
        }

        function checkCurrentState() {
            clearLog('prep-output');
            log('prep-output', '📊 Checking current application state...\n');
            
            // Check localStorage
            const recordingCounts = localStorage.getItem('icuAppRecordingsCount');
            const selectedPhrases = localStorage.getItem('icu_selected_phrases');
            const demographics = localStorage.getItem('icuAppDemographics');
            
            log('prep-output', '📱 localStorage Status:');
            log('prep-output', `  Recording counts: ${recordingCounts ? 'EXISTS' : 'CLEAN'}`);
            log('prep-output', `  Selected phrases: ${selectedPhrases ? 'EXISTS' : 'CLEAN'}`);
            log('prep-output', `  Demographics: ${demographics ? 'EXISTS' : 'CLEAN'}`);
            
            // Check sessionStorage
            const sessionActive = sessionStorage.getItem('icuAppSessionActive');
            log('prep-output', `  Session active: ${sessionActive || 'NO'}`);
            
            if (!recordingCounts && !selectedPhrases && !demographics) {
                log('prep-output', '\n✅ State is CLEAN - ready for testing');
            } else {
                log('prep-output', '\n⚠️ State has existing data - may affect test results');
                log('prep-output', 'Recommend clearing data before testing');
            }
        }

        function openApp() {
            log('prep-output', '\n🚀 Opening application...');
            window.open('http://localhost:3003', '_blank');
        }

        function checkWorkflowState() {
            clearLog('debug-output');
            log('debug-output', '🔄 Checking workflow state...\n');
            
            // Simulate the workflow conditions
            const hasConsent = true; // Assume we're past consent
            const demographicsCompleted = !!localStorage.getItem('icuAppDemographics');
            const selectedPhrases = localStorage.getItem('icu_selected_phrases');
            const phrasesSelected = !!selectedPhrases;
            
            log('debug-output', '📋 Workflow Conditions:');
            log('debug-output', `  hasConsent: ${hasConsent}`);
            log('debug-output', `  demographicsCompleted: ${demographicsCompleted}`);
            log('debug-output', `  phrasesSelected: ${phrasesSelected}`);
            
            if (!hasConsent) {
                log('debug-output', '\n➡️ Expected: Consent page');
            } else if (!demographicsCompleted) {
                log('debug-output', '\n➡️ Expected: Demographics form');
            } else if (!phrasesSelected) {
                log('debug-output', '\n➡️ Expected: Phrase selection');
            } else {
                log('debug-output', '\n➡️ Expected: Recording interface');
                log('debug-output', '🎯 THIS IS THE CRITICAL TEST POINT');
                log('debug-output', 'If you see completion page instead, the bug is still present');
            }
        }

        function monitorConsole() {
            clearLog('debug-output');
            log('debug-output', '📝 Console monitoring instructions:\n');
            log('debug-output', '1. Open browser DevTools (F12)');
            log('debug-output', '2. Go to Console tab');
            log('debug-output', '3. Look for these key messages:');
            log('debug-output', '   🔧 INITIALIZATION TIMEOUT: About to complete initialization');
            log('debug-output', '   🎯 SET SELECTED PHRASES: Setting phrases and resetting completion state');
            log('debug-output', '   ✅ Phrases set, completion prompt reset, ready for recording');
            log('debug-output', '   🔍 RECORDING SESSION MANAGER RENDER');
            log('debug-output', '\n4. If you see "showCompletionPrompt: true", that indicates the bug');
            log('debug-output', '5. The fix should show "showCompletionPrompt: false" when entering recording');
        }

        function recordTestResult(result) {
            clearLog('results-output');
            const timestamp = new Date().toLocaleString();
            
            log('results-output', `🧪 Test Result: ${result.toUpperCase()}\n`);
            log('results-output', `📅 Timestamp: ${timestamp}\n`);
            
            if (result === 'pass') {
                log('results-output', '✅ SUCCESS: Workflow bypass issue has been FIXED!');
                log('results-output', '✅ Users can now properly access the recording interface');
                log('results-output', '✅ No premature completion page display');
            } else if (result === 'fail') {
                log('results-output', '❌ FAILURE: Workflow bypass issue still exists');
                log('results-output', '❌ App still skips to completion page');
                log('results-output', '❌ Further debugging required');
            } else if (result === 'partial') {
                log('results-output', '⚠️ PARTIAL: Some improvement but issues remain');
                log('results-output', '⚠️ May work in some cases but not others');
                log('results-output', '⚠️ Additional fixes needed');
            }
            
            log('results-output', '\n📋 Next Steps:');
            if (result === 'pass') {
                log('results-output', '1. Test with multiple phrase selections');
                log('results-output', '2. Test auto-advance functionality');
                log('results-output', '3. Test session persistence');
            } else {
                log('results-output', '1. Check console logs for error messages');
                log('results-output', '2. Verify state management in providers');
                log('results-output', '3. Review conditional rendering logic');
            }
        }

        // Initialize on page load
        window.addEventListener('load', () => {
            checkCurrentState();
        });
    </script>
</body>
</html>
