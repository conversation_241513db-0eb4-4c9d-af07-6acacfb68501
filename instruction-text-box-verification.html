<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Instruction Text Box Verification - ICU Dataset Application</title>
    <style>
        body {
            font-family: '<PERSON><PERSON>', <PERSON>l, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            line-height: 1.6;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 3px solid #2196F3;
        }
        .header h1 {
            color: #1976d2;
            margin: 0;
            font-size: 2.5rem;
            font-weight: bold;
        }
        .header p {
            color: #666;
            margin: 10px 0 0 0;
            font-size: 1.1rem;
        }
        .verification-section {
            margin: 30px 0;
            padding: 25px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 5px solid #2196F3;
        }
        .verification-section h2 {
            color: #1976d2;
            margin-top: 0;
            font-size: 1.5rem;
        }
        .checklist {
            list-style: none;
            padding: 0;
        }
        .checklist li {
            margin: 15px 0;
            padding: 12px;
            background: white;
            border-radius: 6px;
            border: 1px solid #e0e0e0;
            display: flex;
            align-items: center;
        }
        .checklist li::before {
            content: "✓";
            color: #4caf50;
            font-weight: bold;
            font-size: 1.2rem;
            margin-right: 12px;
            width: 20px;
            text-align: center;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .feature-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            border: 2px solid #2196F3;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .feature-card h3 {
            color: #1976d2;
            margin-top: 0;
            font-size: 1.3rem;
        }
        .feature-card p {
            color: #555;
            margin-bottom: 0;
        }
        .instruction-preview {
            background: rgba(255, 255, 255, 0.95);
            border: 2px solid #2196F3;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 4px 16px rgba(33, 150, 243, 0.2);
        }
        .instruction-preview h4 {
            color: #1976d2;
            font-weight: bold;
            margin: 0 0 15px 0;
            text-align: center;
            font-size: 1.25rem;
        }
        .instruction-step {
            display: flex;
            align-items: flex-start;
            margin: 12px 0;
        }
        .instruction-step .number {
            color: #1976d2;
            font-weight: bold;
            margin-right: 8px;
            min-width: 20px;
        }
        .instruction-step .text {
            color: #333;
            line-height: 1.4;
        }
        .tip {
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid rgba(33, 150, 243, 0.2);
            text-align: center;
            color: #666;
            font-style: italic;
            font-size: 0.9rem;
        }
        .access-links {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin: 30px 0;
            flex-wrap: wrap;
        }
        .access-links a {
            background: #2196F3;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 6px;
            font-weight: bold;
            transition: background 0.3s ease;
        }
        .access-links a:hover {
            background: #1976d2;
        }
        .responsive-note {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 8px;
            border-left: 5px solid #2196F3;
            margin: 20px 0;
        }
        .responsive-note h3 {
            color: #1976d2;
            margin-top: 0;
        }
        @media (max-width: 768px) {
            .container {
                padding: 20px;
                margin: 10px;
            }
            .header h1 {
                font-size: 2rem;
            }
            .access-links {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📋 Instruction Text Box Verification</h1>
            <p>ICU Dataset Application - VideoRecorder Component Enhancement</p>
        </div>

        <div class="verification-section">
            <h2>✅ Implementation Verification Checklist</h2>
            <ul class="checklist">
                <li>Instruction text box positioned to the left of oval camera viewport</li>
                <li>Clear, readable typography matching application design</li>
                <li>Responsive layout that works on different screen sizes</li>
                <li>Text box remains visible throughout recording process</li>
                <li>Consistent styling with existing UI components</li>
                <li>No overlap with countdown timer, record button, or progress indicators</li>
                <li>All existing functionality preserved (auto-advance, AWS S3, phrase progression)</li>
            </ul>
        </div>

        <div class="feature-grid">
            <div class="feature-card">
                <h3>📱 Responsive Design</h3>
                <p>On desktop: Instruction box appears to the left of the oval viewport. On mobile: Instruction box appears below the oval viewport for optimal space usage.</p>
            </div>
            <div class="feature-card">
                <h3>🎨 Visual Design</h3>
                <p>Semi-transparent white background with blue border matching the application's color scheme. Clear typography with proper spacing and visual hierarchy.</p>
            </div>
            <div class="feature-card">
                <h3>📝 Content Structure</h3>
                <p>Step-by-step instructions with numbered points, clear action items, and a helpful positioning tip at the bottom.</p>
            </div>
            <div class="feature-card">
                <h3>⚙️ Functionality</h3>
                <p>Text box remains visible during recording, doesn't interfere with camera feed, and maintains all existing auto-advance and upload features.</p>
            </div>
        </div>

        <div class="instruction-preview">
            <h4>Recording Instructions</h4>
            <div class="instruction-step">
                <span class="number">1.</span>
                <span class="text">Press "Start Recording" and silently mouth the word shown in the oval one time</span>
            </div>
            <div class="instruction-step">
                <span class="number">2.</span>
                <span class="text">The recording will automatically stop after 5 seconds</span>
            </div>
            <div class="instruction-step">
                <span class="number">3.</span>
                <span class="text">The next word will load automatically – repeat until you're done</span>
            </div>
            <div class="tip">
                💡 Position your mouth in the centre of the oval for best results
            </div>
        </div>

        <div class="responsive-note">
            <h3>📱 Responsive Behavior</h3>
            <p><strong>Desktop/Tablet (md and up):</strong> Instruction box appears to the left of the oval viewport with proper spacing.</p>
            <p><strong>Mobile (xs/sm):</strong> Instruction box appears below the oval viewport to optimize screen space usage.</p>
            <p>The layout automatically adapts using CSS flexbox with responsive breakpoints.</p>
        </div>

        <div class="verification-section">
            <h2>🧪 Testing Instructions</h2>
            <ol style="padding-left: 20px;">
                <li><strong>Navigate to the application:</strong> Go to the phrase selection page and select phrases to record</li>
                <li><strong>Access VideoRecorder:</strong> Click "Start Recording" to access the VideoRecorder component</li>
                <li><strong>Verify layout:</strong> Confirm the instruction text box appears to the left of the oval viewport (desktop) or below it (mobile)</li>
                <li><strong>Check responsiveness:</strong> Resize the browser window to test different screen sizes</li>
                <li><strong>Test functionality:</strong> Ensure all recording features work normally with the new instruction box</li>
                <li><strong>Verify visibility:</strong> Confirm the instruction box remains visible during recording</li>
            </ol>
        </div>

        <div class="access-links">
            <a href="http://localhost:3000" target="_blank">🚀 Open Application</a>
            <a href="http://localhost:3000" target="_blank">📝 Test Recording</a>
            <a href="#" onclick="window.location.reload()">🔄 Refresh Verification</a>
        </div>

        <div style="text-align: center; margin-top: 40px; padding-top: 20px; border-top: 2px solid #e0e0e0; color: #666;">
            <p><strong>ICU Dataset Application</strong> - VideoRecorder Component Enhancement</p>
            <p>Instruction text box successfully implemented with responsive design and preserved functionality</p>
        </div>
    </div>
</body>
</html>
