/**
 * Debug S3 Phrase Matching
 * This script helps identify mismatches between S3 phrase keys and application phrase names
 */

console.log('🔍 === S3 PHRASE MATCHING DEBUG ===');

// Function to normalize phrase key (same as in s3ProgressService.js)
function normalizePhraseKey(phrase) {
  return phrase
    .toLowerCase()
    .replace(/[^a-z0-9\s]/g, '') // Remove special characters except spaces
    .replace(/\s+/g, '_') // Replace spaces with underscores
    .trim();
}

// Function to find phrase count with variations (same as in s3ProgressService.js)
function findPhraseCount(byPhraseData, originalPhrase, normalizedKey) {
  if (!byPhraseData) return 0;

  // Try exact match first
  if (byPhraseData[originalPhrase] !== undefined) {
    console.log(`✅ Exact match found: "${originalPhrase}" = ${byPhraseData[originalPhrase]}`);
    return byPhraseData[originalPhrase];
  }

  // Try normalized key
  if (byPhraseData[normalizedKey] !== undefined) {
    console.log(`✅ Normalized match found: "${normalizedKey}" = ${byPhraseData[normalizedKey]}`);
    return byPhraseData[normalizedKey];
  }

  // Try common variations
  const variations = [
    originalPhrase.toLowerCase(),
    originalPhrase.replace(/\s+/g, '_'),
    originalPhrase.replace(/[^a-zA-Z0-9\s]/g, '').replace(/\s+/g, '_'),
    normalizedKey.replace(/_/g, ' '),
    normalizedKey.replace(/_/g, '')
  ];

  for (const variation of variations) {
    if (byPhraseData[variation] !== undefined) {
      console.log(`✅ Variation match found: "${variation}" = ${byPhraseData[variation]}`);
      return byPhraseData[variation];
    }
  }

  console.log(`❌ No match found for phrase: "${originalPhrase}" (normalized: "${normalizedKey}")`);
  return 0;
}

// Test function to run in browser console
async function debugS3PhraseMatching() {
  console.log('🔄 Fetching S3 sample counts...');
  
  try {
    const response = await fetch('http://localhost:5000/api/sample-counts');
    const data = await response.json();
    
    if (!data.success) {
      console.error('❌ Failed to fetch S3 data:', data.error);
      return;
    }
    
    console.log('✅ S3 data fetched successfully');
    console.log('📊 Total recordings:', data.counts.total);
    console.log('📝 Phrases in S3:', Object.keys(data.counts.byPhrase).length);
    
    // Show all S3 phrase keys
    console.log('\n🗂️ === S3 PHRASE KEYS ===');
    Object.entries(data.counts.byPhrase)
      .sort(([,a], [,b]) => b - a) // Sort by count descending
      .forEach(([phrase, count]) => {
        console.log(`  "${phrase}": ${count}`);
      });
    
    // Test some common phrases that should be in the app
    console.log('\n🧪 === TESTING PHRASE MATCHING ===');
    const testPhrases = [
      'Call the doctor',
      'Call the nurse', 
      'I need water',
      'I need food',
      'I am in pain',
      'Thank you',
      'Please',
      'Zero',
      'One',
      'Two',
      'Three',
      'Eight',
      'Seven',
      'Six',
      'Ten'
    ];
    
    testPhrases.forEach(phrase => {
      const normalizedKey = normalizePhraseKey(phrase);
      const count = findPhraseCount(data.counts.byPhrase, phrase, normalizedKey);
      console.log(`\n🔍 Testing phrase: "${phrase}"`);
      console.log(`   Normalized: "${normalizedKey}"`);
      console.log(`   Count found: ${count}`);
    });
    
    // Check for potential mismatches
    console.log('\n⚠️ === POTENTIAL ISSUES ===');
    const s3Keys = Object.keys(data.counts.byPhrase);
    const unmatchedKeys = s3Keys.filter(key => {
      // Check if this S3 key might not match any of our test phrases
      const isTestKey = key.includes('test') || key.includes('debug') || key.includes('verification');
      return !isTestKey && !testPhrases.some(phrase => {
        const normalized = normalizePhraseKey(phrase);
        return key === phrase || key === normalized || 
               key.toLowerCase() === phrase.toLowerCase() ||
               key.replace(/_/g, ' ') === phrase.toLowerCase();
      });
    });
    
    if (unmatchedKeys.length > 0) {
      console.log('🔍 S3 keys that might not match app phrases:');
      unmatchedKeys.forEach(key => {
        console.log(`   "${key}": ${data.counts.byPhrase[key]}`);
      });
    } else {
      console.log('✅ All non-test S3 keys appear to have potential matches');
    }
    
    return data;
    
  } catch (error) {
    console.error('❌ Error debugging S3 phrase matching:', error);
  }
}

// Make function available globally
window.debugS3PhraseMatching = debugS3PhraseMatching;

console.log('🚀 Debug script loaded. Run window.debugS3PhraseMatching() in browser console to test.');
