<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 Debug Auto-Advancement Issue</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .debug-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .issue-header {
            background: #dc3545;
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .debug-output {
            background: #1e1e1e;
            color: #00ff00;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            margin: 10px 0;
            max-height: 300px;
            overflow-y: auto;
        }
        .button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .button.danger { background: #dc3545; }
        .button.success { background: #28a745; }
        .button.warning { background: #ffc107; color: #212529; }
        .step {
            background: #e9ecef;
            padding: 15px;
            margin: 10px 0;
            border-left: 4px solid #007bff;
            border-radius: 4px;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="issue-header">
        <h1>🔍 Auto-Advancement Issue Debugger</h1>
        <p><strong>Problem:</strong> Recording page is being bypassed, jumping directly to completion page</p>
        <p><strong>Likely Cause:</strong> False positive recording counts or testing mode interference</p>
    </div>

    <div class="debug-container">
        <h2>🧪 Step 1: Check Current Application State</h2>
        <button class="button" onclick="checkApplicationState()">🔍 Check App State</button>
        <button class="button warning" onclick="checkTestingMode()">🧪 Check Testing Mode</button>
        <button class="button danger" onclick="clearAllData()">🗑️ Clear All Data</button>
        <div id="app-state-output" class="debug-output">Click "Check App State" to see current application state...</div>
    </div>

    <div class="debug-container">
        <h2>📊 Step 2: Analyze localStorage Data</h2>
        <button class="button" onclick="analyzeLocalStorage()">📊 Analyze localStorage</button>
        <button class="button" onclick="showRecordingCounts()">📈 Show Recording Counts</button>
        <div id="localstorage-output" class="debug-output">Click "Analyze localStorage" to see stored data...</div>
    </div>

    <div class="debug-container">
        <h2>🔄 Step 3: Test Auto-Advancement Logic</h2>
        <button class="button" onclick="testAutoAdvancement()">🔄 Test Auto-Advancement</button>
        <button class="button" onclick="simulateRecordingCompletion()">🎬 Simulate Recording</button>
        <div id="auto-advance-output" class="debug-output">Click "Test Auto-Advancement" to check logic...</div>
    </div>

    <div class="debug-container">
        <h2>🛠️ Step 4: Fix Actions</h2>
        <div class="step">
            <h3>Quick Fixes:</h3>
            <button class="button success" onclick="resetToFreshState()">🔄 Reset to Fresh State</button>
            <button class="button warning" onclick="disableTestingMode()">🚫 Disable Testing Mode</button>
            <button class="button" onclick="setCorrectRecordingCounts()">📊 Set Correct Counts</button>
        </div>
        <div id="fix-output" class="debug-output">Fix actions will show results here...</div>
    </div>

    <div class="debug-container">
        <h2>🎯 Step 5: Test Recording Flow</h2>
        <div class="warning">
            <strong>After applying fixes:</strong>
            <ol>
                <li>Open the application: <a href="http://localhost:3003" target="_blank">http://localhost:3003</a></li>
                <li>Complete demographics and phrase selection</li>
                <li>Navigate to recording page</li>
                <li>Verify recording interface appears and stays visible</li>
                <li>Test actual recording functionality</li>
            </ol>
        </div>
        <button class="button success" onclick="openApplication()">🚀 Open Application for Testing</button>
    </div>

    <script>
        function log(elementId, message) {
            const element = document.getElementById(elementId);
            element.innerHTML += message + '\n';
            element.scrollTop = element.scrollHeight;
        }

        function clearLog(elementId) {
            document.getElementById(elementId).innerHTML = '';
        }

        function checkApplicationState() {
            clearLog('app-state-output');
            log('app-state-output', '🔍 Checking current application state...');
            log('app-state-output', '=' .repeat(50));
            
            // Check URL parameters
            const urlParams = new URLSearchParams(window.location.search);
            log('app-state-output', '📍 URL Parameters:');
            for (const [key, value] of urlParams.entries()) {
                log('app-state-output', `  ${key}: ${value}`);
            }
            
            // Check if testing mode is active
            const testingMode = window.location.search.includes('testing=true');
            log('app-state-output', `🧪 Testing Mode Active: ${testingMode}`);
            
            // Check localStorage keys
            log('app-state-output', '\n📱 localStorage Keys:');
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                log('app-state-output', `  ${key}`);
            }
            
            // Check sessionStorage
            log('app-state-output', '\n🔄 sessionStorage Keys:');
            for (let i = 0; i < sessionStorage.length; i++) {
                const key = sessionStorage.key(i);
                log('app-state-output', `  ${key}`);
            }
        }

        function checkTestingMode() {
            clearLog('app-state-output');
            log('app-state-output', '🧪 Checking for testing mode interference...');
            log('app-state-output', '=' .repeat(50));
            
            // Check URL for testing parameters
            const url = window.location.href;
            const hasTestingParam = url.includes('testing=true');
            log('app-state-output', `🔍 Current URL: ${url}`);
            log('app-state-output', `🧪 Has testing=true parameter: ${hasTestingParam}`);
            
            // Check for testing mode in localStorage
            const testingData = localStorage.getItem('testing_mode');
            log('app-state-output', `📱 localStorage testing_mode: ${testingData}`);
            
            // Check for mock data
            const mockData = localStorage.getItem('mock_recordings');
            log('app-state-output', `🎭 Mock recordings data: ${mockData ? 'Present' : 'Not found'}`);
            
            if (hasTestingParam || testingData || mockData) {
                log('app-state-output', '\n⚠️ TESTING MODE DETECTED!');
                log('app-state-output', 'This may be causing auto-advancement issues.');
                log('app-state-output', 'Use "Disable Testing Mode" to fix.');
            } else {
                log('app-state-output', '\n✅ No testing mode interference detected.');
            }
        }

        function analyzeLocalStorage() {
            clearLog('localstorage-output');
            log('localstorage-output', '📊 Analyzing localStorage data...');
            log('localstorage-output', '=' .repeat(50));
            
            // Check recording counts
            const recordingCounts = localStorage.getItem('icuAppRecordingsCount');
            log('localstorage-output', '📈 Recording Counts:');
            if (recordingCounts) {
                try {
                    const counts = JSON.parse(recordingCounts);
                    log('localstorage-output', JSON.stringify(counts, null, 2));
                    
                    // Analyze counts
                    let totalRecordings = 0;
                    let completedPhrases = 0;
                    for (const [phraseKey, count] of Object.entries(counts)) {
                        totalRecordings += count;
                        if (count >= 3) completedPhrases++;
                        log('localstorage-output', `  ${phraseKey}: ${count} recordings ${count >= 3 ? '(COMPLETE)' : ''}`);
                    }
                    log('localstorage-output', `\n📊 Summary: ${totalRecordings} total recordings, ${completedPhrases} completed phrases`);
                } catch (e) {
                    log('localstorage-output', `❌ Error parsing recording counts: ${e.message}`);
                }
            } else {
                log('localstorage-output', '  No recording counts found');
            }
            
            // Check selected phrases
            const selectedPhrases = localStorage.getItem('icu_selected_phrases');
            log('localstorage-output', '\n📝 Selected Phrases:');
            if (selectedPhrases) {
                try {
                    const phrases = JSON.parse(selectedPhrases);
                    log('localstorage-output', `  Count: ${phrases.length}`);
                    phrases.forEach((phrase, index) => {
                        log('localstorage-output', `  ${index + 1}. ${phrase.phrase} (${phrase.category})`);
                    });
                } catch (e) {
                    log('localstorage-output', `❌ Error parsing selected phrases: ${e.message}`);
                }
            } else {
                log('localstorage-output', '  No selected phrases found');
            }
        }

        function showRecordingCounts() {
            clearLog('localstorage-output');
            log('localstorage-output', '📈 Detailed Recording Counts Analysis...');
            log('localstorage-output', '=' .repeat(50));
            
            const recordingCounts = localStorage.getItem('icuAppRecordingsCount');
            const selectedPhrases = localStorage.getItem('icu_selected_phrases');
            
            if (!recordingCounts || !selectedPhrases) {
                log('localstorage-output', '❌ Missing required data for analysis');
                return;
            }
            
            try {
                const counts = JSON.parse(recordingCounts);
                const phrases = JSON.parse(selectedPhrases);
                
                log('localstorage-output', '📊 Phrase-by-Phrase Analysis:');
                phrases.forEach((phrase, index) => {
                    const phraseKey = `${phrase.category}:${phrase.phrase}`;
                    const count = counts[phraseKey] || 0;
                    const status = count >= 3 ? 'COMPLETE ✅' : `${count}/3 recordings`;
                    log('localstorage-output', `  ${index + 1}. "${phrase.phrase}" - ${status}`);
                    
                    if (count >= 3) {
                        log('localstorage-output', `      ⚠️ This phrase is marked complete - may trigger auto-advancement!`);
                    }
                });
                
                // Check if first phrase is already complete
                if (phrases.length > 0) {
                    const firstPhraseKey = `${phrases[0].category}:${phrases[0].phrase}`;
                    const firstPhraseCount = counts[firstPhraseKey] || 0;
                    if (firstPhraseCount >= 3) {
                        log('localstorage-output', '\n🚨 ISSUE FOUND: First phrase is already marked complete!');
                        log('localstorage-output', 'This will cause immediate auto-advancement, skipping recording page.');
                    }
                }
                
            } catch (e) {
                log('localstorage-output', `❌ Error analyzing data: ${e.message}`);
            }
        }

        function clearAllData() {
            clearLog('fix-output');
            log('fix-output', '🗑️ Clearing all application data...');
            
            // Clear localStorage
            const keysToRemove = [
                'icuAppRecordingsCount',
                'icu_selected_phrases',
                'icu_demographics',
                'testing_mode',
                'mock_recordings'
            ];
            
            keysToRemove.forEach(key => {
                if (localStorage.getItem(key)) {
                    localStorage.removeItem(key);
                    log('fix-output', `✅ Removed: ${key}`);
                }
            });
            
            // Clear sessionStorage
            sessionStorage.clear();
            log('fix-output', '✅ Cleared sessionStorage');
            
            log('fix-output', '\n🎉 All data cleared! Application should start fresh.');
            log('fix-output', 'Refresh the page and try again.');
        }

        function resetToFreshState() {
            clearLog('fix-output');
            log('fix-output', '🔄 Resetting to fresh state...');
            
            // Clear all data first
            clearAllData();
            
            // Set fresh session marker
            sessionStorage.setItem('icuAppSessionActive', 'true');
            log('fix-output', '✅ Set fresh session marker');
            
            log('fix-output', '\n🎉 Reset complete! Ready for fresh recording session.');
        }

        function disableTestingMode() {
            clearLog('fix-output');
            log('fix-output', '🚫 Disabling testing mode...');
            
            // Remove testing parameters from URL
            const url = new URL(window.location);
            url.searchParams.delete('testing');
            window.history.replaceState({}, document.title, url);
            log('fix-output', '✅ Removed testing parameter from URL');
            
            // Remove testing data from localStorage
            localStorage.removeItem('testing_mode');
            localStorage.removeItem('mock_recordings');
            log('fix-output', '✅ Removed testing data from localStorage');
            
            log('fix-output', '\n🎉 Testing mode disabled!');
        }

        function setCorrectRecordingCounts() {
            clearLog('fix-output');
            log('fix-output', '📊 Setting correct recording counts...');
            
            // Reset all recording counts to 0
            localStorage.setItem('icuAppRecordingsCount', '{}');
            log('fix-output', '✅ Reset all recording counts to 0');
            
            log('fix-output', '\n🎉 Recording counts corrected!');
        }

        function openApplication() {
            window.open('http://localhost:3003', '_blank');
            log('fix-output', '🚀 Application opened in new tab');
            log('fix-output', 'Test the recording flow to verify the fix worked.');
        }

        // Auto-run initial checks
        window.addEventListener('load', () => {
            setTimeout(() => {
                checkApplicationState();
                analyzeLocalStorage();
            }, 500);
        });
    </script>
</body>
</html>
