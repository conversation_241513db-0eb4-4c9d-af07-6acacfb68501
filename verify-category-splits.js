// Verification script for category splits
const fs = require('fs');

// Read the updated phrases.js file
const phrasesContent = fs.readFileSync('src/phrases.js', 'utf8');

// Extract the phrases object (simple regex approach for verification)
const phrasesMatch = phrasesContent.match(/export const phrases = ({[\s\S]*?});/);
if (!phrasesMatch) {
  console.error('Could not extract phrases object');
  process.exit(1);
}

// Evaluate the phrases object (in a real app, you'd import it properly)
const phrasesObjectString = phrasesMatch[1];
const phrases = eval(`(${phrasesObjectString})`);

console.log('=== CATEGORY SPLIT VERIFICATION ===\n');

// Original data for comparison
const originalICUCoreWords = [
  "doctor", "nurse", "help", "pain", "water", "drink", "food", "toilet", "move", "sit", "lie", "rest", "blanket", "pillow", "glasses", "hearing aids", "phone", "charger", "music", "news", "TV", "lights", "family", "wife", "husband", "son", "daughter", "question", "medication", "cough", "suction", "head", "neck", "face", "eyes", "arms", "legs", "stomach", "feet", "chest", "hands", "headache", "hot", "cold"
];

const originalPhysicalDiscomfort = [
  "My back hurts.", "My chest hurts.", "My neck hurts.", "My stomach hurts.", "I feel sick.", "I feel dizzy.", "I feel numb.", "I'm cold.", "I'm hot.", "I'm tired.", "I'm confused.", "I'm itchy.", "I'm uncomfortable.", "I feel bloated.", "I have a headache.", "I need my teeth cleaned.", "I'm in pain."
];

// Verify ICU core words split
console.log('🔍 ICU CORE WORDS VERIFICATION:');
const icuPart1 = phrases["ICU core words Part 1"] || [];
const icuPart2 = phrases["ICU core words Part 2"] || [];
const icuPart3 = phrases["ICU core words Part 3"] || [];
const combinedICU = [...icuPart1, ...icuPart2, ...icuPart3];

console.log(`Part 1: ${icuPart1.length} items`);
console.log(`Part 2: ${icuPart2.length} items`);
console.log(`Part 3: ${icuPart3.length} items`);
console.log(`Total combined: ${combinedICU.length} items`);
console.log(`Original total: ${originalICUCoreWords.length} items`);

// Check if all original phrases are preserved
const missingICU = originalICUCoreWords.filter(phrase => !combinedICU.includes(phrase));
const extraICU = combinedICU.filter(phrase => !originalICUCoreWords.includes(phrase));

if (missingICU.length === 0 && extraICU.length === 0 && combinedICU.length === originalICUCoreWords.length) {
  console.log('✅ ICU core words: All phrases preserved correctly');
} else {
  console.log('❌ ICU core words: Issues found');
  if (missingICU.length > 0) console.log('  Missing:', missingICU);
  if (extraICU.length > 0) console.log('  Extra:', extraICU);
}

// Verify Physical Discomfort split
console.log('\n🔍 PHYSICAL DISCOMFORT VERIFICATION:');
const physicalPart1 = phrases["Physical Discomfort Part 1"] || [];
const physicalPart2 = phrases["Physical Discomfort Part 2"] || [];
const combinedPhysical = [...physicalPart1, ...physicalPart2];

console.log(`Part 1: ${physicalPart1.length} items`);
console.log(`Part 2: ${physicalPart2.length} items`);
console.log(`Total combined: ${combinedPhysical.length} items`);
console.log(`Original total: ${originalPhysicalDiscomfort.length} items`);

// Check if all original phrases are preserved
const missingPhysical = originalPhysicalDiscomfort.filter(phrase => !combinedPhysical.includes(phrase));
const extraPhysical = combinedPhysical.filter(phrase => !originalPhysicalDiscomfort.includes(phrase));

if (missingPhysical.length === 0 && extraPhysical.length === 0 && combinedPhysical.length === originalPhysicalDiscomfort.length) {
  console.log('✅ Physical Discomfort: All phrases preserved correctly');
} else {
  console.log('❌ Physical Discomfort: Issues found');
  if (missingPhysical.length > 0) console.log('  Missing:', missingPhysical);
  if (extraPhysical.length > 0) console.log('  Extra:', extraPhysical);
}

// Overall category audit
console.log('\n📊 UPDATED CATEGORY AUDIT:');
let totalCategories = 0;
let totalPhrases = 0;
let maxCategorySize = 0;
let oversizedCategories = [];

Object.entries(phrases).forEach(([category, phraseList]) => {
  const count = phraseList.length;
  totalCategories++;
  totalPhrases += count;
  maxCategorySize = Math.max(maxCategorySize, count);
  
  if (count > 15) {
    oversizedCategories.push({ category, count });
  }
  
  console.log(`${category}: ${count} items ${count <= 15 ? '✅' : '⚠️'}`);
});

console.log('\n=== SUMMARY ===');
console.log(`Total categories: ${totalCategories} (was 17, now ${totalCategories})`);
console.log(`Total phrases: ${totalPhrases} (should be 173)`);
console.log(`Largest category: ${maxCategorySize} items`);
console.log(`Categories over 15 items: ${oversizedCategories.length}`);

if (oversizedCategories.length === 0) {
  console.log('✅ SUCCESS: All categories now have ≤15 items');
} else {
  console.log('❌ ISSUE: Some categories still exceed 15 items:');
  oversizedCategories.forEach(cat => console.log(`  - ${cat.category}: ${cat.count} items`));
}

console.log('\n🎯 TASK 2 STATUS:');
if (totalPhrases === 173 && oversizedCategories.length === 0 && missingICU.length === 0 && missingPhysical.length === 0) {
  console.log('✅ TASK 2 COMPLETED SUCCESSFULLY');
  console.log('  - All phrases preserved');
  console.log('  - All categories ≤15 items');
  console.log('  - ICU core words remains prioritized (Parts 1-3 first)');
} else {
  console.log('❌ TASK 2 NEEDS ATTENTION');
}
