<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ICU Dataset Application - Browser Diagnostic</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .status.success {
            background: #d5f4e6;
            color: #27ae60;
            border: 1px solid #27ae60;
        }
        .status.error {
            background: #fadbd8;
            color: #e74c3c;
            border: 1px solid #e74c3c;
        }
        .status.info {
            background: #d6eaf8;
            color: #2980b9;
            border: 1px solid #2980b9;
        }
        .status.warning {
            background: #fef9e7;
            color: #f39c12;
            border: 1px solid #f39c12;
        }
        .button {
            background: #3498db;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .button:hover {
            background: #2980b9;
        }
        .button.success {
            background: #27ae60;
        }
        .button.danger {
            background: #e74c3c;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #f9f9f9;
        }
        .code-block {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        #results {
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 ICU Dataset Application - Browser Diagnostic</h1>
        
        <div class="test-section">
            <h2>🌐 Application Access Test</h2>
            <p>This diagnostic will help identify why the ICU dataset application might not be loading properly.</p>
            
            <button class="button" onclick="testApplicationAccess()">Test Application Access</button>
            <button class="button" onclick="testBackendConnectivity()">Test Backend Connectivity</button>
            <button class="button success" onclick="openApplication()">Open Application</button>
            <button class="button danger" onclick="clearBrowserData()">Clear Browser Data</button>
            
            <div id="results"></div>
        </div>

        <div class="test-section">
            <h2>📋 Manual Diagnostic Steps</h2>
            <ol>
                <li><strong>Check Browser Console:</strong> Press F12 and look for JavaScript errors in the Console tab</li>
                <li><strong>Check Network Tab:</strong> Look for failed requests (red entries) in the Network tab</li>
                <li><strong>Try Incognito Mode:</strong> Open the application in an incognito/private window</li>
                <li><strong>Clear Cache:</strong> Hard refresh with Ctrl+Shift+R (or Cmd+Shift+R on Mac)</li>
                <li><strong>Check Different Browser:</strong> Try Chrome, Firefox, Safari, or Edge</li>
            </ol>
        </div>

        <div class="test-section">
            <h2>🔧 Common Issues & Solutions</h2>
            <ul>
                <li><strong>Blank Page:</strong> Usually JavaScript errors - check console</li>
                <li><strong>Loading Forever:</strong> Network connectivity issues - check backend</li>
                <li><strong>CORS Errors:</strong> Backend CORS configuration - restart servers</li>
                <li><strong>404 Errors:</strong> React router issues - refresh page</li>
                <li><strong>Cache Issues:</strong> Old cached files - clear browser cache</li>
            </ul>
        </div>
    </div>

    <script>
        function addResult(message, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.innerHTML = message;
            results.appendChild(div);
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        async function testApplicationAccess() {
            clearResults();
            addResult('🔍 Testing application access...', 'info');

            try {
                // Test if the main page loads
                const response = await fetch('http://localhost:3001');
                if (response.ok) {
                    addResult('✅ Main page accessible (HTTP ' + response.status + ')', 'success');
                    
                    // Test if JavaScript bundle loads
                    const jsResponse = await fetch('http://localhost:3001/static/js/bundle.js');
                    if (jsResponse.ok) {
                        addResult('✅ JavaScript bundle accessible (' + (jsResponse.headers.get('content-length') / 1024 / 1024).toFixed(1) + 'MB)', 'success');
                    } else {
                        addResult('❌ JavaScript bundle failed to load (HTTP ' + jsResponse.status + ')', 'error');
                    }
                } else {
                    addResult('❌ Main page not accessible (HTTP ' + response.status + ')', 'error');
                }
            } catch (error) {
                addResult('❌ Network error: ' + error.message, 'error');
            }
        }

        async function testBackendConnectivity() {
            addResult('🔍 Testing backend connectivity...', 'info');

            try {
                const response = await fetch('http://localhost:5000/health');
                if (response.ok) {
                    const data = await response.json();
                    addResult('✅ Backend healthy: ' + data.status + ' (uptime: ' + Math.round(data.uptime) + 's)', 'success');
                } else {
                    addResult('❌ Backend health check failed (HTTP ' + response.status + ')', 'error');
                }
            } catch (error) {
                addResult('❌ Backend connection error: ' + error.message, 'error');
            }
        }

        function openApplication() {
            addResult('🚀 Opening application in new tab...', 'info');
            window.open('http://localhost:3001', '_blank');
        }

        function clearBrowserData() {
            addResult('🧹 Clearing browser data...', 'warning');
            
            // Clear localStorage
            try {
                localStorage.clear();
                addResult('✅ localStorage cleared', 'success');
            } catch (e) {
                addResult('⚠️ Could not clear localStorage: ' + e.message, 'warning');
            }

            // Clear sessionStorage
            try {
                sessionStorage.clear();
                addResult('✅ sessionStorage cleared', 'success');
            } catch (e) {
                addResult('⚠️ Could not clear sessionStorage: ' + e.message, 'warning');
            }

            addResult('💡 Recommendation: Hard refresh the application page (Ctrl+Shift+R)', 'info');
        }

        // Auto-run basic tests on page load
        window.addEventListener('load', function() {
            setTimeout(() => {
                testApplicationAccess();
                setTimeout(() => {
                    testBackendConnectivity();
                }, 1000);
            }, 500);
        });

        // Check for console errors
        window.addEventListener('error', function(e) {
            addResult('❌ JavaScript Error: ' + e.message + ' (Line: ' + e.lineno + ')', 'error');
        });

        // Check for unhandled promise rejections
        window.addEventListener('unhandledrejection', function(e) {
            addResult('❌ Unhandled Promise Rejection: ' + e.reason, 'error');
        });
    </script>
</body>
</html>
