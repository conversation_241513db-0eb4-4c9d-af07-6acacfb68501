# 🏥 ICU Dataset Application

A React-based web application for collecting video recordings of speech phrases to support ICU patient communication research. The application enables users to record themselves speaking various phrases, with automatic upload to AWS S3 and comprehensive receipt tracking.

## 🎯 Purpose

This application is designed to collect speech data that could potentially help save ICU patients' lives by improving communication systems in critical care environments. The collected data supports research into speech recognition and communication assistance technologies for ICU settings.

## 🏗️ Architecture

### **Frontend (Netlify)**
- **Framework**: React 18
- **UI Library**: Material-UI (MUI)
- **Deployment**: Netlify
- **Storage**: AWS S3 (direct upload)

### **Backend (EC2)**
- **Runtime**: Node.js 18
- **Framework**: Express.js
- **Server**: AWS EC2 (*************:5000)
- **Storage**: AWS S3 integration

### **Data Storage**
- **Videos**: AWS S3 bucket `icudatasetphrasesfortesting`
- **Receipts**: AWS S3 bucket `receipt-numbers`
- **Session Data**: localStorage (privacy-compliant)

## 🚀 Quick Start

### **Access the Application**
- **Production URL**: [Your Netlify URL]
- **Local Development**: `http://localhost:3000`

### **For Users**
1. Open the application in a web browser
2. Complete the consent form
3. Fill in demographic information
4. Select phrases to record
5. Record videos using the built-in camera interface
6. Receive receipt numbers for completed recordings

## 🛠️ Development Setup

### **Prerequisites**
- Node.js 18 or higher
- npm or yarn package manager
- AWS account with S3 access
- EC2 instance (for backend)

### **Local Development**
```bash
# Clone the repository
git clone https://github.com/jasonhall1985/ICU_dataset_application_21.6.25.git
cd ICU_dataset_application_21.6.25

# Install dependencies
npm install

# Set up environment variables
cp .env.example .env
# Edit .env with your AWS credentials

# Start development servers
npm run dev:both  # Starts both frontend and backend
# OR
npm start         # Frontend only (port 3000)
npm run server    # Backend only (port 5000)
```

### **Environment Variables**
```bash
# Frontend (.env)
REACT_APP_AWS_IDENTITY_POOL_ID=your-cognito-identity-pool-id
REACT_APP_AWS_REGION=ap-southeast-2
REACT_APP_S3_BUCKET=icudatasetphrasesfortesting
REACT_APP_BACKEND_URL=http://localhost:5000

# Backend (.env)
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=ap-southeast-2
AWS_S3_BUCKET=icudatasetphrasesfortesting
PORT=5000
```

## 🌐 Deployment

### **Netlify Frontend Deployment**

1. **Connect Repository**
   - Link your GitHub repository to Netlify
   - Set build command: `npm run build`
   - Set publish directory: `build`

2. **Environment Variables**
   ```
   REACT_APP_AWS_IDENTITY_POOL_ID=ap-southeast-2:59baeaf9-a80c-4afc-8f27-f4d6191242dd
   REACT_APP_AWS_REGION=ap-southeast-2
   REACT_APP_S3_BUCKET=icudatasetphrasesfortesting
   REACT_APP_BACKEND_URL=http://*************:5000
   NODE_ENV=production
   ```

3. **Deploy**
   - Push to main branch triggers automatic deployment

### **EC2 Backend Deployment**

1. **Server Setup**
   ```bash
   # SSH into EC2 instance
   ssh -i your-key.pem ec2-user@*************
   
   # Install Node.js 18
   curl -fsSL https://rpm.nodesource.com/setup_18.x | sudo bash -
   sudo yum install -y nodejs
   
   # Clone and setup application
   git clone https://github.com/jasonhall1985/ICU_dataset_application_21.6.25.git
   cd ICU_dataset_application_21.6.25
   npm install
   ```

2. **Configure Environment**
   ```bash
   # Create .env file with AWS credentials
   nano .env
   
   # Start server
   node server/server.js
   ```

3. **Security Group Configuration**
   - **Type**: Custom TCP
   - **Port**: 5000
   - **Source**: 0.0.0.0/0 (Anywhere)

## 🎥 Key Features

### **Video Recording System**
- **Camera Integration**: WebRTC-based video capture
- **Privacy Protection**: Mouth-region-only recording (excludes eyes/upper face)
- **LipNet Compatibility**: 150×75 pixel mouth ROI, 25fps, H.264 codec
- **Quality Controls**: 5-second recordings with countdown timer
- **Real-time Preview**: Oval viewport with lip positioning guide

### **Receipt System**
- **Sequential Numbering**: 6-digit format (000001, 000002, etc.)
- **AWS S3 Storage**: Primary storage with localStorage fallback
- **Video Mapping**: Links receipt numbers to recorded videos
- **Audit Trail**: Complete session tracking and logging

### **Privacy & Data Management**
- **Complete Data Reset**: Automatic on page refresh for multi-user sessions
- **Session Isolation**: No data persistence between different users
- **Receipt Preservation**: Counter persists for sequential numbering
- **GDPR Compliance**: User-controlled data collection

### **Progress Tracking**
- **Real-time Updates**: Live progress indicators
- **Category Management**: Organized phrase collections
- **Completion Status**: Visual feedback for recorded phrases
- **Session Summary**: Comprehensive recording statistics

## 📱 User Interface

### **Responsive Design**
- **Desktop**: Optimized for laptop/desktop recording sessions
- **Mobile**: Responsive layout for mobile devices
- **Accessibility**: Screen reader compatible, keyboard navigation

### **Recording Interface**
- **Oval Viewport**: Centered camera view with face framing
- **Instruction Sidebar**: Step-by-step recording guidance
- **Progress Indicators**: Real-time recording status
- **Zoom Controls**: Camera zoom adjustment
- **Countdown Timer**: 5-second recording countdown

## 🔧 Technical Specifications

### **Video Processing**
- **Format**: MP4 with H.264 codec
- **Resolution**: 400×200 pixels (mouth region)
- **Frame Rate**: 25fps
- **Duration**: 5 seconds per recording
- **Audio**: Removed for privacy compliance

### **Data Storage Structure**
```
S3 Bucket Structure:
icu-videos/
├── [AGE_GROUP]/
│   ├── [GENDER]/
│   │   ├── [ETHNICITY]/
│   │   │   ├── [PHRASE_LABEL]/
│   │   │   │   └── [FILENAME].mp4

receipt-numbers/
├── receipt-counter.json
├── receipt-mapping-000001.json
└── receipt-mapping-000002.json
```

### **API Endpoints**
- `GET /health` - Server health check
- `POST /upload` - Video upload endpoint
- `POST /api/receipt/generate` - Generate receipt number
- `GET /api/receipt/counter` - Get current receipt counter
- `GET /api/recordings` - Get recording statistics
- `GET /api/sample-counts` - Get sample count data

## 🧪 Testing

### **Test Pages Available**
- `test-ec2-server-endpoints.html` - Backend API testing
- `test-receipt-sequential-numbering.html` - Receipt system testing
- `test-data-reset-functionality.html` - Privacy reset testing
- `test-aws-credentials.html` - AWS connectivity testing

### **Running Tests**
```bash
# Frontend tests
npm test

# Backend health check
curl http://localhost:5000/health

# End-to-end testing
npm run test:e2e
```

## 🔒 Security & Privacy

### **Data Protection**
- **No Personal Identification**: Only demographic categories collected
- **Session Isolation**: Complete data reset between users
- **Secure Storage**: AWS S3 with proper access controls
- **HTTPS Encryption**: All data transmission encrypted

### **Privacy Compliance**
- **Consent Required**: Explicit user consent before recording
- **Data Minimization**: Only necessary data collected
- **User Control**: Users can exit at any time
- **Automatic Cleanup**: Session data cleared on page refresh

## 📊 Monitoring & Analytics

### **Application Metrics**
- Recording completion rates
- Session duration statistics
- Error tracking and logging
- AWS S3 upload success rates

### **Health Monitoring**
- Server uptime monitoring
- AWS service connectivity
- Storage capacity tracking
- Performance metrics

## 🤝 Contributing

### **Development Workflow**
1. Fork the repository
2. Create a feature branch
3. Make changes with tests
4. Submit pull request
5. Code review and merge

### **Code Standards**
- ESLint configuration enforced
- Prettier code formatting
- React best practices
- Comprehensive error handling

## 🔧 Troubleshooting

### **Common Issues & Solutions**

#### **Frontend Issues**
```bash
# Build failures
npm install --legacy-peer-deps
npm run build

# Camera access denied
# Ensure HTTPS or localhost for camera permissions
# Check browser camera permissions

# AWS upload failures
# Verify REACT_APP_AWS_* environment variables
# Check AWS Cognito Identity Pool configuration
```

#### **Backend Issues**
```bash
# Server won't start
# Check if port 5000 is available
netstat -tlnp | grep :5000
pkill -f node  # Kill existing processes

# AWS credential errors
# Verify .env file has correct AWS credentials
# Restart server after updating credentials

# CORS errors
# Check if Netlify domain is in CORS configuration
# Verify Origin header in browser requests
```

#### **Deployment Issues**
```bash
# Netlify build failures
# Check environment variables are set
# Verify build command: npm run build
# Check Node.js version compatibility

# EC2 connectivity issues
# Verify security group allows port 5000
# Check if server is running: curl http://*************:5000/health
# Ensure server listens on 0.0.0.0, not localhost
```

### **Performance Optimization**

#### **Frontend Optimization**
- Enable React production build
- Implement code splitting for large components
- Optimize video recording buffer sizes
- Use service workers for offline capability

#### **Backend Optimization**
- Implement connection pooling for AWS services
- Add request rate limiting
- Enable gzip compression
- Use PM2 for process management

### **Advanced Configuration**

#### **Custom Phrase Collections**
```javascript
// src/data/phrases.js
export const customPhrases = {
  "Medical Terms": [
    { phrase: "I need help", category: "Emergency" },
    { phrase: "Pain level 8", category: "Assessment" }
  ]
};
```

#### **AWS S3 Bucket Policies**
```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Principal": "*",
      "Action": "s3:PutObject",
      "Resource": "arn:aws:s3:::icudatasetphrasesfortesting/*",
      "Condition": {
        "StringEquals": {
          "s3:x-amz-acl": "bucket-owner-full-control"
        }
      }
    }
  ]
}
```

#### **Production Environment Setup**
```bash
# PM2 Process Management
npm install -g pm2
pm2 start ecosystem.config.js --env production
pm2 save
pm2 startup

# SSL Certificate (Let's Encrypt)
sudo certbot --nginx -d your-domain.com

# Nginx Reverse Proxy
server {
    listen 80;
    server_name your-domain.com;
    location / {
        proxy_pass http://localhost:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 📊 Analytics & Monitoring

### **Application Metrics**
- **Recording Success Rate**: Percentage of successful video uploads
- **Session Completion Rate**: Users who complete full recording sessions
- **Error Tracking**: Categorized error logging and reporting
- **Performance Metrics**: Page load times, video processing duration

### **AWS CloudWatch Integration**
```javascript
// Example CloudWatch metrics
const cloudwatch = new AWS.CloudWatch();
cloudwatch.putMetricData({
  Namespace: 'ICU-Dataset-App',
  MetricData: [{
    MetricName: 'VideoUploads',
    Value: 1,
    Unit: 'Count'
  }]
});
```

### **Health Monitoring**
- **Server Uptime**: Continuous availability monitoring
- **AWS Service Status**: S3 connectivity and response times
- **Storage Utilization**: S3 bucket usage tracking
- **Error Rate Monitoring**: Real-time error detection

## 🚀 Scaling & Production

### **Horizontal Scaling**
- **Load Balancer**: Distribute traffic across multiple EC2 instances
- **Auto Scaling Groups**: Automatic instance scaling based on demand
- **CDN Integration**: CloudFront for global content delivery
- **Database Migration**: Move from localStorage to RDS for persistence

### **Security Hardening**
- **WAF Integration**: Web Application Firewall for DDoS protection
- **VPC Configuration**: Private subnets for backend services
- **IAM Roles**: Least privilege access for AWS services
- **Encryption**: At-rest and in-transit data encryption

### **Backup & Recovery**
- **S3 Cross-Region Replication**: Automatic backup to secondary region
- **Database Backups**: Automated RDS snapshots
- **Code Repository**: GitHub with branch protection rules
- **Disaster Recovery**: Multi-region deployment strategy

## 📞 Support & Contact

### **Technical Support**
- **Documentation**: Comprehensive guides in `/docs` folder
- **Test Pages**: Built-in diagnostic tools for troubleshooting
- **Error Logging**: Detailed error messages in browser console
- **Health Checks**: Real-time system status monitoring

### **Community & Contributions**
- **Repository**: https://github.com/jasonhall1985/ICU_dataset_application_21.6.25
- **Issues**: GitHub Issues for bug reports and feature requests
- **Pull Requests**: Welcome contributions following coding standards
- **Discussions**: GitHub Discussions for questions and ideas

### **Emergency Contacts**
- **Critical Issues**: Use GitHub Issues with "urgent" label
- **Security Concerns**: Report via private repository issues
- **Medical Research Inquiries**: Contact through institutional channels

## 📄 License & Legal

### **Open Source License**
This project is developed for medical research purposes to support ICU patient communication systems. Please review the LICENSE file for specific terms and conditions.

### **Data Privacy Compliance**
- **GDPR Compliant**: European data protection standards
- **HIPAA Considerations**: Healthcare data protection guidelines
- **Institutional Review**: Ethics committee approval for research use
- **Consent Management**: Explicit user consent for data collection

### **Medical Research Ethics**
- **IRB Approval**: Institutional Review Board oversight
- **Data Anonymization**: No personally identifiable information stored
- **Research Purpose**: Exclusively for ICU communication improvement
- **Data Retention**: Defined retention periods for research data

---

## 🏥 **Mission Statement**

**Supporting ICU Patient Care Through Technology**

This application contributes to critical medical research that could help save lives by improving communication systems in intensive care units. Every recording collected supports the development of better communication tools for patients who cannot speak due to medical interventions.

**Together, we're building technology that gives voice to those who need it most.**

---

*Last Updated: July 2025*
*Version: 1.0.0*
*Deployment: Production Ready*
