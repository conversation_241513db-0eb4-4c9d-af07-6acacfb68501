import React from 'react';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { CssBaseline, Snackbar, Alert } from '@mui/material';
import './App.css';

// Import providers
import AppStateProvider, { useAppState } from './providers/AppStateProvider';
import SessionStateProvider from './providers/SessionStateProvider';
import RecordingSessionProvider from './providers/RecordingSessionProvider';

// Import main application component
import AppContent from './components/AppContent';

// Notification component that uses app state
const AppNotifications = () => {
  const { notification, clearNotification } = useAppState();

  return (
    <Snackbar
      open={notification.open}
      autoHideDuration={6000}
      onClose={clearNotification}
    >
      <Alert onClose={clearNotification} severity={notification.severity} sx={{ width: '100%' }}>
        {notification.message}
      </Alert>
    </Snackbar>
  );
};

// Main App Shell Component - Clean and Focused
const App = () => {
  return (
    <AppStateProvider>
      <SessionStateProvider>
        <RecordingSessionProvider>
          <ThemeProvider theme={createTheme()}>
            <CssBaseline />
            <div className="App">
              <AppContent />
              <AppNotifications />
            </div>
          </ThemeProvider>
        </RecordingSessionProvider>
      </SessionStateProvider>
    </AppStateProvider>
  );
};

export default App;
