/**
 * Upload Debugging Utility
 * Provides systematic debugging for video upload pipeline
 */

export class UploadDebugger {
  constructor() {
    this.logs = [];
    this.startTime = null;
  }

  startDebugging(context = 'Upload Process') {
    this.startTime = Date.now();
    this.log('🚀 UPLOAD DEBUG: Starting debugging session', { context, timestamp: new Date().toISOString() });
  }

  log(message, data = null) {
    const logEntry = {
      timestamp: new Date().toISOString(),
      elapsed: this.startTime ? Date.now() - this.startTime : 0,
      message,
      data
    };
    
    this.logs.push(logEntry);
    
    if (data) {
      console.log(`${message}:`, data);
    } else {
      console.log(message);
    }
  }

  error(message, error = null) {
    const errorEntry = {
      timestamp: new Date().toISOString(),
      elapsed: this.startTime ? Date.now() - this.startTime : 0,
      level: 'ERROR',
      message,
      error: error ? {
        name: error.name,
        message: error.message,
        stack: error.stack,
        code: error.code
      } : null
    };
    
    this.logs.push(errorEntry);
    console.error(`❌ ${message}`, error || '');
  }

  debugVideoBlob(videoBlob, context = 'Video Blob') {
    this.log(`🎬 ${context} Analysis`, {
      size: videoBlob.size,
      type: videoBlob.type,
      sizeInMB: (videoBlob.size / (1024 * 1024)).toFixed(2),
      isValid: videoBlob.size > 0 && videoBlob.type.includes('video')
    });

    // Check for common issues
    if (videoBlob.size === 0) {
      this.error('Video blob is empty');
      return false;
    }

    if (videoBlob.size > 100 * 1024 * 1024) { // 100MB
      this.error('Video blob is very large (>100MB)', { size: videoBlob.size });
    }

    if (!videoBlob.type.includes('video')) {
      this.error('Video blob has invalid MIME type', { type: videoBlob.type });
      return false;
    }

    return true;
  }

  debugDemographics(demographics, context = 'Demographics') {
    this.log(`👤 ${context} Analysis`, demographics);

    const requiredFields = ['userId', 'ageGroup', 'gender', 'ethnicity'];
    const missingFields = requiredFields.filter(field => !demographics[field]);

    if (missingFields.length > 0) {
      this.error('Missing required demographic fields', { missingFields });
      return false;
    }

    // Validate field values
    const validAgeGroups = ['18to39', '40to64', '65plus'];
    const validGenders = ['male', 'female', 'nonbinary'];
    const validEthnicities = ['caucasian', 'asian', 'african', 'hispanic', 'middle_eastern', 'pacific_islander', 'native_american', 'mixed', 'not_specified'];

    if (!validAgeGroups.includes(demographics.ageGroup)) {
      this.error('Invalid age group', { ageGroup: demographics.ageGroup, validOptions: validAgeGroups });
    }

    if (!validGenders.includes(demographics.gender)) {
      this.error('Invalid gender', { gender: demographics.gender, validOptions: validGenders });
    }

    if (!validEthnicities.includes(demographics.ethnicity)) {
      this.error('Invalid ethnicity', { ethnicity: demographics.ethnicity, validOptions: validEthnicities });
    }

    return true;
  }

  debugAWSConfig() {
    this.log('🔧 AWS Configuration Check', {
      frontendConfig: {
        identityPoolId: process.env.REACT_APP_AWS_IDENTITY_POOL_ID,
        region: process.env.REACT_APP_AWS_REGION,
        bucket: process.env.REACT_APP_S3_BUCKET,
        hasIdentityPool: !!process.env.REACT_APP_AWS_IDENTITY_POOL_ID && 
                        process.env.REACT_APP_AWS_IDENTITY_POOL_ID !== 'your-identity-pool-id-here'
      }
    });

    // Check if configuration looks valid
    const hasValidConfig = process.env.REACT_APP_AWS_IDENTITY_POOL_ID && 
                          process.env.REACT_APP_AWS_IDENTITY_POOL_ID !== 'your-identity-pool-id-here' &&
                          process.env.REACT_APP_AWS_REGION &&
                          process.env.REACT_APP_S3_BUCKET;

    if (!hasValidConfig) {
      this.error('AWS configuration appears to be incomplete or using placeholder values');
      return false;
    }

    return true;
  }

  debugUploadRequest(formData, endpoint = '/upload') {
    this.log(`📤 Upload Request Debug`, {
      endpoint,
      hasFormData: !!formData,
      formDataEntries: formData ? Array.from(formData.keys()) : []
    });

    if (!formData) {
      this.error('FormData is null or undefined');
      return false;
    }

    // Check required fields
    const requiredFields = ['video', 'phrase', 'category', 'recordingNumber', 'demographics'];
    const missingFields = requiredFields.filter(field => !formData.has(field));

    if (missingFields.length > 0) {
      this.error('Missing required form fields', { missingFields });
      return false;
    }

    return true;
  }

  generateReport() {
    const report = {
      sessionId: `debug-${Date.now()}`,
      startTime: this.startTime,
      totalDuration: this.startTime ? Date.now() - this.startTime : 0,
      totalLogs: this.logs.length,
      errors: this.logs.filter(log => log.level === 'ERROR'),
      logs: this.logs
    };

    this.log('📊 Debug Report Generated', {
      totalDuration: `${report.totalDuration}ms`,
      totalLogs: report.totalLogs,
      errorCount: report.errors.length
    });

    return report;
  }

  exportLogs() {
    const report = this.generateReport();
    const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    
    const a = document.createElement('a');
    a.href = url;
    a.download = `upload-debug-${Date.now()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    this.log('💾 Debug logs exported to file');
  }

  clear() {
    this.logs = [];
    this.startTime = null;
    console.log('🧹 Upload debugger cleared');
  }
}

// Create a singleton instance
export const uploadDebugger = new UploadDebugger();

// Helper function to debug complete upload process
export const debugUploadProcess = async (videoBlob, phrase, demographics, category = 'general', recordingNumber = 1) => {
  uploadDebugger.startDebugging('Complete Upload Process');
  
  try {
    // Debug video blob
    const videoBlobValid = uploadDebugger.debugVideoBlob(videoBlob);
    if (!videoBlobValid) {
      throw new Error('Video blob validation failed');
    }

    // Debug demographics
    const demographicsValid = uploadDebugger.debugDemographics(demographics);
    if (!demographicsValid) {
      throw new Error('Demographics validation failed');
    }

    // Debug AWS config
    const awsConfigValid = uploadDebugger.debugAWSConfig();
    if (!awsConfigValid) {
      uploadDebugger.log('⚠️ AWS config invalid - will use backend upload');
    }

    // Create FormData for backend upload
    const formData = new FormData();
    formData.append('video', videoBlob, `${phrase}_${Date.now()}.webm`);
    formData.append('phrase', phrase);
    formData.append('category', category);
    formData.append('recordingNumber', recordingNumber.toString());
    formData.append('demographics', JSON.stringify(demographics));

    // Debug upload request
    const requestValid = uploadDebugger.debugUploadRequest(formData);
    if (!requestValid) {
      throw new Error('Upload request validation failed');
    }

    uploadDebugger.log('✅ All validations passed - ready for upload');
    return uploadDebugger.generateReport();

  } catch (error) {
    uploadDebugger.error('Upload process debugging failed', error);
    return uploadDebugger.generateReport();
  }
};
