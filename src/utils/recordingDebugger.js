/**
 * Recording Debugger Utility
 * Comprehensive debugging tools for video recording save functionality
 */

// Test AWS Configuration
export const testAWSConfiguration = () => {
  console.log('🔧 === AWS CONFIGURATION TEST ===');
  
  const config = {
    identityPoolId: process.env.REACT_APP_AWS_IDENTITY_POOL_ID,
    region: process.env.REACT_APP_AWS_REGION,
    bucket: process.env.REACT_APP_S3_BUCKET
  };
  
  console.log('Environment Variables:');
  console.log('  REACT_APP_AWS_IDENTITY_POOL_ID:', config.identityPoolId);
  console.log('  REACT_APP_AWS_REGION:', config.region);
  console.log('  REACT_APP_S3_BUCKET:', config.bucket);
  
  const issues = [];
  
  if (!config.identityPoolId || config.identityPoolId === 'your-identity-pool-id-here') {
    issues.push('❌ AWS Identity Pool ID not configured');
  } else {
    console.log('✅ AWS Identity Pool ID configured');
  }
  
  if (!config.region) {
    issues.push('❌ AWS Region not configured');
  } else {
    console.log('✅ AWS Region configured');
  }
  
  if (!config.bucket) {
    issues.push('❌ S3 Bucket not configured');
  } else {
    console.log('✅ S3 Bucket configured');
  }
  
  if (issues.length > 0) {
    console.log('🚨 Configuration Issues Found:');
    issues.forEach(issue => console.log('  ' + issue));
    return { success: false, issues };
  } else {
    console.log('✅ AWS Configuration appears correct');
    return { success: true, config };
  }
};

// Test localStorage functionality
export const testLocalStorageTracking = () => {
  console.log('📱 === LOCALSTORAGE TRACKING TEST ===');
  
  try {
    // Test basic localStorage functionality
    const testKey = 'icuAppTest';
    const testData = { test: true, timestamp: Date.now() };
    
    localStorage.setItem(testKey, JSON.stringify(testData));
    const retrieved = JSON.parse(localStorage.getItem(testKey));
    localStorage.removeItem(testKey);
    
    if (retrieved.test === true) {
      console.log('✅ localStorage basic functionality working');
    } else {
      console.log('❌ localStorage basic functionality failed');
      return { success: false, error: 'Basic localStorage test failed' };
    }
    
    // Check existing recording counts
    const recordingsCount = localStorage.getItem('icuAppRecordingsCount');
    if (recordingsCount) {
      const parsed = JSON.parse(recordingsCount);
      console.log('📊 Existing recording counts:', parsed);
      console.log('📊 Total phrases with recordings:', Object.keys(parsed).length);
    } else {
      console.log('📊 No existing recording counts found');
    }
    
    // Check completed phrases
    const completedPhrases = localStorage.getItem('icuAppCompletedPhrases');
    if (completedPhrases) {
      const parsed = JSON.parse(completedPhrases);
      console.log('✅ Completed phrases:', parsed);
    } else {
      console.log('📊 No completed phrases found');
    }
    
    return { success: true };
  } catch (error) {
    console.error('❌ localStorage test failed:', error);
    return { success: false, error: error.message };
  }
};

// Test video blob creation
export const testVideoBlobCreation = async () => {
  console.log('🎬 === VIDEO BLOB CREATION TEST ===');
  
  try {
    // Create a test video blob
    const canvas = document.createElement('canvas');
    canvas.width = 640;
    canvas.height = 480;
    const ctx = canvas.getContext('2d');
    
    // Draw a simple test pattern
    ctx.fillStyle = 'blue';
    ctx.fillRect(0, 0, 640, 480);
    ctx.fillStyle = 'white';
    ctx.font = '48px Arial';
    ctx.fillText('Test Video', 200, 240);
    
    // Convert to blob
    return new Promise((resolve) => {
      canvas.toBlob((blob) => {
        if (blob && blob.size > 0) {
          console.log('✅ Test video blob created successfully');
          console.log('📊 Blob details:', {
            size: blob.size,
            type: blob.type
          });
          resolve({ success: true, blob });
        } else {
          console.log('❌ Failed to create test video blob');
          resolve({ success: false, error: 'Blob creation failed' });
        }
      }, 'video/webm');
    });
  } catch (error) {
    console.error('❌ Video blob creation test failed:', error);
    return { success: false, error: error.message };
  }
};

// Test S3 path generation
export const testS3PathGeneration = () => {
  console.log('🗂️ === S3 PATH GENERATION TEST ===');
  
  const testCases = [
    {
      phrase: 'I need water',
      demographics: {
        ageGroup: '40to64',
        gender: 'female',
        ethnicity: 'caucasian',
        userId: 'user01'
      }
    },
    {
      phrase: 'Call my family',
      demographics: {
        ageGroup: '18to39',
        gender: 'male',
        ethnicity: 'asian',
        userId: 'user02'
      }
    }
  ];
  
  testCases.forEach((testCase, index) => {
    console.log(`Test case ${index + 1}:`);
    console.log('  Input:', testCase);
    
    // Simulate the path generation logic
    const sanitizedPhrase = testCase.phrase.toLowerCase().replace(/[^\w\s]/g, '').replace(/\s+/g, '_');
    const expectedPath = `icu-videos/${testCase.demographics.ageGroup}/${testCase.demographics.gender}/${testCase.demographics.ethnicity}/${sanitizedPhrase}/`;
    
    console.log('  Expected S3 path prefix:', expectedPath);
  });
  
  return { success: true };
};

// Comprehensive recording test
export const runComprehensiveRecordingTest = async () => {
  console.log('🧪 === COMPREHENSIVE RECORDING TEST ===');
  
  const results = {
    awsConfig: testAWSConfiguration(),
    localStorage: testLocalStorageTracking(),
    videoBlobCreation: await testVideoBlobCreation(),
    s3PathGeneration: testS3PathGeneration()
  };
  
  console.log('📋 === TEST RESULTS SUMMARY ===');
  Object.entries(results).forEach(([test, result]) => {
    const status = result.success ? '✅' : '❌';
    console.log(`${status} ${test}: ${result.success ? 'PASSED' : 'FAILED'}`);
    if (!result.success && result.error) {
      console.log(`   Error: ${result.error}`);
    }
  });
  
  const allPassed = Object.values(results).every(r => r.success);
  console.log(`\n🎯 Overall Status: ${allPassed ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}`);
  
  return { success: allPassed, results };
};

// Debug recording save process
export const debugRecordingSaveProcess = (videoBlob, phrase, category, demographics) => {
  console.log('🔍 === RECORDING SAVE PROCESS DEBUG ===');
  console.log('Input parameters:');
  console.log('  videoBlob:', {
    size: videoBlob?.size,
    type: videoBlob?.type,
    isValid: videoBlob instanceof Blob
  });
  console.log('  phrase:', phrase);
  console.log('  category:', category);
  console.log('  demographics:', demographics);
  
  // Validate inputs
  const validations = [];
  
  if (!videoBlob || !(videoBlob instanceof Blob)) {
    validations.push('❌ Invalid video blob');
  } else if (videoBlob.size === 0) {
    validations.push('❌ Empty video blob');
  } else {
    validations.push('✅ Valid video blob');
  }
  
  if (!phrase || phrase.trim() === '') {
    validations.push('❌ Invalid phrase');
  } else {
    validations.push('✅ Valid phrase');
  }
  
  if (!category || category.trim() === '') {
    validations.push('❌ Invalid category');
  } else {
    validations.push('✅ Valid category');
  }
  
  if (!demographics || typeof demographics !== 'object') {
    validations.push('❌ Invalid demographics');
  } else {
    validations.push('✅ Valid demographics');
  }
  
  console.log('Validation results:');
  validations.forEach(v => console.log('  ' + v));
  
  const isValid = validations.every(v => v.startsWith('✅'));
  console.log(`\n🎯 Input validation: ${isValid ? '✅ PASSED' : '❌ FAILED'}`);
  
  return { success: isValid, validations };
};

// Export all functions for easy access
export default {
  testAWSConfiguration,
  testLocalStorageTracking,
  testVideoBlobCreation,
  testS3PathGeneration,
  runComprehensiveRecordingTest,
  debugRecordingSaveProcess
};
