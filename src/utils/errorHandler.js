/**
 * Global Error Handler Utility
 * Provides centralized error handling and logging for the application
 */

import { logErrorToServer } from '../services/loggingService';

// Error types
export const ERROR_TYPES = {
  NETWORK: 'NETWORK_ERROR',
  VALIDATION: 'VALIDATION_ERROR',
  PERMISSION: 'PERMISSION_ERROR',
  UPLOAD: 'UPLOAD_ERROR',
  CAMERA: 'CAMERA_ERROR',
  AWS: 'AWS_ERROR',
  UNKNOWN: 'UNKNOWN_ERROR'
};

// Error severity levels
export const ERROR_SEVERITY = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  CRITICAL: 'critical'
};

/**
 * Log error to console with structured format
 * @param {Error} error - The error object
 * @param {string} context - Context where the error occurred
 * @param {string} severity - Error severity level
 */
export const logError = (error, context = 'Unknown', severity = ERROR_SEVERITY.MEDIUM) => {
  const timestamp = new Date().toISOString();
  const errorInfo = {
    timestamp,
    context,
    severity,
    message: error.message || 'Unknown error',
    stack: error.stack,
    type: error.name || 'Error'
  };

  // Log to console with appropriate level
  switch (severity) {
    case ERROR_SEVERITY.CRITICAL:
    case ERROR_SEVERITY.HIGH:
      console.error('🚨 ERROR:', errorInfo);
      break;
    case ERROR_SEVERITY.MEDIUM:
      console.warn('⚠️ WARNING:', errorInfo);
      break;
    case ERROR_SEVERITY.LOW:
      console.info('ℹ️ INFO:', errorInfo);
      break;
    default:
      console.log('📝 LOG:', errorInfo);
  }

  // Send error to server for centralized logging
  try {
    logErrorToServer({
      message: error.message || 'Unknown error',
      context,
      severity,
      stack: error.stack
    });
  } catch (logError) {
    console.warn('Failed to send error to server:', logError);
  }
};

/**
 * Handle camera-related errors
 * @param {Error} error - Camera error
 * @returns {Object} User-friendly error message
 */
export const handleCameraError = (error) => {
  let userMessage = 'Camera error occurred';
  let severity = ERROR_SEVERITY.MEDIUM;

  switch (error.name) {
    case 'NotAllowedError':
      userMessage = 'Camera access denied. Please allow camera access in your browser settings.';
      severity = ERROR_SEVERITY.HIGH;
      break;
    case 'NotFoundError':
      userMessage = 'No camera found. Please connect a camera and try again.';
      severity = ERROR_SEVERITY.HIGH;
      break;
    case 'NotReadableError':
      userMessage = 'Camera is already in use by another application.';
      severity = ERROR_SEVERITY.MEDIUM;
      break;
    case 'OverconstrainedError':
      userMessage = 'Camera does not support the required settings.';
      severity = ERROR_SEVERITY.MEDIUM;
      break;
    case 'SecurityError':
      userMessage = 'Camera access blocked due to security restrictions.';
      severity = ERROR_SEVERITY.HIGH;
      break;
    default:
      userMessage = `Camera error: ${error.message || 'Unknown camera error'}`;
  }

  logError(error, 'Camera', severity);
  
  return {
    type: ERROR_TYPES.CAMERA,
    message: userMessage,
    severity,
    originalError: error
  };
};

/**
 * Handle network-related errors
 * @param {Error} error - Network error
 * @returns {Object} User-friendly error message
 */
export const handleNetworkError = (error) => {
  let userMessage = 'Network error occurred';
  let severity = ERROR_SEVERITY.MEDIUM;

  if (error.message.includes('fetch')) {
    userMessage = 'Unable to connect to server. Please check your internet connection.';
    severity = ERROR_SEVERITY.HIGH;
  } else if (error.message.includes('timeout')) {
    userMessage = 'Request timed out. Please try again.';
    severity = ERROR_SEVERITY.MEDIUM;
  } else if (error.message.includes('CORS')) {
    userMessage = 'Server configuration error. Please contact support.';
    severity = ERROR_SEVERITY.HIGH;
  }

  logError(error, 'Network', severity);
  
  return {
    type: ERROR_TYPES.NETWORK,
    message: userMessage,
    severity,
    originalError: error
  };
};

/**
 * Handle upload-related errors
 * @param {Error} error - Upload error
 * @returns {Object} User-friendly error message
 */
export const handleUploadError = (error) => {
  let userMessage = 'Recording processing failed';
  let severity = ERROR_SEVERITY.MEDIUM;

  if (error.message.includes('413') || error.message.includes('too large')) {
    userMessage = 'File is too large. Please try recording a shorter video.';
    severity = ERROR_SEVERITY.MEDIUM;
  } else if (error.message.includes('400')) {
    userMessage = 'Invalid file format. Please try recording again.';
    severity = ERROR_SEVERITY.MEDIUM;
  } else if (error.message.includes('500')) {
    userMessage = 'Server error during upload. Please try again later.';
    severity = ERROR_SEVERITY.HIGH;
  } else if (error.message.includes('AWS') || error.message.includes('S3')) {
    userMessage = 'Cloud storage error. Your recording has been saved locally.';
    severity = ERROR_SEVERITY.MEDIUM;
  } else if (error.message.includes('simulated') || error.message.includes('simulation')) {
    userMessage = 'Recording saved successfully (simulated mode)';
    severity = ERROR_SEVERITY.LOW;
  }

  logError(error, 'Upload', severity);
  
  return {
    type: ERROR_TYPES.UPLOAD,
    message: userMessage,
    severity,
    originalError: error
  };
};

/**
 * Generic error handler for unknown errors
 * @param {Error} error - Unknown error
 * @param {string} context - Context where error occurred
 * @returns {Object} User-friendly error message
 */
export const handleGenericError = (error, context = 'Application') => {
  const userMessage = 'An unexpected error occurred. Please try again.';
  const severity = ERROR_SEVERITY.MEDIUM;

  logError(error, context, severity);
  
  return {
    type: ERROR_TYPES.UNKNOWN,
    message: userMessage,
    severity,
    originalError: error
  };
};

/**
 * Main error handler that routes to specific handlers
 * @param {Error} error - The error to handle
 * @param {string} context - Context where error occurred
 * @returns {Object} User-friendly error message
 */
export const handleError = (error, context = 'Application') => {
  if (!error) {
    return handleGenericError(new Error('Unknown error'), context);
  }

  // Route to specific error handlers based on error type or context
  if (context.toLowerCase().includes('camera') || error.name?.includes('Media')) {
    return handleCameraError(error);
  }
  
  if (context.toLowerCase().includes('network') || context.toLowerCase().includes('fetch')) {
    return handleNetworkError(error);
  }
  
  if (context.toLowerCase().includes('upload') || context.toLowerCase().includes('s3')) {
    return handleUploadError(error);
  }
  
  return handleGenericError(error, context);
};

export default {
  handleError,
  handleCameraError,
  handleNetworkError,
  handleUploadError,
  handleGenericError,
  logError,
  ERROR_TYPES,
  ERROR_SEVERITY
};
