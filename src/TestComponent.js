import React from 'react';

function TestComponent() {
  console.log('🧪 TestComponent rendered successfully');
  
  return (
    <div style={{
      padding: '20px',
      backgroundColor: '#e8f5e8',
      border: '2px solid #4caf50',
      borderRadius: '8px',
      margin: '20px',
      textAlign: 'center'
    }}>
      <h1 style={{ color: '#2e7d32' }}>✅ React is Working!</h1>
      <p>If you can see this message, React is loading and rendering correctly.</p>
      <p><strong>Timestamp:</strong> {new Date().toLocaleString()}</p>
      <button 
        onClick={() => alert('Button clicked! React event handling is working.')}
        style={{
          padding: '10px 20px',
          backgroundColor: '#4caf50',
          color: 'white',
          border: 'none',
          borderRadius: '4px',
          cursor: 'pointer'
        }}
      >
        Test Button
      </button>
    </div>
  );
}

export default TestComponent;
