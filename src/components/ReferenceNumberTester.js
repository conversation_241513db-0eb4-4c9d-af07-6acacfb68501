import React, { useState } from 'react';
import {
  Box,
  Paper,
  Typo<PERSON>,
  Button,
  Alert,
  LinearProgress,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Chip,
  Grid,
  Card,
  CardContent
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import ErrorIcon from '@mui/icons-material/Error';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import BugReportIcon from '@mui/icons-material/BugReport';
import referenceNumberService from '../services/referenceNumberService';

const ReferenceNumberTester = () => {
  const [testResults, setTestResults] = useState([]);
  const [isRunning, setIsRunning] = useState(false);
  const [progress, setProgress] = useState(0);
  const [generatedReferences, setGeneratedReferences] = useState([]);

  const addTestResult = (testName, passed, message, details = null) => {
    setTestResults(prev => [...prev, {
      testName,
      passed,
      message,
      details,
      timestamp: new Date().toISOString()
    }]);
  };

  const runComprehensiveTests = async () => {
    setIsRunning(true);
    setProgress(0);
    setTestResults([]);
    setGeneratedReferences([]);

    const tests = [
      testReferenceNumberGeneration,
      testUniquenessAcrossSessions,
      testDataPersistence,
      testSessionDataStructure,
      testVerificationSystem,
      testDataRetrieval,
      testMultipleSessionHandling,
      testErrorHandling,
      testDataIntegrity
    ];

    for (let i = 0; i < tests.length; i++) {
      try {
        await tests[i]();
        setProgress(((i + 1) / tests.length) * 100);
        // Small delay to show progress
        await new Promise(resolve => setTimeout(resolve, 100));
      } catch (error) {
        addTestResult(
          `Test ${i + 1}`,
          false,
          `Test failed with error: ${error.message}`,
          { error: error.stack }
        );
      }
    }

    setIsRunning(false);
  };

  // Test 1: Reference Number Generation
  const testReferenceNumberGeneration = async () => {
    const testName = 'Reference Number Generation';
    
    try {
      const refNum = referenceNumberService.generateReferenceNumber();
      
      // Check format
      const formatRegex = /^ICU-[A-Z0-9]+-[A-Z0-9]+$/;
      if (!formatRegex.test(refNum)) {
        addTestResult(testName, false, `Invalid format: ${refNum}`);
        return;
      }

      // Check length
      if (refNum.length < 10 || refNum.length > 30) {
        addTestResult(testName, false, `Invalid length: ${refNum.length}`);
        return;
      }

      addTestResult(testName, true, `Generated valid reference: ${refNum}`, { referenceNumber: refNum });
    } catch (error) {
      addTestResult(testName, false, `Generation failed: ${error.message}`);
    }
  };

  // Test 2: Uniqueness Across Sessions
  const testUniquenessAcrossSessions = async () => {
    const testName = 'Uniqueness Across Sessions';
    const generatedNumbers = new Set();
    const testCount = 50;
    
    try {
      for (let i = 0; i < testCount; i++) {
        const refNum = referenceNumberService.generateReferenceNumber();
        
        if (generatedNumbers.has(refNum)) {
          addTestResult(testName, false, `Duplicate found: ${refNum} (iteration ${i + 1})`);
          return;
        }
        
        generatedNumbers.add(refNum);
      }

      setGeneratedReferences(Array.from(generatedNumbers));
      addTestResult(
        testName, 
        true, 
        `Generated ${testCount} unique reference numbers`,
        { count: testCount, samples: Array.from(generatedNumbers).slice(0, 5) }
      );
    } catch (error) {
      addTestResult(testName, false, `Uniqueness test failed: ${error.message}`);
    }
  };

  // Test 3: Data Persistence
  const testDataPersistence = async () => {
    const testName = 'Data Persistence';
    
    try {
      const mockSessionData = {
        demographicInfo: { ageGroup: '18to39', gender: 'female', ethnicity: 'caucasian', userId: 'test001' },
        selectedPhrases: ['test phrase 1', 'test phrase 2'],
        recordingsCount: { 'test phrase 1': 3, 'test phrase 2': 2 },
        sessionStats: { startTime: new Date(), recentRecordings: [] },
        sessionRecordingsCount: 5
      };

      const result = referenceNumberService.createSessionReference(mockSessionData);
      
      if (!result.success) {
        addTestResult(testName, false, 'Failed to create session reference');
        return;
      }

      // Verify data was stored
      const retrievedData = referenceNumberService.getSessionData(result.referenceNumber);
      
      if (!retrievedData) {
        addTestResult(testName, false, 'Failed to retrieve stored data');
        return;
      }

      // Verify key fields
      if (retrievedData.demographics.ageGroup !== mockSessionData.demographicInfo.ageGroup) {
        addTestResult(testName, false, 'Demographics not preserved correctly');
        return;
      }

      addTestResult(
        testName, 
        true, 
        `Data persisted successfully for ${result.referenceNumber}`,
        { referenceNumber: result.referenceNumber, dataKeys: Object.keys(retrievedData) }
      );
    } catch (error) {
      addTestResult(testName, false, `Persistence test failed: ${error.message}`);
    }
  };

  // Test 4: Session Data Structure
  const testSessionDataStructure = async () => {
    const testName = 'Session Data Structure';
    
    try {
      const mockSessionData = {
        demographicInfo: { ageGroup: '40to64', gender: 'male', ethnicity: 'asian', userId: 'test002' },
        selectedPhrases: ['phrase A', 'phrase B', 'phrase C'],
        recordingsCount: { 'phrase A': 3, 'phrase B': 3, 'phrase C': 1 },
        sessionStats: { startTime: new Date(), recentRecordings: [] },
        sessionRecordingsCount: 7
      };

      const result = referenceNumberService.createSessionReference(mockSessionData);
      const sessionData = result.sessionData;

      // Check required fields
      const requiredFields = [
        'timestamp', 'sessionStartTime', 'sessionEndTime', 'sessionDuration',
        'demographics', 'selectedPhrases', 'phrasesRecorded', 'totalRecordingsInSession',
        'recordingCounts', 'applicationVersion', 'consentWithdrawalInfo'
      ];

      const missingFields = requiredFields.filter(field => !(field in sessionData));
      
      if (missingFields.length > 0) {
        addTestResult(testName, false, `Missing required fields: ${missingFields.join(', ')}`);
        return;
      }

      // Check data types and values
      if (typeof sessionData.sessionDuration !== 'number') {
        addTestResult(testName, false, 'Session duration should be a number');
        return;
      }

      if (!Array.isArray(sessionData.phrasesRecorded)) {
        addTestResult(testName, false, 'Phrases recorded should be an array');
        return;
      }

      addTestResult(
        testName, 
        true, 
        'Session data structure is valid',
        { 
          fieldsPresent: requiredFields.length,
          phrasesRecorded: sessionData.phrasesRecorded.length,
          totalRecordings: sessionData.totalRecordingsInSession
        }
      );
    } catch (error) {
      addTestResult(testName, false, `Structure test failed: ${error.message}`);
    }
  };

  // Test 5: Verification System
  const testVerificationSystem = async () => {
    const testName = 'Verification System';
    
    try {
      // Test with valid reference
      const mockSessionData = {
        demographicInfo: { ageGroup: '65plus', gender: 'female', ethnicity: 'hispanic', userId: 'test003' },
        selectedPhrases: ['verify phrase'],
        recordingsCount: { 'verify phrase': 3 },
        sessionStats: { startTime: new Date(), recentRecordings: [] },
        sessionRecordingsCount: 3
      };

      const result = referenceNumberService.createSessionReference(mockSessionData);
      const verification = referenceNumberService.verifyReferenceNumber(result.referenceNumber);

      if (!verification.exists || !verification.valid) {
        addTestResult(testName, false, 'Valid reference number failed verification');
        return;
      }

      // Test with invalid reference
      const invalidVerification = referenceNumberService.verifyReferenceNumber('ICU-INVALID-123456');
      
      if (invalidVerification.exists || invalidVerification.valid) {
        addTestResult(testName, false, 'Invalid reference number passed verification');
        return;
      }

      addTestResult(
        testName, 
        true, 
        'Verification system working correctly',
        { 
          validTest: verification.message,
          invalidTest: invalidVerification.message
        }
      );
    } catch (error) {
      addTestResult(testName, false, `Verification test failed: ${error.message}`);
    }
  };

  // Test 6: Data Retrieval
  const testDataRetrieval = async () => {
    const testName = 'Data Retrieval';
    
    try {
      // Create multiple sessions
      const sessions = [];
      for (let i = 0; i < 3; i++) {
        const mockData = {
          demographicInfo: { ageGroup: '18to39', gender: 'male', ethnicity: 'other', userId: `retrieve${i}` },
          selectedPhrases: [`phrase ${i}`],
          recordingsCount: { [`phrase ${i}`]: 3 },
          sessionStats: { startTime: new Date(), recentRecordings: [] },
          sessionRecordingsCount: 3
        };
        
        const result = referenceNumberService.createSessionReference(mockData);
        sessions.push(result.referenceNumber);
      }

      // Test retrieval of each session
      for (const refNum of sessions) {
        const data = referenceNumberService.getSessionData(refNum);
        if (!data) {
          addTestResult(testName, false, `Failed to retrieve data for ${refNum}`);
          return;
        }
      }

      // Test statistics
      const stats = referenceNumberService.getStatistics();
      if (!stats || stats.totalSessions < 3) {
        addTestResult(testName, false, 'Statistics not reflecting created sessions');
        return;
      }

      addTestResult(
        testName, 
        true, 
        `Successfully retrieved data for ${sessions.length} sessions`,
        { 
          sessions: sessions,
          totalSessions: stats.totalSessions,
          totalRecordings: stats.totalRecordings
        }
      );
    } catch (error) {
      addTestResult(testName, false, `Retrieval test failed: ${error.message}`);
    }
  };

  // Test 7: Multiple Session Handling
  const testMultipleSessionHandling = async () => {
    const testName = 'Multiple Session Handling';
    
    try {
      const sessionCount = 10;
      const createdSessions = [];

      for (let i = 0; i < sessionCount; i++) {
        const mockData = {
          demographicInfo: { 
            ageGroup: ['18to39', '40to64', '65plus'][i % 3], 
            gender: ['male', 'female'][i % 2], 
            ethnicity: 'test', 
            userId: `multi${i}` 
          },
          selectedPhrases: [`multi phrase ${i}`],
          recordingsCount: { [`multi phrase ${i}`]: Math.floor(Math.random() * 3) + 1 },
          sessionStats: { startTime: new Date(Date.now() - i * 1000), recentRecordings: [] },
          sessionRecordingsCount: Math.floor(Math.random() * 10) + 1
        };
        
        const result = referenceNumberService.createSessionReference(mockData);
        if (result.success) {
          createdSessions.push(result.referenceNumber);
        }
      }

      if (createdSessions.length !== sessionCount) {
        addTestResult(testName, false, `Only created ${createdSessions.length} of ${sessionCount} sessions`);
        return;
      }

      // Verify all sessions are retrievable
      let retrievableCount = 0;
      for (const refNum of createdSessions) {
        if (referenceNumberService.getSessionData(refNum)) {
          retrievableCount++;
        }
      }

      if (retrievableCount !== sessionCount) {
        addTestResult(testName, false, `Only ${retrievableCount} of ${sessionCount} sessions retrievable`);
        return;
      }

      addTestResult(
        testName, 
        true, 
        `Successfully handled ${sessionCount} concurrent sessions`,
        { 
          created: createdSessions.length,
          retrievable: retrievableCount,
          sampleReferences: createdSessions.slice(0, 3)
        }
      );
    } catch (error) {
      addTestResult(testName, false, `Multiple session test failed: ${error.message}`);
    }
  };

  // Test 8: Error Handling
  const testErrorHandling = async () => {
    const testName = 'Error Handling';
    
    try {
      // Test with invalid session data
      const invalidResult = referenceNumberService.createSessionReference(null);
      
      if (invalidResult.success) {
        addTestResult(testName, false, 'Should not succeed with null session data');
        return;
      }

      // Test verification with empty string
      const emptyVerification = referenceNumberService.verifyReferenceNumber('');
      
      if (emptyVerification.valid) {
        addTestResult(testName, false, 'Should not validate empty reference number');
        return;
      }

      // Test retrieval with non-existent reference
      const nonExistentData = referenceNumberService.getSessionData('ICU-NONEXISTENT-123456');
      
      if (nonExistentData !== null) {
        addTestResult(testName, false, 'Should return null for non-existent reference');
        return;
      }

      addTestResult(
        testName, 
        true, 
        'Error handling working correctly',
        { 
          invalidDataHandled: !invalidResult.success,
          emptyStringHandled: !emptyVerification.valid,
          nonExistentHandled: nonExistentData === null
        }
      );
    } catch (error) {
      addTestResult(testName, false, `Error handling test failed: ${error.message}`);
    }
  };

  // Test 9: Data Integrity
  const testDataIntegrity = async () => {
    const testName = 'Data Integrity';
    
    try {
      const originalData = {
        demographicInfo: { ageGroup: '40to64', gender: 'female', ethnicity: 'caucasian', userId: 'integrity001' },
        selectedPhrases: ['integrity phrase 1', 'integrity phrase 2'],
        recordingsCount: { 'integrity phrase 1': 3, 'integrity phrase 2': 2 },
        sessionStats: { startTime: new Date(), recentRecordings: [{ phrase: 'test', timestamp: new Date().toISOString() }] },
        sessionRecordingsCount: 5
      };

      const result = referenceNumberService.createSessionReference(originalData);
      const retrievedData = referenceNumberService.getSessionData(result.referenceNumber);

      // Check that all original data is preserved
      if (retrievedData.demographics.ageGroup !== originalData.demographicInfo.ageGroup) {
        addTestResult(testName, false, 'Age group not preserved');
        return;
      }

      if (retrievedData.totalRecordingsInSession !== originalData.sessionRecordingsCount) {
        addTestResult(testName, false, 'Recording count not preserved');
        return;
      }

      if (retrievedData.selectedPhrases.length !== originalData.selectedPhrases.length) {
        addTestResult(testName, false, 'Selected phrases not preserved');
        return;
      }

      // Check that computed fields are correct
      const expectedCompleted = Object.values(originalData.recordingsCount).filter(count => count >= 3).length;
      if (retrievedData.totalPhrasesCompleted !== expectedCompleted) {
        addTestResult(testName, false, 'Computed phrases completed incorrect');
        return;
      }

      addTestResult(
        testName, 
        true, 
        'Data integrity maintained',
        { 
          originalRecordings: originalData.sessionRecordingsCount,
          retrievedRecordings: retrievedData.totalRecordingsInSession,
          originalPhrases: originalData.selectedPhrases.length,
          retrievedPhrases: retrievedData.selectedPhrases.length
        }
      );
    } catch (error) {
      addTestResult(testName, false, `Data integrity test failed: ${error.message}`);
    }
  };

  const getTestSummary = () => {
    const total = testResults.length;
    const passed = testResults.filter(result => result.passed).length;
    const failed = total - passed;
    
    return { total, passed, failed, passRate: total > 0 ? (passed / total * 100).toFixed(1) : 0 };
  };

  const summary = getTestSummary();

  return (
    <Box sx={{ p: 3 }}>
      <Paper elevation={3} sx={{ p: 3, mb: 3 }}>
        <Typography variant="h5" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <BugReportIcon color="primary" />
          Reference Number System Testing
        </Typography>
        
        <Typography variant="body2" color="text.secondary" paragraph>
          Comprehensive testing suite for the reference number system including uniqueness verification,
          data persistence, retrieval functionality, and error handling.
        </Typography>

        <Button
          variant="contained"
          onClick={runComprehensiveTests}
          disabled={isRunning}
          startIcon={<PlayArrowIcon />}
          sx={{ mb: 3 }}
        >
          {isRunning ? 'Running Tests...' : 'Run Comprehensive Tests'}
        </Button>

        {isRunning && (
          <Box sx={{ mb: 3 }}>
            <Typography variant="body2" gutterBottom>
              Running tests... {Math.round(progress)}% complete
            </Typography>
            <LinearProgress variant="determinate" value={progress} />
          </Box>
        )}

        {testResults.length > 0 && (
          <Grid container spacing={2} sx={{ mb: 3 }}>
            <Grid item xs={3}>
              <Card>
                <CardContent sx={{ textAlign: 'center' }}>
                  <Typography variant="h4" color="primary">{summary.total}</Typography>
                  <Typography variant="body2">Total Tests</Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={3}>
              <Card>
                <CardContent sx={{ textAlign: 'center' }}>
                  <Typography variant="h4" color="success.main">{summary.passed}</Typography>
                  <Typography variant="body2">Passed</Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={3}>
              <Card>
                <CardContent sx={{ textAlign: 'center' }}>
                  <Typography variant="h4" color="error.main">{summary.failed}</Typography>
                  <Typography variant="body2">Failed</Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={3}>
              <Card>
                <CardContent sx={{ textAlign: 'center' }}>
                  <Typography variant="h4" color={summary.passRate >= 90 ? 'success.main' : 'warning.main'}>
                    {summary.passRate}%
                  </Typography>
                  <Typography variant="body2">Pass Rate</Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        )}
      </Paper>

      {testResults.length > 0 && (
        <Paper elevation={3} sx={{ p: 3 }}>
          <Typography variant="h6" gutterBottom>Test Results</Typography>
          
          <List>
            {testResults.map((result, index) => (
              <ListItem key={index}>
                <ListItemIcon>
                  {result.passed ? 
                    <CheckCircleIcon color="success" /> : 
                    <ErrorIcon color="error" />
                  }
                </ListItemIcon>
                <ListItemText
                  primary={
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Typography variant="subtitle1">{result.testName}</Typography>
                      <Chip 
                        label={result.passed ? 'PASS' : 'FAIL'} 
                        color={result.passed ? 'success' : 'error'}
                        size="small"
                      />
                    </Box>
                  }
                  secondary={
                    <Box>
                      <Typography variant="body2">{result.message}</Typography>
                      {result.details && (
                        <Accordion sx={{ mt: 1 }}>
                          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                            <Typography variant="caption">View Details</Typography>
                          </AccordionSummary>
                          <AccordionDetails>
                            <pre style={{ fontSize: '0.8rem', overflow: 'auto' }}>
                              {JSON.stringify(result.details, null, 2)}
                            </pre>
                          </AccordionDetails>
                        </Accordion>
                      )}
                    </Box>
                  }
                />
              </ListItem>
            ))}
          </List>
        </Paper>
      )}
    </Box>
  );
};

export default ReferenceNumberTester;
