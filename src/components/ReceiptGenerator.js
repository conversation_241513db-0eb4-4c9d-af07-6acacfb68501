import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Button,
  IconButton,
  Divider,
  CircularProgress,
  Alert
} from '@mui/material';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import receiptService from '../services/receiptService';

/**
 * Component for generating and displaying a receipt for the user's recording session
 * Now includes receipt-video mapping functionality
 */
const ReceiptGenerator = ({ demographicInfo, sessionRecordingsCount, onClose, savedVideos = [] }) => {
  const [copied, setCopied] = useState(false);
  const [receiptMappingStatus, setReceiptMappingStatus] = useState('pending');
  const [receiptNumber, setReceiptNumber] = useState(null);
  const [isGenerating, setIsGenerating] = useState(true);
  const [error, setError] = useState(null);

  // Generate receipt number and create mapping when component mounts
  useEffect(() => {
    const initializeReceipt = async () => {
      try {
        setIsGenerating(true);
        setError(null);

        console.log('🧾 Generating receipt number with AWS S3 integration...');

        // Generate receipt number using the receipt service
        const generatedReceiptNumber = await receiptService.generateReceiptNumber();
        setReceiptNumber(generatedReceiptNumber);

        console.log('✅ Receipt number generated:', generatedReceiptNumber);

        // Create receipt mapping if we have saved videos
        if (savedVideos && savedVideos.length > 0) {
          console.log(`🔄 Creating receipt mapping for ${generatedReceiptNumber} with ${savedVideos.length} videos`);
          setReceiptMappingStatus('creating');

          // Extract video URLs from savedVideos
          const videoUrls = savedVideos.map(video => video.url || video.filePath || video.key).filter(Boolean);

          // Create session info
          const sessionInfo = {
            sessionRecordingsCount,
            timestamp: new Date().toISOString(),
            applicationVersion: '21.6.25'
          };

          const mappingSuccess = await receiptService.createReceiptMapping(
            generatedReceiptNumber,
            videoUrls,
            demographicInfo,
            sessionInfo
          );

          if (mappingSuccess) {
            console.log('✅ Receipt mapping created successfully');
            setReceiptMappingStatus('success');
          } else {
            console.warn('⚠️ Receipt mapping created with fallback storage');
            setReceiptMappingStatus('partial');
          }
        } else {
          console.log('📋 No saved videos provided, skipping receipt mapping');
          setReceiptMappingStatus('skipped');
        }

      } catch (error) {
        console.error('❌ Error initializing receipt:', error);
        setError(error.message);
        setReceiptMappingStatus('failed');

        // Fallback to timestamp-based receipt number
        const fallbackNumber = Date.now().toString().slice(-6);
        setReceiptNumber(fallbackNumber);
        console.log('🔄 Using fallback receipt number:', fallbackNumber);
      } finally {
        setIsGenerating(false);
      }
    };

    initializeReceipt();
  }, [savedVideos, demographicInfo, sessionRecordingsCount]);

  // Handle copy to clipboard
  const handleCopy = () => {
    if (receiptNumber) {
      navigator.clipboard.writeText(receiptNumber);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    }
  };
  
  return (
    <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', py: 3 }}>
      <Paper 
        elevation={3} 
        sx={{ 
          p: 4, 
          maxWidth: 500, 
          width: '100%',
          borderRadius: 2,
          border: '1px solid #e0e0e0'
        }}
        id="receipt-content"
      >
        <Typography variant="h4" align="center" gutterBottom sx={{ color: '#009688', fontWeight: 'bold' }}>
          Thank you for your contribution!
        </Typography>

        <Typography variant="body1" paragraph align="center" sx={{ mb: 4 }}>
          Your recordings have been successfully saved and will help train AI technology to give a voice to those who need it most.
        </Typography>

        <Divider sx={{ my: 3 }} />

        <Box sx={{ textAlign: 'center', my: 4 }}>
          <Typography variant="h6" sx={{ fontWeight: 'bold', color: '#00796b', mb: 2 }}>
            Receipt Number
          </Typography>

          {isGenerating ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', gap: 2, py: 2 }}>
              <CircularProgress size={24} />
              <Typography variant="body1" sx={{ color: '#666' }}>
                Generating receipt number...
              </Typography>
            </Box>
          ) : error ? (
            <Alert severity="warning" sx={{ mb: 2 }}>
              Error generating receipt: {error}
            </Alert>
          ) : (
            <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', gap: 2 }}>
              <Typography variant="h3" sx={{ fontFamily: 'monospace', fontWeight: 'bold', color: '#263238' }}>
                {receiptNumber}
              </Typography>
              <IconButton
                onClick={handleCopy}
                color={copied ? "success" : "primary"}
                size="large"
                sx={{ ml: 1 }}
                disabled={!receiptNumber}
              >
                {copied ? <CheckCircleIcon /> : <ContentCopyIcon />}
              </IconButton>
            </Box>
          )}

          {copied && receiptNumber && (
            <Typography variant="body2" sx={{ color: '#00796b', mt: 1 }}>
              ✅ Receipt number copied to clipboard
            </Typography>
          )}

          {receiptMappingStatus === 'creating' && (
            <Typography variant="body2" sx={{ color: '#666', mt: 1 }}>
              🔄 Creating video mapping...
            </Typography>
          )}

          {receiptMappingStatus === 'partial' && (
            <Typography variant="body2" sx={{ color: '#ff9800', mt: 1 }}>
              ⚠️ Receipt saved with local backup
            </Typography>
          )}

          {receiptMappingStatus === 'success' && savedVideos && savedVideos.length > 0 && (
            <Typography variant="body2" sx={{ color: '#00796b', mt: 1 }}>
              ✅ {savedVideos.length} video(s) mapped to receipt
            </Typography>
          )}
        </Box>

        <Divider sx={{ my: 3 }} />

        <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
          <Button
            variant="contained"
            color="primary"
            size="large"
            onClick={onClose}
            sx={{
              fontWeight: 'bold',
              py: 2,
              px: 6,
              fontSize: '1.1rem'
            }}
          >
            Continue
          </Button>
        </Box>
      </Paper>
    </Box>
  );
};

export default ReceiptGenerator;
