import { useEffect } from 'react';
import s3ProgressService from '../services/s3ProgressService';

/**
 * S3ProgressPreloader Component
 * Preloads S3 progress data in the background to improve performance
 * when users reach the completion page
 */
const S3ProgressPreloader = ({ 
  preloadOnMount = true,
  preloadDelay = 2000, // Delay before starting preload (ms)
  children 
}) => {
  useEffect(() => {
    if (!preloadOnMount) return;

    const preloadData = async () => {
      try {
        console.log('🚀 Preloading S3 progress data in background...');
        
        // Check if we already have valid cached data
        const cacheStatus = s3ProgressService.getCacheStatus();
        
        if (cacheStatus.isValid) {
          console.log('📊 S3 progress data already cached and valid');
          return;
        }

        // Preload data in background
        await s3ProgressService.fetchProgressData();
        console.log('✅ S3 progress data preloaded successfully');
        
      } catch (error) {
        console.warn('⚠️ Failed to preload S3 progress data:', error);
        // Don't throw error - this is background preloading
      }
    };

    // Delay preloading to not interfere with initial page load
    const timeoutId = setTimeout(preloadData, preloadDelay);

    return () => clearTimeout(timeoutId);
  }, [preloadOnMount, preloadDelay]);

  // This component doesn't render anything visible
  return children || null;
};

export default S3ProgressPreloader;
