import React, { useState, useRef, useEffect, useCallback } from 'react';
import { Box, Typography, Button, Paper } from '@mui/material';
import Webcam from 'react-webcam';
import * as faceLandmarksDetection from '@tensorflow-models/face-landmarks-detection';
import * as tf from '@tensorflow/tfjs';

const MouthDetectionTest = () => {
  const webcamRef = useRef(null);
  const canvasRef = useRef(null);
  const detectionRef = useRef(null);
  const [model, setModel] = useState(null);
  const [isDetecting, setIsDetecting] = useState(false);
  const [mouthPosition, setMouthPosition] = useState(null);
  const [showAllLandmarks, setShowAllLandmarks] = useState(false);
  const [detectionStats, setDetectionStats] = useState({
    frameCount: 0,
    detectionCount: 0,
    avgConfidence: 0
  });

  // Initialize MediaPipe FaceMesh model
  useEffect(() => {
    const initModel = async () => {
      try {
        console.log('🎯 Initializing TensorFlow.js...');
        await tf.ready();
        console.log('🎯 TensorFlow.js ready');

        console.log('🎯 Loading MediaPipe FaceMesh model...');
        const model = faceLandmarksDetection.SupportedModels.MediaPipeFaceMesh;
        const detectorConfig = {
          runtime: 'mediapipe',
          solutionPath: 'https://cdn.jsdelivr.net/npm/@mediapipe/face_mesh',
          refineLandmarks: false,
        };

        const loadedModel = await faceLandmarksDetection.createDetector(model, detectorConfig);
        console.log('🎯 MediaPipe FaceMesh model loaded successfully');
        setModel(loadedModel);
      } catch (error) {
        console.error('🎯 Error initializing face detection model:', error);
      }
    };

    initModel();
  }, []);

  // Mouth detection function
  const detectMouth = useCallback(async () => {
    if (!webcamRef.current || !webcamRef.current.video || !model || !canvasRef.current) {
      return;
    }

    const video = webcamRef.current.video;
    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');

    if (video.videoWidth === 0 || video.videoHeight === 0) {
      detectionRef.current = requestAnimationFrame(detectMouth);
      return;
    }

    // Set canvas size to match video
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;

    // Draw video frame
    ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

    try {
      const faces = await model.estimateFaces(video);
      
      setDetectionStats(prev => ({
        ...prev,
        frameCount: prev.frameCount + 1
      }));

      if (faces.length > 0) {
        const face = faces[0];
        const keypoints = face.keypoints;
        
        if (keypoints && keypoints.length > 0) {
          // MediaPipe FaceMesh lip landmark indices (empirically tested)
          // Based on MediaPipe FaceMesh 468 landmarks documentation
          const outerLipIndices = [
            61, 146, 91, 181, 84, 17, 314, 405, 320, 307, 375, 321, 308, 324, 318
          ];
          const innerLipIndices = [
            78, 95, 88, 178, 87, 14, 317, 402, 318, 324, 308
          ];
          const mouthCornerIndices = [61, 291]; // Left and right mouth corners

          const allLipIndices = [...new Set([...outerLipIndices, ...innerLipIndices, ...mouthCornerIndices])];
          console.log('🎯 Testing lip landmark indices:', allLipIndices);
          const lipPoints = keypoints.filter((_, index) => allLipIndices.includes(index));

          if (lipPoints.length > 0) {
            // Calculate mouth bounding box
            const xs = lipPoints.map(p => p.x);
            const ys = lipPoints.map(p => p.y);

            const minX = Math.min(...xs);
            const maxX = Math.max(...xs);
            const minY = Math.min(...ys);
            const maxY = Math.max(...ys);

            const mouthWidth = maxX - minX;
            const mouthHeight = maxY - minY;
            const mouthCenterX = (minX + maxX) / 2;
            const mouthCenterY = (minY + maxY) / 2;

            const confidence = Math.min(1, lipPoints.length / allLipIndices.length);

            // Draw mouth bounding box
            ctx.strokeStyle = 'red';
            ctx.lineWidth = 2;
            ctx.strokeRect(minX, minY, mouthWidth, mouthHeight);

            // Draw mouth center point
            ctx.fillStyle = 'red';
            ctx.beginPath();
            ctx.arc(mouthCenterX, mouthCenterY, 3, 0, 2 * Math.PI);
            ctx.fill();

            // Draw all landmarks with indices for debugging (if enabled)
            if (showAllLandmarks) {
              ctx.fillStyle = 'cyan';
              ctx.font = '8px Arial';
              keypoints.forEach((point, index) => {
                // Draw small dot for each landmark
                ctx.beginPath();
                ctx.arc(point.x, point.y, 1, 0, 2 * Math.PI);
                ctx.fill();

                // Draw index number for landmarks in mouth area (roughly)
                if (point.y > video.videoHeight * 0.4 && point.y < video.videoHeight * 0.8 &&
                    point.x > video.videoWidth * 0.2 && point.x < video.videoWidth * 0.8) {
                  ctx.fillText(index.toString(), point.x + 2, point.y - 2);
                }
              });
            }

            // Draw lip points in different color
            ctx.fillStyle = 'yellow';
            lipPoints.forEach((point, index) => {
              ctx.beginPath();
              ctx.arc(point.x, point.y, 2, 0, 2 * Math.PI);
              ctx.fill();
            });

            // Calculate LipNet crop area (150x75 aspect ratio)
            const targetAspectRatio = 150 / 75; // 2:1
            const currentAspectRatio = mouthWidth / mouthHeight;
            
            let finalCropWidth, finalCropHeight;
            
            if (currentAspectRatio > targetAspectRatio) {
              finalCropHeight = Math.max(mouthHeight * 2.5, 60);
              finalCropWidth = finalCropHeight * targetAspectRatio;
            } else {
              finalCropWidth = Math.max(mouthWidth * 2.0, 120);
              finalCropHeight = finalCropWidth / targetAspectRatio;
            }

            const cropX = Math.max(0, mouthCenterX - finalCropWidth / 2);
            const cropY = Math.max(0, mouthCenterY - finalCropHeight / 2);

            // Draw LipNet crop area
            ctx.strokeStyle = 'lime';
            ctx.lineWidth = 3;
            ctx.strokeRect(cropX, cropY, finalCropWidth, finalCropHeight);

            setMouthPosition({
              center: { x: mouthCenterX, y: mouthCenterY },
              bounds: { minX, maxX, minY, maxY },
              size: { width: mouthWidth, height: mouthHeight },
              cropArea: { x: cropX, y: cropY, width: finalCropWidth, height: finalCropHeight },
              confidence: confidence,
              lipPointsCount: lipPoints.length
            });

            setDetectionStats(prev => ({
              ...prev,
              detectionCount: prev.detectionCount + 1,
              avgConfidence: (prev.avgConfidence * prev.detectionCount + confidence) / (prev.detectionCount + 1)
            }));
          }
        }
      }
    } catch (error) {
      console.error('🎯 Detection error:', error);
    }

    if (isDetecting) {
      detectionRef.current = requestAnimationFrame(detectMouth);
    }
  }, [model, isDetecting]);

  const startDetection = () => {
    setIsDetecting(true);
    setDetectionStats({ frameCount: 0, detectionCount: 0, avgConfidence: 0 });
    detectMouth();
  };

  const stopDetection = () => {
    setIsDetecting(false);
    if (detectionRef.current) {
      cancelAnimationFrame(detectionRef.current);
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        🎯 Mouth Detection Test
      </Typography>
      
      <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
        <Button
          variant="contained"
          onClick={startDetection}
          disabled={!model || isDetecting}
        >
          Start Detection
        </Button>
        <Button
          variant="outlined"
          onClick={stopDetection}
          disabled={!isDetecting}
        >
          Stop Detection
        </Button>
        <Button
          variant={showAllLandmarks ? "contained" : "outlined"}
          onClick={() => setShowAllLandmarks(!showAllLandmarks)}
        >
          {showAllLandmarks ? 'Hide All Landmarks' : 'Show All Landmarks'}
        </Button>
      </Box>

      <Box sx={{ display: 'flex', gap: 3 }}>
        <Box>
          <Typography variant="h6">Camera Feed</Typography>
          <Box sx={{ position: 'relative' }}>
            <Webcam
              ref={webcamRef}
              width={640}
              height={480}
              style={{ border: '1px solid #ccc' }}
            />
            <canvas
              ref={canvasRef}
              style={{
                position: 'absolute',
                top: 0,
                left: 0,
                pointerEvents: 'none'
              }}
            />
          </Box>
        </Box>

        <Paper sx={{ p: 2, minWidth: 300 }}>
          <Typography variant="h6">Detection Stats</Typography>
          <Typography>Model: {model ? '✅ Loaded' : '❌ Loading...'}</Typography>
          <Typography>Status: {isDetecting ? '🔄 Detecting' : '⏸️ Stopped'}</Typography>
          <Typography>Frames: {detectionStats.frameCount}</Typography>
          <Typography>Detections: {detectionStats.detectionCount}</Typography>
          <Typography>Success Rate: {detectionStats.frameCount > 0 ? ((detectionStats.detectionCount / detectionStats.frameCount) * 100).toFixed(1) : 0}%</Typography>
          <Typography>Avg Confidence: {detectionStats.avgConfidence.toFixed(3)}</Typography>
          
          {mouthPosition && (
            <Box sx={{ mt: 2 }}>
              <Typography variant="h6">Current Detection</Typography>
              <Typography>Center: ({mouthPosition.center.x.toFixed(1)}, {mouthPosition.center.y.toFixed(1)})</Typography>
              <Typography>Size: {mouthPosition.size.width.toFixed(1)} × {mouthPosition.size.height.toFixed(1)}</Typography>
              <Typography>Crop: {mouthPosition.cropArea.width.toFixed(1)} × {mouthPosition.cropArea.height.toFixed(1)}</Typography>
              <Typography>Confidence: {mouthPosition.confidence.toFixed(3)}</Typography>
              <Typography>Lip Points: {mouthPosition.lipPointsCount}</Typography>
            </Box>
          )}
        </Paper>
      </Box>
    </Box>
  );
};

export default MouthDetectionTest;
