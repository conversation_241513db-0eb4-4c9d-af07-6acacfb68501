import React from 'react';
import { Box, Typography, Paper, Grid, LinearProgress, Tooltip } from '@mui/material';
import MedicalServicesIcon from '@mui/icons-material/MedicalServices';
import AirIcon from '@mui/icons-material/Air';
import FitnessCenterIcon from '@mui/icons-material/FitnessCenter';
import SentimentDissatisfiedIcon from '@mui/icons-material/SentimentDissatisfied';
import { useCategoryProgress } from '../hooks/useProgressTracking';

// Helper function to get icon for each category
const getCategoryIcon = (category) => {
  switch(category) {
    case 'Physical Discomfort':
      return <SentimentDissatisfiedIcon fontSize="large" sx={{ color: '#009688' }} />;
    case 'Positioning, Mobility & Assistance':
      return <FitnessCenterIcon fontSize="large" sx={{ color: '#009688' }} />;
    case 'Communication Assistance':
      return <AirIcon fontSize="large" sx={{ color: '#009688' }} />;
    case 'Nutrition & Hydration':
    case 'Environmental Controls & Comfort':
    case 'Family & Social Connection':
    case 'General Conversation & Social Engagement':
    case 'Person-Centred Orientation':
    case 'Memory, Thinking & Clarifying':
    case 'Technology & Belongings':
    case 'Emotional & Mental Support':
    case 'Procedural & Planning Information':
    case 'Courtesy & Gratitude':
    case 'Question Words':
    case 'Numbers':
    case 'Requests':
      return <MedicalServicesIcon fontSize="large" sx={{ color: '#009688' }} />;
    default:
      return <MedicalServicesIcon fontSize="large" sx={{ color: '#009688' }} />;
  }
};

// Category Card Component with Progress
const CategoryCard = ({ category, onClick }) => {
  const { progress, percentage, completed, total } = useCategoryProgress(category);

  return (
    <Paper
      className="category-card"
      elevation={2}
      sx={{
        p: 2,
        cursor: 'pointer',
        transition: 'all 0.2s ease-in-out',
        '&:hover': {
          elevation: 4,
          transform: 'translateY(-2px)'
        }
      }}
      onClick={() => onClick(category)}
    >
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
        {getCategoryIcon(category)}
        <Typography variant="subtitle1" sx={{ ml: 1, flex: 1 }}>
          {category}
        </Typography>
      </Box>

      {progress && (
        <Box sx={{ mt: 1 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 0.5 }}>
            <Typography variant="caption" color="text.secondary">
              Progress
            </Typography>
            <Typography variant="caption" color="text.secondary">
              {completed}/{total} phrases
            </Typography>
          </Box>
          <Tooltip title={`${percentage}% complete - ${completed} out of ${total} phrases completed`}>
            <LinearProgress
              variant="determinate"
              value={percentage}
              sx={{
                height: 6,
                borderRadius: 3,
                backgroundColor: 'rgba(0, 150, 136, 0.1)',
                '& .MuiLinearProgress-bar': {
                  backgroundColor: percentage === 100 ? '#4caf50' : '#009688',
                  borderRadius: 3
                }
              }}
            />
          </Tooltip>
        </Box>
      )}
    </Paper>
  );
};

const CategorySelector = ({ categories, selectedCategory, onCategoryChange }) => {
  const selectedCategoryProgress = useCategoryProgress(selectedCategory);

  return (
    <Box sx={{ mb: 2 }}>
      {selectedCategory ? (
        <Box sx={{ mb: 1 }}>
          <Paper
            elevation={2}
            sx={{
              p: 2,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              background: 'linear-gradient(90deg, rgba(0,150,136,0.1) 0%, rgba(38,166,154,0.05) 100%)',
              borderLeft: '4px solid #009688'
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', flex: 1 }}>
              {getCategoryIcon(selectedCategory)}
              <Box sx={{ ml: 1, flex: 1 }}>
                <Typography variant="subtitle1">
                  {selectedCategory}
                </Typography>
                {selectedCategoryProgress.progress && (
                  <Typography variant="caption" color="text.secondary">
                    {selectedCategoryProgress.completed}/{selectedCategoryProgress.total} phrases completed ({selectedCategoryProgress.percentage}%)
                  </Typography>
                )}
              </Box>
            </Box>

            <Typography
              variant="body2"
              color="primary"
              sx={{ cursor: 'pointer', textDecoration: 'underline' }}
              onClick={() => onCategoryChange('')}
            >
              Change
            </Typography>
          </Paper>
        </Box>
      ) : (
        <Grid container spacing={2}>
          {categories.map((category) => (
            <Grid item xs={12} sm={6} md={4} key={category}>
              <CategoryCard category={category} onClick={onCategoryChange} />
            </Grid>
          ))}
        </Grid>
      )}
    </Box>
  );
};

export default CategorySelector;
