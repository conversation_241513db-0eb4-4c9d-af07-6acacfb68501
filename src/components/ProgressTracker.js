import React from 'react';
import { Box, LinearProgress, Typography, Grid, Paper } from '@mui/material';
import { phraseCollectionConfig } from '../phrases';

const ProgressTracker = ({ recordingsCount, currentCategory, currentPhraseIndex, totalPhrases, recordingsPerPhrase, categoryName }) => {
  // Add safety checks for undefined or null values
  const safeRecordingsCount = recordingsCount || {};
  const safeCurrentCategory = currentCategory || '';
  const safeTotalPhrases = totalPhrases || 0;
  const safeRecordingsPerPhrase = recordingsPerPhrase || phraseCollectionConfig.recordingsPerPhrase;
  const safeCategoryName = categoryName || 'Category';
  
  // Calculate overall progress
  const totalRecordingsNeeded = 30; // Fixed at 30 recordings total as requested
  let totalRecordingsMade = 0;
  
  // Count total recordings made across all categories
  Object.keys(safeRecordingsCount).forEach(key => {
    totalRecordingsMade += Math.min(safeRecordingsCount[key], safeRecordingsPerPhrase);
  });
  
  // Cap at 30 recordings
  totalRecordingsMade = Math.min(totalRecordingsMade, totalRecordingsNeeded);
  
  const overallProgressPercentage = (totalRecordingsMade / totalRecordingsNeeded) * 100;
  
  return (
    <Box sx={{ mt: 4, mb: 2 }}>
      <Paper elevation={2} sx={{ p: 2, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          Recording Progress
        </Typography>
        
        <Grid container spacing={2}>
          <Grid item xs={12}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
              <Typography variant="body2" color="textSecondary">
                Overall Progress
              </Typography>
              <Typography variant="body2" color="textSecondary">
                {totalRecordingsMade} of {totalRecordingsNeeded} recordings completed
              </Typography>
            </Box>
            <LinearProgress 
              variant="determinate" 
              value={overallProgressPercentage} 
              sx={{ height: 10, borderRadius: 5 }}
            />
          </Grid>
        </Grid>
      </Paper>
    </Box>
  );
};

export default ProgressTracker;
