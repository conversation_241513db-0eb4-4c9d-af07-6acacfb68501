import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Button,
  Paper,
  Alert,
  CircularProgress,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Chip,
  Divider
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import ErrorIcon from '@mui/icons-material/Error';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import { testAWSConnection, testS3Upload } from '../services/awsStorage';

const S3TestPanel = () => {
  const [connectionTest, setConnectionTest] = useState({ status: 'idle', result: null });
  const [uploadTest, setUploadTest] = useState({ status: 'idle', result: null });
  const [autoTestCompleted, setAutoTestCompleted] = useState(false);

  // Auto-run connection test on mount
  useEffect(() => {
    if (!autoTestCompleted) {
      runConnectionTest();
      setAutoTestCompleted(true);
    }
  }, [autoTestCompleted]);

  const runConnectionTest = async () => {
    setConnectionTest({ status: 'running', result: null });
    console.log('🧪 S3TestPanel: Running connection test...');
    
    try {
      const result = await testAWSConnection();
      setConnectionTest({ status: 'completed', result });
      console.log('🧪 S3TestPanel: Connection test completed:', result);
    } catch (error) {
      console.error('🧪 S3TestPanel: Connection test error:', error);
      setConnectionTest({ 
        status: 'error', 
        result: { success: false, error: error.message } 
      });
    }
  };

  const runUploadTest = async () => {
    setUploadTest({ status: 'running', result: null });
    console.log('🧪 S3TestPanel: Running upload test...');
    
    try {
      const result = await testS3Upload();
      setUploadTest({ status: 'completed', result });
      console.log('🧪 S3TestPanel: Upload test completed:', result);
    } catch (error) {
      console.error('🧪 S3TestPanel: Upload test error:', error);
      setUploadTest({ 
        status: 'error', 
        result: { success: false, error: error.message } 
      });
    }
  };

  const getStatusIcon = (status, result) => {
    if (status === 'running') return <CircularProgress size={20} />;
    if (status === 'completed' && result?.success) return <CheckCircleIcon color="success" />;
    if (status === 'error' || (status === 'completed' && !result?.success)) return <ErrorIcon color="error" />;
    return null;
  };

  const getStatusColor = (status, result) => {
    if (status === 'running') return 'info';
    if (status === 'completed' && result?.success) return 'success';
    if (status === 'error' || (status === 'completed' && !result?.success)) return 'error';
    return 'default';
  };

  return (
    <Box sx={{ p: 3, maxWidth: 800, mx: 'auto' }}>
      <Typography variant="h4" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        <CloudUploadIcon />
        S3 Upload Testing Panel
      </Typography>
      
      <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
        Test AWS S3 connectivity and upload functionality for the ICU Dataset Application
      </Typography>

      {/* AWS Configuration Display */}
      <Paper sx={{ p: 2, mb: 3, bgcolor: 'grey.50' }}>
        <Typography variant="h6" gutterBottom>AWS Configuration</Typography>
        <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 2fr', gap: 1, fontSize: '0.875rem' }}>
          <Typography><strong>Region:</strong></Typography>
          <Typography>{process.env.REACT_APP_AWS_REGION || 'Not set'}</Typography>
          
          <Typography><strong>S3 Bucket:</strong></Typography>
          <Typography>{process.env.REACT_APP_S3_BUCKET || 'Not set'}</Typography>
          
          <Typography><strong>Identity Pool:</strong></Typography>
          <Typography sx={{ wordBreak: 'break-all' }}>
            {process.env.REACT_APP_AWS_IDENTITY_POOL_ID || 'Not set'}
          </Typography>
        </Box>
      </Paper>

      {/* Connection Test */}
      <Accordion defaultExpanded>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, width: '100%' }}>
            <Typography variant="h6">1. Connection Test</Typography>
            {getStatusIcon(connectionTest.status, connectionTest.result)}
            <Chip 
              label={connectionTest.status === 'idle' ? 'Ready' : 
                     connectionTest.status === 'running' ? 'Testing...' :
                     connectionTest.result?.success ? 'Success' : 'Failed'}
              color={getStatusColor(connectionTest.status, connectionTest.result)}
              size="small"
            />
          </Box>
        </AccordionSummary>
        <AccordionDetails>
          <Box sx={{ mb: 2 }}>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              Tests AWS credentials and S3 bucket access permissions
            </Typography>
            
            <Button 
              variant="outlined" 
              onClick={runConnectionTest}
              disabled={connectionTest.status === 'running'}
              sx={{ mb: 2 }}
            >
              {connectionTest.status === 'running' ? 'Testing...' : 'Run Connection Test'}
            </Button>

            {connectionTest.result && (
              <Alert 
                severity={connectionTest.result.success ? 'success' : 'error'}
                sx={{ mb: 2 }}
              >
                {connectionTest.result.success ? (
                  <Box>
                    <Typography variant="subtitle2">✅ Connection Successful!</Typography>
                    <Typography variant="body2">
                      Bucket: {connectionTest.result.bucket} | 
                      Region: {connectionTest.result.region} | 
                      Objects: {connectionTest.result.objectCount}
                    </Typography>
                  </Box>
                ) : (
                  <Box>
                    <Typography variant="subtitle2">❌ Connection Failed</Typography>
                    <Typography variant="body2">
                      Error: {connectionTest.result.error}
                    </Typography>
                    {connectionTest.result.errorCode && (
                      <Typography variant="body2">
                        Code: {connectionTest.result.errorCode}
                      </Typography>
                    )}
                  </Box>
                )}
              </Alert>
            )}
          </Box>
        </AccordionDetails>
      </Accordion>

      {/* Upload Test */}
      <Accordion>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, width: '100%' }}>
            <Typography variant="h6">2. Upload Test</Typography>
            {getStatusIcon(uploadTest.status, uploadTest.result)}
            <Chip 
              label={uploadTest.status === 'idle' ? 'Ready' : 
                     uploadTest.status === 'running' ? 'Uploading...' :
                     uploadTest.result?.success ? 'Success' : 'Failed'}
              color={getStatusColor(uploadTest.status, uploadTest.result)}
              size="small"
            />
          </Box>
        </AccordionSummary>
        <AccordionDetails>
          <Box sx={{ mb: 2 }}>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              Tests actual file upload to S3 bucket with metadata
            </Typography>
            
            <Button 
              variant="contained" 
              onClick={runUploadTest}
              disabled={uploadTest.status === 'running' || !connectionTest.result?.success}
              sx={{ mb: 2 }}
            >
              {uploadTest.status === 'running' ? 'Uploading...' : 'Run Upload Test'}
            </Button>

            {!connectionTest.result?.success && (
              <Alert severity="warning" sx={{ mb: 2 }}>
                Please run and pass the connection test first
              </Alert>
            )}

            {uploadTest.result && (
              <Alert 
                severity={uploadTest.result.success ? 'success' : 'error'}
                sx={{ mb: 2 }}
              >
                {uploadTest.result.success ? (
                  <Box>
                    <Typography variant="subtitle2">✅ Upload Successful!</Typography>
                    <Typography variant="body2">
                      File URL: <a href={uploadTest.result.fileUrl} target="_blank" rel="noopener noreferrer">
                        {uploadTest.result.fileUrl}
                      </a>
                    </Typography>
                  </Box>
                ) : (
                  <Box>
                    <Typography variant="subtitle2">❌ Upload Failed</Typography>
                    <Typography variant="body2">
                      Error: {uploadTest.result.error}
                    </Typography>
                    {uploadTest.result.errorCode && (
                      <Typography variant="body2">
                        Code: {uploadTest.result.errorCode}
                      </Typography>
                    )}
                  </Box>
                )}
              </Alert>
            )}
          </Box>
        </AccordionDetails>
      </Accordion>

      <Divider sx={{ my: 3 }} />

      {/* Instructions */}
      <Paper sx={{ p: 2, bgcolor: 'info.light', color: 'info.contrastText' }}>
        <Typography variant="h6" gutterBottom>Next Steps</Typography>
        <Typography variant="body2">
          1. ✅ Run connection test to verify AWS credentials and bucket access<br/>
          2. ✅ Run upload test to confirm file upload functionality<br/>
          3. 🎥 If both tests pass, proceed to test video recording uploads<br/>
          4. 🔍 Check browser console for detailed logging during tests
        </Typography>
      </Paper>
    </Box>
  );
};

export default S3TestPanel;
