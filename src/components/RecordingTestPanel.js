import React, { useState, useEffect } from 'react';
import { Box, Typography, Button, Paper, Alert } from '@mui/material';

const RecordingTestPanel = ({ recordingNumber, onRecordingComplete }) => {
  const [localCount, setLocalCount] = useState(0);
  const [testResults, setTestResults] = useState([]);

  // Sync with parent recordingNumber
  useEffect(() => {
    console.log('🧪 RecordingTestPanel: Syncing with parent recordingNumber:', recordingNumber);
    setLocalCount(recordingNumber || 0);
  }, [recordingNumber]);

  // Track localCount changes
  useEffect(() => {
    console.log('🧪 RecordingTestPanel: localCount changed to:', localCount);
    setTestResults(prev => [...prev, `localCount updated to: ${localCount}`]);
  }, [localCount]);

  const simulateRecording = () => {
    console.log('🧪 RecordingTestPanel: Simulating recording...');
    
    const mockSavedData = {
      id: `test-recording-${Date.now()}`,
      url: 'https://test-url.com/video.mp4',
      key: 'test/video.mp4',
      filename: 'test-video.mp4',
      phrase: 'test phrase',
      category: 'test category',
      timestamp: new Date().toISOString()
    };

    const mockMetadata = {
      phrase: 'test phrase',
      category: 'test category',
      recordingNumber: localCount + 1
    };

    const mockQualityCheck = {
      metrics: { overall: 'good' }
    };

    console.log('🧪 RecordingTestPanel: Calling onRecordingComplete with:', {
      mockSavedData,
      mockMetadata,
      mockQualityCheck
    });

    if (onRecordingComplete) {
      onRecordingComplete(mockSavedData, mockMetadata, mockQualityCheck);
    } else {
      console.error('🧪 RecordingTestPanel: onRecordingComplete is not provided!');
    }
  };

  return (
    <Paper sx={{ p: 3, mb: 3, bgcolor: 'info.light', color: 'info.contrastText' }}>
      <Typography variant="h6" gutterBottom>
        🧪 Recording Test Panel
      </Typography>
      
      <Box sx={{ mb: 2 }}>
        <Typography variant="body2">
          <strong>Parent recordingNumber:</strong> {recordingNumber}
        </Typography>
        <Typography variant="body2">
          <strong>Local count:</strong> {localCount}
        </Typography>
        <Typography variant="body2">
          <strong>Progress:</strong> {localCount}/3
        </Typography>
      </Box>

      <Box sx={{ mb: 2, display: 'flex', gap: 1 }}>
        {[1, 2, 3].map((index) => (
          <Box
            key={index}
            sx={{
              width: 20,
              height: 20,
              borderRadius: '50%',
              bgcolor: localCount >= index ? 'success.main' : 'grey.400',
              border: '2px solid',
              borderColor: localCount >= index ? 'success.main' : 'grey.400',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: 'white',
              fontSize: '0.7rem',
              fontWeight: 'bold'
            }}
          >
            {localCount >= index ? '✓' : index}
          </Box>
        ))}
      </Box>

      <Button
        variant="contained"
        color="primary"
        onClick={simulateRecording}
        sx={{ mb: 2 }}
      >
        Simulate Recording
      </Button>

      <Box sx={{ maxHeight: 200, overflow: 'auto' }}>
        <Typography variant="subtitle2" gutterBottom>
          Test Results:
        </Typography>
        {testResults.map((result, index) => (
          <Typography key={index} variant="caption" display="block">
            {index + 1}. {result}
          </Typography>
        ))}
      </Box>

      {localCount >= 3 && (
        <Alert severity="success" sx={{ mt: 2 }}>
          ✅ All 3 recordings completed! Auto-navigation should trigger.
        </Alert>
      )}
    </Paper>
  );
};

export default RecordingTestPanel;
