import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Grid,
  Card,
  CardContent,
  LinearProgress,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Avatar,
  Chip,
  IconButton,
  Tooltip,
  Tab,
  Tabs
} from '@mui/material';
import {
  Person as PersonIcon,
  RecordVoiceOver as RecordIcon,
  Assessment as AssessmentIcon,
  CalendarToday as CalendarIcon,
  AccessTime as TimeIcon,
  Star as StarIcon,
  StarBorder as StarBorderIcon
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';

// Styled components
const StatsCard = styled(Card)(({ theme }) => ({
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
  '& .MuiCardContent-root': {
    flexGrow: 1,
  },
  background: 'linear-gradient(135deg, rgba(0,150,136,0.05) 0%, rgba(0,150,136,0.1) 100%)',
  border: '1px solid rgba(0,150,136,0.2)',
}));

const TabPanel = ({ children, value, index, ...other }) => (
  <div
    role="tabpanel"
    hidden={value !== index}
    id={`profile-tabpanel-${index}`}
    aria-labelledby={`profile-tab-${index}`}
    {...other}
  >
    {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
  </div>
);

const UserProfile = ({ demographicInfo, recordingsCount, phrases, sessionStats }) => {
  const [tabValue, setTabValue] = useState(0);
  const [stats, setStats] = useState({
    totalRecordings: 0,
    completedPhrases: 0,
    totalPhrases: 0,
    categories: {},
    recentActivity: [],
    achievements: []
  });
  
  // Calculate statistics
  useEffect(() => {
    const calculateStats = () => {
      // Count total recordings and completed phrases
      let totalRecs = 0;
      let completed = 0;
      const categoryStats = {};
      
      Object.entries(recordingsCount).forEach(([category, phraseRecordings]) => {
        if (!categoryStats[category]) {
          categoryStats[category] = {
            total: 0,
            completed: 0,
            recordings: 0
          };
        }
        
        Object.entries(phraseRecordings).forEach(([phrase, count]) => {
          totalRecs += count;
          categoryStats[category].recordings += count;
          categoryStats[category].total++;
          if (count === 3) {
            completed++;
            categoryStats[category].completed++;
          }
        });
      });
      
      // Generate achievements
      const achievements = [];
      if (totalRecs >= 10) achievements.push({ title: 'Getting Started', description: 'Recorded 10+ phrases' });
      if (completed >= 5) achievements.push({ title: 'Completionist', description: 'Completed 5+ phrases' });
      if (Object.keys(categoryStats).length >= 3) achievements.push({ title: 'Diverse Speaker', description: 'Recorded in 3+ categories' });
      
      setStats({
        totalRecordings: totalRecs,
        completedPhrases: completed,
        totalPhrases: Object.keys(phrases || {}).length,
        categories: categoryStats,
        recentActivity: sessionStats?.recentRecordings || [],
        achievements
      });
    };
    
    calculateStats();
  }, [recordingsCount, phrases, sessionStats]);
  
  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };
  
  return (
    <Box sx={{ maxWidth: 1200, mx: 'auto', p: 3 }}>
      {/* Profile Header */}
      <Paper sx={{ p: 3, mb: 3, borderRadius: 2 }}>
        <Grid container spacing={3} alignItems="center">
          <Grid item>
            <Avatar
              sx={{
                width: 80,
                height: 80,
                bgcolor: '#009688',
                fontSize: '2rem'
              }}
            >
              {demographicInfo?.userId?.charAt(0)?.toUpperCase() || 'U'}
            </Avatar>
          </Grid>
          <Grid item xs>
            <Typography variant="h5" gutterBottom>
              User Profile
            </Typography>
            <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
              <Chip
                icon={<PersonIcon />}
                label={`Age: ${demographicInfo?.age || 'Not specified'}`}
                variant="outlined"
              />
              <Chip
                icon={<RecordIcon />}
                label={`${stats.totalRecordings} Recordings`}
                variant="outlined"
              />
              <Chip
                icon={<AssessmentIcon />}
                label={`${stats.completedPhrases}/${stats.totalPhrases} Phrases Completed`}
                variant="outlined"
              />
            </Box>
          </Grid>
        </Grid>
      </Paper>
      
      {/* Tabs Navigation */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}>
        <Tabs value={tabValue} onChange={handleTabChange} aria-label="profile tabs">
          <Tab label="Overview" />
          <Tab label="Statistics" />
          <Tab label="Achievements" />
        </Tabs>
      </Box>
      
      {/* Overview Tab */}
      <TabPanel value={tabValue} index={0}>
        <Grid container spacing={3}>
          {/* Quick Stats */}
          <Grid item xs={12} md={4}>
            <StatsCard>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Recording Progress
                </Typography>
                <Box sx={{ mt: 2 }}>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    Total Progress
                  </Typography>
                  <LinearProgress
                    variant="determinate"
                    value={(stats.completedPhrases / stats.totalPhrases) * 100}
                    sx={{ height: 8, borderRadius: 4 }}
                  />
                  <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
                    {Math.round((stats.completedPhrases / stats.totalPhrases) * 100)}% Complete
                  </Typography>
                </Box>
              </CardContent>
            </StatsCard>
          </Grid>
          
          {/* Recent Activity */}
          <Grid item xs={12} md={8}>
            <StatsCard>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Recent Activity
                </Typography>
                <List>
                  {stats.recentActivity.slice(0, 5).map((activity, index) => (
                    <ListItem key={index}>
                      <ListItemIcon>
                        <TimeIcon />
                      </ListItemIcon>
                      <ListItemText
                        primary={activity.phrase}
                        secondary={`${activity.category} - Recording ${activity.recordingNumber}/3`}
                      />
                    </ListItem>
                  ))}
                </List>
              </CardContent>
            </StatsCard>
          </Grid>
        </Grid>
      </TabPanel>
      
      {/* Statistics Tab */}
      <TabPanel value={tabValue} index={1}>
        <Grid container spacing={3}>
          {Object.entries(stats.categories).map(([category, catStats]) => (
            <Grid item xs={12} md={6} key={category}>
              <StatsCard>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    {category}
                  </Typography>
                  <Box sx={{ mt: 2 }}>
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      Completion Rate
                    </Typography>
                    <LinearProgress
                      variant="determinate"
                      value={(catStats.completed / catStats.total) * 100}
                      sx={{ height: 8, borderRadius: 4 }}
                    />
                    <Box sx={{ mt: 2 }}>
                      <Typography variant="body2" color="text.secondary">
                        {catStats.completed}/{catStats.total} Phrases Completed
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {catStats.recordings} Total Recordings
                      </Typography>
                    </Box>
                  </Box>
                </CardContent>
              </StatsCard>
            </Grid>
          ))}
        </Grid>
      </TabPanel>
      
      {/* Achievements Tab */}
      <TabPanel value={tabValue} index={2}>
        <Grid container spacing={3}>
          {stats.achievements.map((achievement, index) => (
            <Grid item xs={12} sm={6} md={4} key={index}>
              <StatsCard>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <StarIcon sx={{ color: '#ffc107', mr: 1 }} />
                    <Typography variant="h6">
                      {achievement.title}
                    </Typography>
                  </Box>
                  <Typography variant="body2" color="text.secondary">
                    {achievement.description}
                  </Typography>
                </CardContent>
              </StatsCard>
            </Grid>
          ))}
          {/* Locked Achievements */}
          <Grid item xs={12} sm={6} md={4}>
            <StatsCard sx={{ opacity: 0.6 }}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <StarBorderIcon sx={{ color: '#bdbdbd', mr: 1 }} />
                  <Typography variant="h6">
                    Master Speaker
                  </Typography>
                </Box>
                <Typography variant="body2" color="text.secondary">
                  Complete 50+ phrases
                </Typography>
              </CardContent>
            </StatsCard>
          </Grid>
        </Grid>
      </TabPanel>
    </Box>
  );
};

export default UserProfile;
