import React from 'react';
import { 
  Box, 
  Paper, 
  Typography, 
  LinearProgress, 
  <PERSON><PERSON>,
  Card,
  CardContent,
  Grid,
  Divider,
  Chip
} from '@mui/material';
import ShareIcon from '@mui/icons-material/Share';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import PeopleIcon from '@mui/icons-material/People';
import AutorenewIcon from '@mui/icons-material/Autorenew';
import ReceiptIcon from '@mui/icons-material/Receipt';
import { 
  getCompletionPercentage, 
  getTotalRecordings,
  getMaxPhrasesPerVolunteer,
  phraseCollectionConfig
} from '../services/phraseRotationService';

const CollectionTracker = ({ recordingsCount, totalGoal, sessionCount = 0, onGetReceipt }) => {
  // Add safety checks for undefined or null values
  const safeRecordingsCount = recordingsCount || {};
  const safeTotalGoal = typeof totalGoal === 'number' ? totalGoal : 0;
  const safeSessionCount = typeof sessionCount === 'number' ? sessionCount : 0;
  // Get recordings config
  const recordingsPerPhrase = phraseCollectionConfig.recordingsPerPhrase;
  const maxPhrasesPerVolunteer = getMaxPhrasesPerVolunteer();
  
  // Calculate total recordings collected
  const totalRecordings = getTotalRecordings(safeRecordingsCount);
  
  // Calculate percentage complete
  const percentComplete = getCompletionPercentage(safeRecordingsCount);
  
  // Calculate recordings needed to reach goal
  const recordingsNeeded = Math.max(safeTotalGoal - totalRecordings, 0);
  
  // Generate share text
  const shareText = `I just contributed ${totalRecordings} recordings to help train an AI lip-reading app for ICU patients! Join me: [LINK]`;
  
  // Ensure all values are valid for rendering
  const safePercentComplete = isNaN(percentComplete) ? 0 : percentComplete;
  
  // Handle share button click
  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: 'Help ICU Patients Communicate',
        text: shareText,
        url: window.location.href,
      })
      .catch((error) => console.log('Error sharing', error));
    } else {
      // Fallback - copy to clipboard
      navigator.clipboard.writeText(shareText)
        .then(() => {
          alert('Share text copied to clipboard!');
        })
        .catch(err => {
          console.error('Could not copy text: ', err);
        });
    }
  };
  
  return (
    <Paper elevation={3} sx={{ p: 3, mb: 4, mt: 2 }}>
      <Box sx={{ textAlign: 'center', mb: 3 }}>
        <CheckCircleIcon color="success" sx={{ fontSize: 48, mb: 1 }} />
        <Typography variant="h5" gutterBottom>
          Thank You For Your Contributions!
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Your recordings will help ICU patients communicate when they cannot speak.
        </Typography>
        
        {safeSessionCount > 0 && (
          <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>
            <Chip 
              icon={<AutorenewIcon />} 
              label={`${safeSessionCount} recordings in this session`} 
              color="primary" 
              variant="outlined" 
              sx={{ px: 1 }}
            />
          </Box>
        )}
      </Box>
      
      <Divider sx={{ my: 2 }} />
      
      <Box sx={{ mb: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
          <Typography variant="body1" fontWeight="medium">
            Overall Progress
          </Typography>
          <Typography variant="body1" fontWeight="medium">
            {safePercentComplete}%
          </Typography>
        </Box>
        <LinearProgress 
          variant="determinate" 
          value={safePercentComplete} 
          sx={{ 
            height: 10, 
            borderRadius: 5,
            '& .MuiLinearProgress-bar': {
              backgroundColor: safePercentComplete >= 100 ? '#4caf50' : '#2196f3'
            }
          }} 
        />
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 1 }}>
          <Typography variant="body2" color="text.secondary">
            {totalRecordings} recordings collected
          </Typography>
          <Typography variant="body2" color="text.secondary">
            {safePercentComplete >= 100 ? 'Complete!' : `${recordingsNeeded} more needed`}
          </Typography>
        </Box>
      </Box>
      
      <Box sx={{ mb: 3, p: 2, bgcolor: 'rgba(25, 118, 210, 0.08)', borderRadius: 2 }}>
        <Typography variant="subtitle1" fontWeight="medium" gutterBottom>
          Phrase Set Rotation
        </Typography>
        <Typography variant="body2" color="text.secondary">
          We're collecting {recordingsPerPhrase} recordings for each phrase.
          Once we have enough recordings, we'll automatically rotate to a new set of phrases.
        </Typography>
      </Box>
      
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={4}>
          <Card variant="outlined" sx={{ height: '100%' }}>
            <CardContent sx={{ textAlign: 'center' }}>
              <Typography color="text.secondary" gutterBottom>
                Total Recordings
              </Typography>
              <Typography variant="h4" component="div">
                {totalRecordings}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={4}>
          <Card variant="outlined" sx={{ height: '100%' }}>
            <CardContent sx={{ textAlign: 'center' }}>
              <Typography color="text.secondary" gutterBottom>
                Recordings Needed
              </Typography>
              <Typography variant="h4" component="div">
                {recordingsNeeded}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={4}>
          <Card variant="outlined" sx={{ height: '100%' }}>
            <CardContent sx={{ textAlign: 'center' }}>
              <Typography color="text.secondary" gutterBottom>
                Max Phrases
              </Typography>
              <Typography variant="h4" component="div">
                {maxPhrasesPerVolunteer}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
      
      <Box sx={{ textAlign: 'center', mt: 4 }}>
        <Typography variant="h6" gutterBottom>
          Help Us Reach Our Goal!
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          Share this project with friends and colleagues to help us collect more recordings.
        </Typography>
        <Box sx={{ display: 'flex', flexDirection: { xs: 'column', sm: 'row' }, gap: 2, justifyContent: 'center' }}>
          <Button
            variant="contained"
            color="primary"
            startIcon={<ShareIcon />}
            onClick={handleShare}
            sx={{ px: 4, py: 1.5, borderRadius: '30px' }}
          >
            Share This Project
          </Button>
          
          {onGetReceipt && (
            <Button
              variant="outlined"
              color="secondary"
              startIcon={<ReceiptIcon />}
              onClick={onGetReceipt}
              sx={{ px: 4, py: 1.5, borderRadius: '30px' }}
            >
              Get Recording Receipt
            </Button>
          )}
        </Box>
      </Box>
    </Paper>
  );
};

export default CollectionTracker;
