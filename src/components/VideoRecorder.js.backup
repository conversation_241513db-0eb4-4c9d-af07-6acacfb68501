import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Typography,
  Button,
  Slider,
  CircularProgress,
  Alert,
  Grid,
  Paper,
  Tooltip,
  IconButton,
  Snackbar,
  LinearProgress
} from '@mui/material';
import FiberManualRecordIcon from '@mui/icons-material/FiberManualRecord';
import InfoIcon from '@mui/icons-material/Info';

import videoStore from '../services/videoStorage';
import Webcam from 'react-webcam';
import WebcamPermissionHandler from './WebcamPermissionHandler';
import * as faceLandmarksDetection from '@tensorflow-models/face-landmarks-detection';
import * as tf from '@tensorflow/tfjs';
import { createMetadata, updateMetadataManifest, getDeviceInfo, updateCameraResolution } from '../services/metadataService';
import { createLipNetVideo, extractVideoThumbnail } from '../services/videoProcessor';

const VideoRecorder = ({ 
  onRecordingComplete, 
  disabled = false, 
  category, 
  phrase, 
  recordingNumber, 
  demographics,
  onNavigateNext // Add callback for navigation
}) => {
  // Video settings for LipNet compatibility
  const VIDEO_CONSTRAINTS = {
    width: 640,
    height: 480,
    frameRate: 30,
    facingMode: 'user'
  };
  
  // LipNet requires specific ROI dimensions for mouth region
  const MOUTH_ROI = {
    width: 100,
    height: 50
  };
  
  // LipNet video processing options
  const LIPNET_OPTIONS = {
    targetWidth: 100,
    targetHeight: 50,
    frameRate: 25,  // LipNet standard frame rate
    grayscale: true // LipNet uses grayscale preprocessing
  };
  
  // State variables
  const [permissionGranted, setPermissionGranted] = useState(false);
  const [cameraError, setCameraError] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [isRecording, setIsRecording] = useState(false);
  const [recordedChunks, setRecordedChunks] = useState([]);
  const [processing, setProcessing] = useState(false);
  const [selectedCamera, setSelectedCamera] = useState('');
  const [availableCameras, setAvailableCameras] = useState([]);
  const [zoomLevel, setZoomLevel] = useState(1);
  const [canReRecord, setCanReRecord] = useState(false);
  const [recordingCount, setRecordingCount] = useState(0);
  const [showSavedNotification, setShowSavedNotification] = useState(false);
  const [faceDetected, setFaceDetected] = useState(false);
  const [mouthPosition, setMouthPosition] = useState(null);
  const [model, setModel] = useState(null);
  const [mouthTrackingQuality, setMouthTrackingQuality] = useState(0);
  const [qualityMetrics, setQualityMetrics] = useState({
    brightness: 0,
    sharpness: 0,
    faceConfidence: 0,
    overall: 'poor'
  });
  
  // Refs
  const webcamRef = useRef(null);
  const mediaRecorderRef = useRef(null);
  const canvasRef = useRef(null);
  const detectionRef = useRef(null);
  const deviceInfoRef = useRef(getDeviceInfo());
  
  // Check if on mobile device
  const isMobile = /iPhone|iPad|iPod|Android/i.test(navigator.userAgent);
  const isLaptop = window.innerWidth > 768;
  
  // Instructions for optimal lip/mouth positioning
  const lipPositioningInstructions = [
    'Position your face in the center of the frame',
    'Ensure your mouth is clearly visible with good lighting',
    'Keep your head still while recording',
    'Speak clearly and at a normal pace',
    'Make sure your chin and lips are fully visible'
  ];
  
  // Function to check camera permissions and list available devices
  const checkCameraPermissions = async () => {
    try {
      console.log('Checking camera permissions...');
      
      // Check if we're in a secure context (HTTPS or localhost)
      if (!window.isSecureContext) {
        throw new Error('Camera access requires a secure context (HTTPS or localhost)');
      }
      
      // Try to access the camera
      const stream = await navigator.mediaDevices.getUserMedia({ video: true });
      
      // If we got here, permission is granted
      console.log('Camera permission granted');
      
      // Get list of available video devices
      const devices = await navigator.mediaDevices.enumerateDevices();
      const videoDevices = devices.filter(device => device.kind === 'videoinput');
      
      setAvailableCameras(videoDevices);
      if (videoDevices.length > 0 && !selectedCamera) {
        setSelectedCamera(videoDevices[0].deviceId);
      }
      
      // Clean up the stream we just created
      stream.getTracks().forEach(track => track.stop());
      
      setPermissionGranted(true);
      setCameraError(false);
      setErrorMessage('');
      
    } catch (err) {
      console.error('Camera permission error:', err);
      setCameraError(true);
      setPermissionGranted(false);
      
      if (err.name === 'NotAllowedError') {
        setErrorMessage('Camera access denied. Please allow camera access in your browser settings.');
      } else if (err.name === 'NotFoundError') {
        setErrorMessage('No camera found. Please connect a camera and try again.');
      } else {
        setErrorMessage(`Camera error: ${err.message || err.name || 'Unknown error'}`);
      }
    }
  };
  
  // Initialize camera and face detection model when component mounts
  useEffect(() => {
    const initModel = async () => {
      await tf.ready();
      const loadedModel = await faceLandmarksDetection.load(
        faceLandmarksDetection.SupportedPackages.mediapipeFacemesh
      );
      setModel(loadedModel);
    };
    
    checkCameraPermissions();
    initModel();
    
    return () => {
      if (detectionRef.current) {
        cancelAnimationFrame(detectionRef.current);
      }
    };
  }, []);
  
  // Check video brightness
  const checkBrightness = (imageData) => {
    const data = imageData.data;
    let sum = 0;
    
    // Calculate average brightness using luminance formula
    for (let i = 0; i < data.length; i += 4) {
      const r = data[i];
      const g = data[i + 1];
      const b = data[i + 2];
      const brightness = (0.299 * r + 0.587 * g + 0.114 * b);
      sum += brightness;
    }
    
    const avgBrightness = sum / (data.length / 4);
    return {
      value: avgBrightness,
      isAdequate: avgBrightness > 40 && avgBrightness < 240 // Acceptable range
    };
  };
  
  // Check for motion blur
  const checkMotionBlur = (imageData) => {
    const data = imageData.data;
    const width = imageData.width;
    const height = imageData.height;
    let blurScore = 0;
    
    // Calculate edge strength as a proxy for blur detection
    for (let y = 1; y < height - 1; y++) {
      for (let x = 1; x < width - 1; x++) {
        const idx = (y * width + x) * 4;
        const prevIdx = (y * width + (x - 1)) * 4;
        const nextIdx = (y * width + (x + 1)) * 4;
        
        // Calculate horizontal gradient
        const gradientX = Math.abs(data[prevIdx] - data[nextIdx]) +
                         Math.abs(data[prevIdx + 1] - data[nextIdx + 1]) +
                         Math.abs(data[prevIdx + 2] - data[nextIdx + 2]);
        
        blurScore += gradientX;
      }
    }
    
    const normalizedBlurScore = blurScore / (width * height);
    return {
      value: normalizedBlurScore,
      isSharp: normalizedBlurScore > 20 // Threshold for acceptable sharpness
    };
  };
  
  // Validate video quality
  const validateVideoQuality = async (video) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;
    ctx.drawImage(video, 0, 0);
    
    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
    
    const brightnessCheck = checkBrightness(imageData);
    const blurCheck = checkMotionBlur(imageData);
    
    const qualityIssues = [];
    
    if (!brightnessCheck.isAdequate) {
      if (brightnessCheck.value < 40) {
        qualityIssues.push('Video is too dark');
      } else {
        qualityIssues.push('Video is too bright');
      }
    }
    
    if (!blurCheck.isSharp) {
      qualityIssues.push('Video is blurry');
    }
    
    if (!faceDetected) {
      qualityIssues.push('Face not clearly visible');
    }
    
    return {
      isValid: qualityIssues.length === 0,
      issues: qualityIssues,
      metrics: {
        brightness: brightnessCheck.value,
        sharpness: blurCheck.value,
        faceConfidence: faceDetected ? 1 : 0
      }
    };
  };
  
  // Update quality metrics in real-time
  const updateQualityMetrics = async () => {
    if (!webcamRef.current || !webcamRef.current.video) return;
    
    const video = webcamRef.current.video;
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;
    ctx.drawImage(video, 0, 0);
    
    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
    
    const brightnessCheck = checkBrightness(imageData);
    const blurCheck = checkMotionBlur(imageData);
    
    const metrics = {
      brightness: brightnessCheck.value,
      sharpness: blurCheck.value,
      faceConfidence: faceDetected ? 1 : 0
    };
    
    // Calculate overall quality score
    const overallScore = (
      (brightnessCheck.isAdequate ? 1 : 0) +
      (blurCheck.isSharp ? 1 : 0) +
      (faceDetected ? 1 : 0)
    ) / 3;
    
    let overall = 'poor';
    if (overallScore > 0.8) overall = 'excellent';
    else if (overallScore > 0.6) overall = 'good';
    else if (overallScore > 0.3) overall = 'fair';
    
    setQualityMetrics({ ...metrics, overall });
  };
  
  // Run face detection and mouth tracking
  const detectFace = async () => {
    if (
      !webcamRef.current ||
      !webcamRef.current.video ||
      !model ||
      !canvasRef.current
    ) {
      return;
    }

    const video = webcamRef.current.video;
    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    
    try {
      const faces = await model.estimateFaces(video, {
        flipHorizontal: false,
        returnTensors: false,
        predictIrises: false
      });

      if (faces.length > 0) {
        const face = faces[0];
        
        // Get mouth landmarks
        const upperLip = face.annotations.lipsUpperOuter;
        const lowerLip = face.annotations.lipsLowerOuter;
        
        // Calculate mouth bounding box
        const mouthLeft = Math.min(...upperLip.map(p => p[0]), ...lowerLip.map(p => p[0]));
        const mouthRight = Math.max(...upperLip.map(p => p[0]), ...lowerLip.map(p => p[0]));
        const mouthTop = Math.min(...upperLip.map(p => p[1]));
        const mouthBottom = Math.max(...lowerLip.map(p => p[1]));
        
        const mouthWidth = mouthRight - mouthLeft;
        const mouthHeight = mouthBottom - mouthTop;
        const mouthCenterX = mouthLeft + mouthWidth / 2;
        const mouthCenterY = mouthTop + mouthHeight / 2;
        
        // Add padding for better mouth region capture
        const padding = 20;
        const roi = {
          x: Math.max(0, mouthCenterX - MOUTH_ROI.width / 2 - padding),
          y: Math.max(0, mouthCenterY - MOUTH_ROI.height / 2 - padding),
          width: Math.min(video.width - mouthCenterX + MOUTH_ROI.width / 2 + padding, MOUTH_ROI.width + 2 * padding),
          height: Math.min(video.height - mouthCenterY + MOUTH_ROI.height / 2 + padding, MOUTH_ROI.height + 2 * padding)
        };
        
        // Calculate tracking quality based on mouth position and size
        const idealMouthWidth = video.width * 0.15; // Mouth should be about 15% of frame width
        const widthQuality = Math.min(mouthWidth / idealMouthWidth, 1.0);
        const centeringQuality = 1.0 - Math.abs((mouthCenterX - video.width / 2) / (video.width / 2));
        const trackingQuality = (widthQuality + centeringQuality) / 2;
        
        setMouthPosition(roi);
        setMouthTrackingQuality(trackingQuality);
        setFaceDetected(true);
        
        // Draw mouth ROI on canvas for debugging
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        ctx.strokeStyle = trackingQuality > 0.7 ? '#4CAF50' : '#FFC107';
        ctx.lineWidth = 2;
        ctx.strokeRect(roi.x, roi.y, roi.width, roi.height);
      } else {
        setFaceDetected(false);
        setMouthTrackingQuality(0);
        ctx.clearRect(0, 0, canvas.width, canvas.height);
      }
    } catch (error) {
      console.error('Error in face detection:', error);
      setFaceDetected(false);
    }
    
    // Continue to run the detection as long as we're not recording
    if (!isRecording) {
      detectionRef.current = requestAnimationFrame(detectFace);
    }
  }
};

// Handle starting recording
const handleStartRecording = async () => {
  try {
    const stream = webcamRef.current.stream;
    const mediaRecorder = new MediaRecorder(stream, {
      mimeType: 'video/webm;codecs=vp8',
      videoBitsPerSecond: 2500000 // 2.5 Mbps for good quality
    });

    mediaRecorderRef.current = mediaRecorder;
    setRecordedChunks([]);
    setIsRecording(true);

    mediaRecorder.ondataavailable = (event) => {
      if (event.data.size > 0) {
        setRecordedChunks((prev) => [...prev, event.data]);
      }
    };

    mediaRecorder.onstop = async () => {
      // Create the original video blob
      const videoBlob = new Blob(recordedChunks, {
        type: 'video/webm'
      });
      
      try {
        // Process the video for LipNet compatibility if we have mouth position data
        let finalVideoBlob = videoBlob;
        if (mouthPosition) {
          console.log('Processing video for LipNet compatibility...', mouthPosition);
          finalVideoBlob = await createLipNetVideo(videoBlob, mouthPosition, LIPNET_OPTIONS);
          console.log('LipNet video processing complete', finalVideoBlob.size);
        } else {
          console.warn('No mouth position data available, using original video');
        }
        
        await handleRecordingComplete(finalVideoBlob);
      } catch (error) {
        console.error('Error processing video:', error);
        // Fall back to original video if processing fails
        await handleRecordingComplete(videoBlob);
      }
    };

    // Start recording with frequent data chunks for better processing
    mediaRecorder.start(100); // Request data every 100ms
    console.log('Recording started with LipNet settings');

    // Record for exactly 3 seconds (standard LipNet duration)
    setTimeout(() => {
      if (mediaRecorder.state === 'recording') {
        handleStopRecording();
      }
    }, 3000);
  } catch (err) {
    console.error('Error starting recording:', err);
    setErrorMessage(`Recording error: ${err.message}`);
    setIsRecording(false);
  }

  // Webcam component
  return (
  <Box sx={{ width: '100%', maxWidth: 800, mx: 'auto' }}>
    <Box
      sx={{
        position: 'relative',
        width: '100%',
        paddingTop: '75%', // 4:3 aspect ratio
        backgroundColor: '#000',
        borderRadius: 2,
        overflow: 'hidden'
      }}
    >
      {permissionGranted ? (
        <>
          <Webcam
            ref={webcamRef}
            audio={false}
            videoConstraints={{
              ...VIDEO_CONSTRAINTS,
              deviceId: selectedCamera
            }}
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              width: '100%',
              height: '100%',
              objectFit: 'cover',
              transform: `scale(${zoomLevel})`,
              transformOrigin: 'center'
            }}
          />
          
          <canvas
            ref={canvasRef}
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              width: '100%',
              height: '100%',
              pointerEvents: 'none'
            }}
            width={VIDEO_CONSTRAINTS.width}
            height={VIDEO_CONSTRAINTS.height}
          />
          
          {/* LipNet positioning guide overlay - only shown when not recording */}
          {!isRecording && (
            <Box
              sx={{
                position: 'absolute',
                top: 16,
                left: 16,
                bgcolor: 'rgba(0, 0, 0, 0.7)',
                color: 'white',
                p: 1.5,
                borderRadius: 2,
                maxWidth: 250
              }}
            >
              <Typography variant="subtitle2" sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <InfoIcon sx={{ mr: 1, color: '#2196f3' }} />
                Lip Positioning Guide
              </Typography>
              <ul style={{ margin: '8px 0', paddingLeft: '20px' }}>
                {lipPositioningInstructions.map((instruction, idx) => (
                  <li key={idx} style={{ fontSize: '12px', marginBottom: '4px' }}>
                    {instruction}
                  </li>
                ))}
              </ul>
            </Box>
          )}
          
          {/* Controls container */}
          <Box
            sx={{
              position: 'absolute',
              bottom: 16,
              left: '50%',
              transform: 'translateX(-50%)',
              width: '100%',
              maxWidth: 300,
              bgcolor: 'rgba(0, 0, 0, 0.7)',
              borderRadius: 2,
              p: 2
            }}
          >
            {/* Camera selection */}
            {availableCameras.length > 1 && (
              <Box sx={{ mb: 2 }}>
                <Typography variant="body2" sx={{ color: 'white', mb: 1 }}>
                  Select Camera:
                </Typography>
                <select
                  value={selectedCamera}
                  onChange={(e) => setSelectedCamera(e.target.value)}
                  style={{
                    width: '100%',
                    padding: '8px',
                    borderRadius: '4px',
                    backgroundColor: 'rgba(255, 255, 255, 0.9)'
                  }}
                >
                  {availableCameras.map((device) => (
                    <option key={device.deviceId} value={device.deviceId}>
                      {device.label || `Camera ${availableCameras.indexOf(device) + 1}`}
                    </option>
                  ))}
                </select>
              </Box>
            )}
            
            {/* Quality feedback panel */}
            <Box
              sx={{
                position: 'absolute',
                top: 16,
                right: 16,
                backgroundColor: 'rgba(0, 0, 0, 0.7)',
                borderRadius: 2,
                p: 2,
                color: 'white',
                minWidth: 200
              }}
            >
              <Typography variant="body2" gutterBottom>
                Recording Quality
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                <CircleIcon
                  sx={{
                    color: mouthTrackingQuality > 0.7 ? 'success.main' : 'warning.main',
                    fontSize: 12
                  }}
                />
                <Typography variant="body2">
                  {mouthTrackingQuality > 0.7 ? 'Good' : 'Adjust Position'}
                </Typography>
              </Box>
            </Box>
            {isMobile && (
              <Box sx={{ mt: 2, p: 2, bgcolor: 'rgba(0,150,136,0.1)', borderRadius: 2 }}>
                <Typography variant="body2" align="left">
                  <strong>Mobile Tips:</strong>
                  <ul style={{ paddingLeft: '20px', textAlign: 'left' }}>
                    <li>Make sure to allow camera permissions when prompted</li>
                    <li>On iOS, Safari is recommended as it may allow camera access on local networks</li>
                    <li>For iOS: Go to Settings &gt; Safari &gt; Camera &gt; Allow for this website</li>
                    <li>For Android: Try using Chrome and ensure all permissions are granted</li>
                    <li>Refresh the page after granting permissions</li>
                  </ul>
                </Typography>
              </Box>
            )}
            <Button
              variant="contained"
              color="primary"
              onClick={() => window.location.reload()}
              sx={{ mt: 3 }}
            >
              Refresh Page
            </Button>
          </Box>
        )}
        
        {/* Quality feedback panel */}
        <Box
          sx={{
            position: 'absolute',
            top: 16,
            right: 16,
            backgroundColor: 'rgba(0, 0, 0, 0.7)',
            borderRadius: 2,
            p: 2,
            color: 'white',
            minWidth: 200
          }}
        >
          <Typography variant="subtitle2" gutterBottom>
            Recording Quality
          </Typography>
          
          <Box sx={{ mb: 1 }}>
            <Typography variant="caption" component="div" sx={{ mb: 0.5 }}>
              Overall: 
              <Box
                component="span"
                sx={{
                  color: {
                    excellent: 'success.main',
                    good: '#4caf50',
                    fair: '#ff9800',
                    poor: 'error.main'
                  }[qualityMetrics.overall],
                  ml: 1,
                  fontWeight: 'bold'
                }}
              >
                {qualityMetrics.overall.toUpperCase()}
              </Box>
            </Typography>
          </Box>
          
          <Box sx={{ mb: 1 }}>
            <Typography variant="caption" component="div">
              Brightness
            </Typography>
            <LinearProgress
              variant="determinate"
              value={(qualityMetrics.brightness / 255) * 100}
              color={qualityMetrics.brightness > 40 && qualityMetrics.brightness < 240 ? 'success' : 'error'}
              sx={{ height: 4, borderRadius: 2 }}
            />
          </Box>
          
          <Box sx={{ mb: 1 }}>
            <Typography variant="caption" component="div">
              Sharpness
            </Typography>
            <LinearProgress
              variant="determinate"
              value={(qualityMetrics.sharpness / 50) * 100}
              color={qualityMetrics.sharpness > 20 ? 'success' : 'error'}
              sx={{ height: 4, borderRadius: 2 }}
            />
          </Box>
          
          <Box>
            <Typography variant="caption" component="div">
              Face Detection
            </Typography>
            <LinearProgress
              variant="determinate"
              value={qualityMetrics.faceConfidence * 100}
              color={faceDetected ? 'success' : 'error'}
              sx={{ height: 4, borderRadius: 2 }}
            />
          </Box>
        </Box>
        
        <Box sx={{ mt: 2, display: 'flex', justifyContent: 'center', gap: 2 }}>
            {!isRecording ? (
              <>
                <Button
                  variant="contained"
                  color="primary"
                  onClick={handleStartRecording}
                  disabled={disabled || cameraError}
                  sx={{ minWidth: 120 }}
                >
                  Start Recording
                </Button>
                
                {canReRecord && (
                  <Button
                    variant="outlined"
                    color="secondary"
                    onClick={() => {
                      console.log('Record Again clicked - resetting recording count and starting new recording');
                      // Reset recording count first
                      setRecordingCount(0);
                      // Small delay to ensure UI updates
                      setTimeout(() => {
                        handleStartRecording();
                      }, 50);
                    }}
                    disabled={disabled || cameraError}
                    sx={{ minWidth: 120 }}
                  >
                    Record Again
                  </Button>
                )}
              </>
            ) : (
              <Button
                variant="contained"
                color="error"
                onClick={handleStopRecording}
                sx={{ minWidth: 120 }}
              >
                Stop Recording
              </Button>
            )}
          </Box>
        </>
        ) : (
          <WebcamPermissionHandler 
            onPermissionGranted={() => setPermissionGranted(true)}
            onError={(msg) => {
              setErrorMessage(msg);
              setCameraError(true);
            }}
          />
        )}
        
        {processing && (
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mt: 2 }}>
            <CircularProgress size={24} />
            <Typography>Processing video...</Typography>
          </Box>
        )}
        
        {errorMessage && (
          <Alert severity="error" sx={{ mt: 2 }}>
            {errorMessage}
          </Alert>
        )}
        
        <Snackbar
          open={showSavedNotification}
          autoHideDuration={3000}
          onClose={() => setShowSavedNotification(false)}
          anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
        >
          <Alert severity="success" variant="filled">
            Recordings saved successfully!
          </Alert>
        </Snackbar>
      </Box>
    </Box>
  );
};

export default VideoRecorder;
