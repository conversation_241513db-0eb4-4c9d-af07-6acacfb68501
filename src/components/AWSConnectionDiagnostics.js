import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Button,
  Paper,
  Alert,
  CircularProgress,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Chip,
  Divider,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Card,
  CardContent
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import ErrorIcon from '@mui/icons-material/Error';
import WarningIcon from '@mui/icons-material/Warning';
import InfoIcon from '@mui/icons-material/Info';
import CloudIcon from '@mui/icons-material/Cloud';
import { testAWSConnection } from '../services/awsStorage';

const AWSConnectionDiagnostics = () => {
  const [diagnostics, setDiagnostics] = useState({
    status: 'idle',
    results: null,
    steps: []
  });

  const runDiagnostics = async () => {
    setDiagnostics({ status: 'running', results: null, steps: [] });
    const steps = [];

    // Step 1: Check environment variables
    steps.push({
      name: 'Environment Variables Check',
      status: 'running',
      details: 'Checking AWS configuration...'
    });
    setDiagnostics(prev => ({ ...prev, steps: [...steps] }));

    const envCheck = {
      identityPoolId: process.env.REACT_APP_AWS_IDENTITY_POOL_ID,
      region: process.env.REACT_APP_AWS_REGION,
      bucket: process.env.REACT_APP_S3_BUCKET,
      backendUrl: process.env.REACT_APP_BACKEND_URL
    };

    const envValid = envCheck.identityPoolId && 
                    envCheck.identityPoolId !== 'your-identity-pool-id-here' &&
                    envCheck.region && 
                    envCheck.bucket;

    steps[0] = {
      name: 'Environment Variables Check',
      status: envValid ? 'success' : 'error',
      details: envValid ? 'All required environment variables are configured' : 'Missing or invalid environment variables',
      data: envCheck
    };
    setDiagnostics(prev => ({ ...prev, steps: [...steps] }));

    // Step 2: Check backend connectivity
    steps.push({
      name: 'Backend Server Check',
      status: 'running',
      details: 'Testing backend server connectivity...'
    });
    setDiagnostics(prev => ({ ...prev, steps: [...steps] }));

    try {
      const backendUrl = process.env.REACT_APP_BACKEND_URL || 'http://localhost:5000';
      const healthResponse = await fetch(`${backendUrl}/health`);
      const healthData = await healthResponse.json();
      
      steps[1] = {
        name: 'Backend Server Check',
        status: healthResponse.ok ? 'success' : 'error',
        details: healthResponse.ok ? 'Backend server is responding' : 'Backend server is not responding',
        data: healthData
      };
    } catch (error) {
      steps[1] = {
        name: 'Backend Server Check',
        status: 'error',
        details: `Backend server connection failed: ${error.message}`,
        data: { error: error.message }
      };
    }
    setDiagnostics(prev => ({ ...prev, steps: [...steps] }));

    // Step 3: Test AWS connection
    steps.push({
      name: 'AWS S3 Connection Test',
      status: 'running',
      details: 'Testing AWS S3 connectivity...'
    });
    setDiagnostics(prev => ({ ...prev, steps: [...steps] }));

    try {
      const awsResult = await testAWSConnection();
      steps[2] = {
        name: 'AWS S3 Connection Test',
        status: awsResult.success ? 'success' : 'error',
        details: awsResult.success ? 'AWS S3 connection successful' : awsResult.error,
        data: awsResult
      };
    } catch (error) {
      steps[2] = {
        name: 'AWS S3 Connection Test',
        status: 'error',
        details: `AWS connection test failed: ${error.message}`,
        data: { error: error.message, type: error.name }
      };
    }

    setDiagnostics({ status: 'completed', results: steps, steps });
  };

  useEffect(() => {
    runDiagnostics();
  }, []);

  const getStatusIcon = (status) => {
    switch (status) {
      case 'success': return <CheckCircleIcon color="success" />;
      case 'error': return <ErrorIcon color="error" />;
      case 'warning': return <WarningIcon color="warning" />;
      case 'running': return <CircularProgress size={20} />;
      default: return <InfoIcon color="info" />;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'success': return 'success';
      case 'error': return 'error';
      case 'warning': return 'warning';
      case 'running': return 'info';
      default: return 'default';
    }
  };

  return (
    <Box sx={{ p: 3, maxWidth: 1000, mx: 'auto' }}>
      <Typography variant="h4" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        <CloudIcon />
        AWS Connection Diagnostics
      </Typography>
      
      <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
        Comprehensive diagnostics for AWS S3 connectivity issues
      </Typography>

      <Box sx={{ mb: 3, display: 'flex', gap: 2 }}>
        <Button 
          variant="contained" 
          onClick={runDiagnostics}
          disabled={diagnostics.status === 'running'}
          startIcon={diagnostics.status === 'running' ? <CircularProgress size={16} /> : null}
        >
          {diagnostics.status === 'running' ? 'Running Diagnostics...' : 'Run Diagnostics'}
        </Button>
      </Box>

      {/* Diagnostic Steps */}
      {diagnostics.steps.map((step, index) => (
        <Accordion key={index} defaultExpanded>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, width: '100%' }}>
              <Typography variant="h6">{index + 1}. {step.name}</Typography>
              {getStatusIcon(step.status)}
              <Chip 
                label={step.status === 'running' ? 'Testing...' : step.status}
                color={getStatusColor(step.status)}
                size="small"
              />
            </Box>
          </AccordionSummary>
          <AccordionDetails>
            <Alert severity={step.status === 'success' ? 'success' : step.status === 'error' ? 'error' : 'info'}>
              <Typography variant="subtitle2">{step.details}</Typography>
            </Alert>
            
            {step.data && (
              <Card sx={{ mt: 2 }}>
                <CardContent>
                  <Typography variant="subtitle2" gutterBottom>Details:</Typography>
                  <pre style={{ fontSize: '12px', overflow: 'auto', maxHeight: '200px' }}>
                    {JSON.stringify(step.data, null, 2)}
                  </pre>
                </CardContent>
              </Card>
            )}
          </AccordionDetails>
        </Accordion>
      ))}

      <Divider sx={{ my: 3 }} />

      {/* Troubleshooting Guide */}
      <Paper sx={{ p: 3, bgcolor: 'background.default' }}>
        <Typography variant="h6" gutterBottom>Troubleshooting Guide</Typography>
        
        <Typography variant="subtitle2" gutterBottom sx={{ mt: 2 }}>
          Common Issues and Solutions:
        </Typography>
        
        <List dense>
          <ListItem>
            <ListItemIcon><ErrorIcon color="error" /></ListItemIcon>
            <ListItemText 
              primary="Failed to fetch / TypeError"
              secondary="Usually indicates CORS issues, network connectivity problems, or invalid AWS region. Check browser console for detailed error messages."
            />
          </ListItem>
          
          <ListItem>
            <ListItemIcon><WarningIcon color="warning" /></ListItemIcon>
            <ListItemText 
              primary="CredentialsProviderError"
              secondary="AWS Cognito Identity Pool credentials are invalid or the Identity Pool doesn't exist. Verify the Identity Pool ID in your AWS console."
            />
          </ListItem>
          
          <ListItem>
            <ListItemIcon><ErrorIcon color="error" /></ListItemIcon>
            <ListItemText 
              primary="AccessDenied"
              secondary="The Cognito Identity Pool doesn't have permissions to access the S3 bucket. Check IAM roles and policies."
            />
          </ListItem>
          
          <ListItem>
            <ListItemIcon><InfoIcon color="info" /></ListItemIcon>
            <ListItemText 
              primary="NoSuchBucket"
              secondary="The S3 bucket doesn't exist in the specified region. Verify bucket name and region in AWS console."
            />
          </ListItem>
        </List>
      </Paper>
    </Box>
  );
};

export default AWSConnectionDiagnostics;
