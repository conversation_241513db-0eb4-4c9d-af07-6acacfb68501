import React from 'react';
import { Box, Button } from '@mui/material';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';

const PhraseNavigation = ({ onPrevious, onNext, hasPrevious, hasNext, disabled }) => {
  return (
    <Box sx={{ display: 'flex', justifyContent: 'center', gap: 2, my: 3 }}>
      <Button
        variant="outlined"
        startIcon={<ArrowBackIcon />}
        onClick={onPrevious}
        disabled={!hasPrevious || disabled}
      >
        Previous
      </Button>
      
      <Button
        variant="outlined"
        endIcon={<ArrowForwardIcon />}
        onClick={onNext}
        disabled={!hasNext || disabled}
      >
        Next
      </Button>
    </Box>
  );
};

export default PhraseNavigation;
