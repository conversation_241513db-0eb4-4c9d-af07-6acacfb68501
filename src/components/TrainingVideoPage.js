import React, { useState, useEffect } from 'react';
import { Box, Typography, Paper, Button, Container } from '@mui/material';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import PlayCircleOutlineIcon from '@mui/icons-material/PlayCircleOutline';

const TrainingVideoPage = ({ onComplete }) => {
  const [videoLoaded, setVideoLoaded] = useState(false);

  // Scroll to top when component mounts
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);



  return (
    <Container maxWidth="md">
      <Paper elevation={3} sx={{ p: 4, mt: 4, mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom align="center">
          Training Tips
        </Typography>
        
        <Typography variant="body1" paragraph align="center">
          Please watch this short video that demonstrates how to position your face when mouthing the phrases.
        </Typography>

        <Box sx={{ position: 'relative', paddingTop: '56.25%', width: '100%', mb: 3 }}>
          <style>
            {`
              .training-video-container {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                border-radius: 8px;
                overflow: hidden;
              }

              .training-video-container video {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                z-index: 1;
                border-radius: 8px;
              }

              /* Black overlay covering top 55% of video viewport */
              .training-video-container::before {
                content: '';
                position: absolute;
                top: 0%;
                left: 0;
                right: 0;
                height: 55%;
                background-color: rgba(0, 0, 0, 1.0);
                z-index: 2;
                pointer-events: none;
              }

              .training-overlay-text {
                position: absolute;
                top: 0%;
                left: 0;
                right: 0;
                height: 55%;
                display: flex;
                justify-content: center;
                align-items: center;
                color: white;
                z-index: 3;
                pointer-events: none;
              }
            `}
          </style>
          <div className="training-video-container">
            <video
              controls
              onLoadedData={() => setVideoLoaded(true)}
              onError={(e) => console.error('Video loading error:', e)}
            >
              <source src="/videos/training-video.mp4" type="video/mp4" />
              <source src="/videos/training-video-backup.mp4" type="video/mp4" />
              Your browser does not support the video tag.
            </video>

            {/* Overlay text in the blackened top area */}
            <div className="training-overlay-text">
              <Typography
                variant="h5"
                sx={{
                  fontWeight: 'bold',
                  color: '#FFFFFF',
                  textShadow: '2px 2px 4px rgba(0,0,0,0.8)',
                  textAlign: 'center'
                }}
              >
                I need a blanket
              </Typography>
            </div>
          </div>
        </Box>
        
        <Typography variant="h5" paragraph align="left" sx={{ fontWeight: 'bold', mt: 3 }}>
          Key points to remember:
        </Typography>
        
        <ul style={{ paddingLeft: '20px' }}>
          <li>
            <Typography variant="h6" paragraph align="left">
              Position your face within the oval guide for optimal recording.
            </Typography>
          </li>
          <li>
            <Typography variant="h6" paragraph align="left">
              Mouth the entire phrase silently—avoid saying each word separately.
            </Typography>
          </li>
          <li>
            <Typography variant="h6" paragraph align="left">
              Use a natural pace when mouthing the words, as if you were speaking normally.
            </Typography>
          </li>
          <li>
            <Typography variant="h6" paragraph align="left">
              Record in a well-lit environment.
            </Typography>
          </li>
          <li>
            <Typography variant="h6" paragraph align="left">
              Please record each word or phrase once then record it again two more times a total of 3 times.
            </Typography>
          </li>
          <li>
            <Typography variant="h6" paragraph align="left">
              Feel free to exit at any time — your recordings save automatically.
            </Typography>
          </li>
        </ul>
        
        <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
          <Button
            variant="contained"
            color="primary"
            size="large"
            endIcon={<ArrowForwardIcon />}
            onClick={onComplete}
          >
            Continue to Phrase Selection
          </Button>
        </Box>
        
        <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>
          <Button
            variant="text"
            color="primary"
            startIcon={<PlayCircleOutlineIcon />}
            onClick={() => {
              // Restart the video
              const videoElement = document.querySelector('video');
              if (videoElement) {
                videoElement.currentTime = 0;
                videoElement.play();
              }
            }}
          >
            Replay Video
          </Button>
        </Box>
      </Paper>
    </Container>
  );
};

export default TrainingVideoPage;
