import React, { useState } from 'react';
import { Box, Typography, Paper, Chip, Button, Accordion, AccordionSummary, AccordionDetails } from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import AWSConnectionDiagnostics from './AWSConnectionDiagnostics';

const EnvironmentDebugger = () => {
  const [showAWSDiagnostics, setShowAWSDiagnostics] = useState(false);

  const envVars = {
    'REACT_APP_BACKEND_URL': process.env.REACT_APP_BACKEND_URL,
    'REACT_APP_AWS_IDENTITY_POOL_ID': process.env.REACT_APP_AWS_IDENTITY_POOL_ID,
    'REACT_APP_AWS_REGION': process.env.REACT_APP_AWS_REGION,
    'REACT_APP_S3_BUCKET': process.env.REACT_APP_S3_BUCKET,
    'NODE_ENV': process.env.NODE_ENV
  };

  const testBackendConnection = async () => {
    const backendUrl = process.env.REACT_APP_BACKEND_URL || 'http://localhost:5000';
    try {
      console.log('🧪 Testing backend connection to:', `${backendUrl}/health`);
      const response = await fetch(`${backendUrl}/health`);
      const data = await response.json();
      console.log('✅ Backend connection successful:', data);
      alert('Backend connection successful! Check console for details.');
    } catch (error) {
      console.error('❌ Backend connection failed:', error);
      alert('Backend connection failed! Check console for details.');
    }
  };

  return (
    <Box sx={{ m: 2 }}>
      {/* Environment Variables Panel */}
      <Accordion>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Typography variant="h6">
            🔧 Environment Variables Debug Panel
          </Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Paper sx={{ p: 2 }}>
            <Box sx={{ mb: 2 }}>
              {Object.entries(envVars).map(([key, value]) => (
                <Box key={key} sx={{ mb: 1, display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Typography variant="body2" sx={{ fontWeight: 'bold', minWidth: 200 }}>
                    {key}:
                  </Typography>
                  <Chip
                    label={value || 'undefined'}
                    color={value ? 'success' : 'error'}
                    size="small"
                  />
                </Box>
              ))}
            </Box>

            <Button
              onClick={testBackendConnection}
              variant="contained"
              color="primary"
              sx={{ mb: 2 }}
            >
              🧪 Test Backend Connection
            </Button>

            <Box sx={{ mt: 2 }}>
              <Typography variant="body2" color="text.secondary">
                This panel shows the current environment variables loaded in the frontend.
                Click "Test Backend Connection" to verify the backend URL is working.
              </Typography>
            </Box>
          </Paper>
        </AccordionDetails>
      </Accordion>

      {/* AWS Connection Diagnostics Panel */}
      <Accordion sx={{ mt: 2 }}>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Typography variant="h6">
            🔍 AWS Connection Diagnostics
          </Typography>
        </AccordionSummary>
        <AccordionDetails>
          <AWSConnectionDiagnostics />
        </AccordionDetails>
      </Accordion>
    </Box>
  );
};

export default EnvironmentDebugger;
