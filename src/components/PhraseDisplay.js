import React from 'react';
import { Typography, Paper, Box, Chip } from '@mui/material';

const PhraseDisplay = ({ phrase, categoryName, currentIndex, totalPhrases, currentRecording, totalRecordings }) => {
  // Add safety checks for undefined or null values
  const safePhrase = phrase || '';
  const safeCategoryName = categoryName || '';
  const safeCurrentIndex = typeof currentIndex === 'number' ? currentIndex : 0;
  const safeTotalPhrases = typeof totalPhrases === 'number' ? totalPhrases : 0;
  const safeCurrentRecording = typeof currentRecording === 'number' ? currentRecording : 0;
  const safeTotalRecordings = typeof totalRecordings === 'number' ? totalRecordings : 0;
  return (
    <Paper 
      elevation={2} 
      sx={{ 
        p: 2, 
        mb: 2, 
        textAlign: 'center',
        backgroundColor: '#f9f9f9',
        borderLeft: '5px solid #3f51b5'
      }}
    >
      <Typography variant="h4" component="h2" sx={{ mb: 1 }}>
        {safePhrase}
      </Typography>
      
      <Box sx={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center',
        flexWrap: 'wrap',
        gap: 1
      }}>
        <Typography variant="body2" color="textSecondary">
          {safeCategoryName}{safeTotalPhrases > 0 ? ` • Phrase ${safeCurrentIndex}/${safeTotalPhrases}` : ''}
        </Typography>
        
        <Chip 
          size="small"
          label={`Recording ${safeCurrentRecording}/${safeTotalRecordings}`} 
          color="primary" 
          variant="outlined"
        />
      </Box>
    </Paper>
  );
};

export default PhraseDisplay;
