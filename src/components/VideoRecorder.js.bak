import React, { useState, useRef, useEffect } from 'react';
import { Box, Button, Typography, CircularProgress } from '@mui/material';
import Webcam from 'react-webcam';
import WebcamPermissionHandler from './WebcamPermissionHandler';

const VideoRecorder = ({ 
  onRecordingComplete, 
  disabled = false, 
  category, 
  phrase, 
  recordingNumber, 
  demographics 
}) => {
  // State variables
  const [permissionGranted, setPermissionGranted] = useState(false);
  const [cameraError, setCameraError] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [isRecording, setIsRecording] = useState(false);
  const [recordedChunks, setRecordedChunks] = useState([]);
  const [processing, setProcessing] = useState(false);
  const [selectedCamera, setSelectedCamera] = useState('');
  const [availableCameras, setAvailableCameras] = useState([]);
  const [zoomLevel, setZoomLevel] = useState(1);
  
  // Refs
  const webcamRef = useRef(null);
  const mediaRecorderRef = useRef(null);
  
  // Check if on mobile device
  const isMobile = /iPhone|iPad|iPod|Android/i.test(navigator.userAgent);
  const isLaptop = window.innerWidth > 768;
  
  // Function to check camera permissions and list available devices
  const checkCameraPermissions = async () => {
    try {
      console.log('Checking camera permissions...');
      
      // Check if we're in a secure context (HTTPS or localhost)
      if (!window.isSecureContext) {
        throw new Error('Camera access requires a secure context (HTTPS or localhost)');
      }
      
      // Try to access the camera
      const stream = await navigator.mediaDevices.getUserMedia({ video: true });
      
      // If we got here, permission is granted
      console.log('Camera permission granted');
      
      // Get list of available video devices
      const devices = await navigator.mediaDevices.enumerateDevices();
      const videoDevices = devices.filter(device => device.kind === 'videoinput');
      
      setAvailableCameras(videoDevices);
      if (videoDevices.length > 0 && !selectedCamera) {
        setSelectedCamera(videoDevices[0].deviceId);
      }
      
      // Clean up the stream we just created
      stream.getTracks().forEach(track => track.stop());
      
      setPermissionGranted(true);
      setCameraError(false);
      setErrorMessage('');
      
    } catch (err) {
      console.error('Camera permission error:', err);
      setCameraError(true);
      setPermissionGranted(false);
      
      if (err.name === 'NotAllowedError') {
        setErrorMessage('Camera access denied. Please allow camera access in your browser settings.');
      } else if (err.name === 'NotFoundError') {
        setErrorMessage('No camera found. Please connect a camera and try again.');
      } else {
        setErrorMessage(`Camera error: ${err.message || err.name || 'Unknown error'}`);
      }
    }
  };
  
  // Initialize camera when component mounts
  useEffect(() => {
    checkCameraPermissions();
  }, []);
  
  // Update camera when selected device changes
  useEffect(() => {
    if (selectedCamera && permissionGranted) {
      console.log('Selected camera changed:', selectedCamera);
    }
  }, [selectedCamera, permissionGranted]);
  
  // Start recording function
  const handleStartRecording = () => {
    if (!webcamRef.current || !webcamRef.current.stream) {
      setErrorMessage('Camera not ready. Please try again.');
      return;
    }
    
    setIsRecording(true);
    setRecordedChunks([]);
    
    const options = { mimeType: 'video/webm' };
    try {
      mediaRecorderRef.current = new MediaRecorder(webcamRef.current.stream, options);
      
      mediaRecorderRef.current.ondataavailable = (e) => {
        if (e.data.size > 0) {
          setRecordedChunks(prev => [...prev, e.data]);
        }
      };
      
      mediaRecorderRef.current.onstop = handleDataAvailable;
      mediaRecorderRef.current.start();
      
      console.log('Recording started');
    } catch (err) {
      console.error('Error starting recording:', err);
      setErrorMessage(`Recording error: ${err.message || 'Unknown error'}`);
      setIsRecording(false);
    }
  };
  
  // Stop recording function
  const handleStopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
      console.log('Recording stopped');
    }
  };
  
  // Handle recorded data
  const handleDataAvailable = async () => {
    if (recordedChunks.length > 0) {
      setProcessing(true);
      
      try {
        const blob = new Blob(recordedChunks, { type: 'video/webm' });
        
        // Pass the recorded blob to the parent component
        if (onRecordingComplete) {
          await onRecordingComplete(blob);
        }
        
        setRecordedChunks([]);
      } catch (err) {
        console.error('Error processing recording:', err);
        setErrorMessage(`Error processing recording: ${err.message || 'Unknown error'}`);
      } finally {
        setProcessing(false);
      }
    }
  };
  
  // Simple oval guide component
  const OvalGuide = ({ isRecording }) => (
    <div
      style={{
        position: 'absolute',
        top: '50%',
        left: '50%',
        transform: 'translate(-50%, -50%)',
        width: '80%',
        height: '60%',
        border: `3px solid ${isRecording ? '#f44336' : '#4CAF50'}`,
        borderRadius: '50%',
        pointerEvents: 'none',
      }}
    />
  );
  
  return (
    <Box sx={{ width: '100%', maxWidth: 600, mx: 'auto', my: 2 }}>
      <Typography variant="h5" gutterBottom align="center">
        Record Video
      </Typography>
      
      {/* Camera view or error message */}
      <Box sx={{ position: 'relative' }}>
        <style>
          {`
            .webcam-container {
              position: relative;
              overflow: hidden;
              border-radius: 50% / 60%;
              background-color: #000;
              width: 400px;
              height: 500px;
              max-width: 90vw;
              max-height: 90vh;
              margin: 0 auto;
            }
          `}
        </style>
        
        {!cameraError && permissionGranted ? (
          <>
            <div className="webcam-container">
              <Webcam
                audio={false}
                ref={webcamRef}
                videoConstraints={{
                  deviceId: selectedCamera ? { exact: selectedCamera } : undefined,
                  facingMode: "user",
                  width: { ideal: 1280 },
                  height: { ideal: 720 }
                }}
                style={{
                  width: '100%',
                  height: '100%',
                  objectFit: 'cover',
                  transform: `scale(${zoomLevel})`,
                  transformOrigin: 'center'
                }}
              />
            </div>
            
            {/* Camera selection dropdown */}
            {availableCameras.length > 1 && (
              <Box sx={{ mt: 2, textAlign: 'center' }}>
                <Typography variant="body2" gutterBottom>
                  Select Camera:
                </Typography>
                <select
                  value={selectedCamera}
                  onChange={(e) => setSelectedCamera(e.target.value)}
                  style={{
                    padding: '8px',
                    borderRadius: '4px',
                    border: '1px solid #ccc'
                  }}
                >
                  {availableCameras.map((camera, idx) => (
                    <option key={camera.deviceId} value={camera.deviceId}>
                      {camera.label || `Camera ${idx + 1}`}
                    </option>
                  ))}
                </select>
              </Box>
            )}
          </>
        ) : (
          <Box
            sx={{
              minHeight: 300,
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'center',
              alignItems: 'center',
              p: 3,
              bgcolor: 'rgba(0,0,0,0.03)'
            }}
          >
            <WebcamPermissionHandler 
              onPermissionGranted={() => {
                setCameraError(false);
                setPermissionGranted(true);
                setErrorMessage('');
                // Re-initialize camera after permission is granted
                checkCameraPermissions();
              }}
              onError={(err) => {
                setCameraError(true);
                setPermissionGranted(false);
                setErrorMessage(err.message || 'Camera error');
              }}
            />
            {isMobile && (
              <Box sx={{ mt: 2, p: 2, bgcolor: 'rgba(0,150,136,0.1)', borderRadius: 2 }}>
                <Typography variant="body2" align="left">
                  <strong>Mobile Tips:</strong>
                  <ul style={{ paddingLeft: '20px', textAlign: 'left' }}>
                    <li>Make sure to allow camera permissions when prompted</li>
                    <li>On iOS, Safari is recommended as it may allow camera access on local networks</li>
                    <li>For iOS: Go to Settings &gt; Safari &gt; Camera &gt; Allow for this website</li>
                    <li>For Android: Try using Chrome and ensure all permissions are granted</li>
                    <li>Refresh the page after granting permissions</li>
                  </ul>
                </Typography>
              </Box>
            )}
            <Button
              variant="contained"
              color="primary"
              onClick={() => window.location.reload()}
              sx={{ mt: 3 }}
            >
              Refresh Page
            </Button>
          </Box>
        )}
        
        <OvalGuide isRecording={isRecording} />
        
        {isRecording && (
          <Box
            sx={{
              position: 'absolute',
              top: 16,
              left: '50%',
              transform: 'translateX(-50%)',
              display: 'flex',
              alignItems: 'center',
              gap: 1,
              bgcolor: 'rgba(244, 67, 54, 0.8)',
              color: 'white',
              px: 2,
              py: 1,
              borderRadius: 2
            }}
          >
            <CircularProgress
              size={16}
              sx={{
                color: 'white',
                animationDuration: '1s'
              }}
            />
            <Typography variant="body2" fontWeight="medium">
              Recording...
            </Typography>
          </Box>
        )}
      </Box>
      
      <Box
        sx={{
          p: 3,
          display: 'flex',
          flexDirection: isLaptop ? 'row' : 'column',
          justifyContent: 'center',
          alignItems: 'center',
          gap: 2,
        }}
      >
        {permissionGranted && !processing && (
          <>
            {!isRecording ? (
              <Button
                variant="contained"
                color="primary"
                onClick={handleStartRecording}
                disabled={disabled || cameraError}
                sx={{ minWidth: 120 }}
              >
                Start Recording
              </Button>
            ) : (
              <Button
                variant="contained"
                color="error"
                onClick={handleStopRecording}
                sx={{ minWidth: 120 }}
              >
                Stop Recording
              </Button>
            )}
          </>
        )}
        
        {processing && (
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <CircularProgress size={24} />
            <Typography>Processing video...</Typography>
          </Box>
        )}
        
        {errorMessage && (
          <Typography color="error" sx={{ mt: 2 }}>
            {errorMessage}
          </Typography>
        )}
      </Box>
    </Box>
  );
};

export default VideoRecorder;
