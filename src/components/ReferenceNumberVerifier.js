import React, { useState } from 'react';
import {
  Box,
  Paper,
  Typo<PERSON>,
  TextField,
  Button,
  Alert,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Chip,
  Grid,
  Divider,
  Card,
  CardContent
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import SearchIcon from '@mui/icons-material/Search';
import VerifiedIcon from '@mui/icons-material/Verified';
import ErrorIcon from '@mui/icons-material/Error';
import InfoIcon from '@mui/icons-material/Info';
import referenceNumberService from '../services/referenceNumberService';

const ReferenceNumberVerifier = () => {
  const [referenceNumber, setReferenceNumber] = useState('');
  const [verificationResult, setVerificationResult] = useState(null);
  const [sessionData, setSessionData] = useState(null);
  const [statistics, setStatistics] = useState(null);
  const [loading, setLoading] = useState(false);

  // Load statistics on component mount
  React.useEffect(() => {
    const stats = referenceNumberService.getStatistics();
    setStatistics(stats);
  }, []);

  const handleVerify = () => {
    if (!referenceNumber.trim()) {
      setVerificationResult({
        exists: false,
        valid: false,
        message: 'Please enter a reference number'
      });
      return;
    }

    setLoading(true);
    
    try {
      // Verify the reference number
      const result = referenceNumberService.verifyReferenceNumber(referenceNumber.trim());
      setVerificationResult(result);

      // If valid, get full session data
      if (result.valid) {
        const fullData = referenceNumberService.getSessionData(referenceNumber.trim());
        setSessionData(fullData);
      } else {
        setSessionData(null);
      }
    } catch (error) {
      setVerificationResult({
        exists: false,
        valid: false,
        message: 'Error during verification',
        error: error.message
      });
      setSessionData(null);
    } finally {
      setLoading(false);
    }
  };

  const handleClear = () => {
    setReferenceNumber('');
    setVerificationResult(null);
    setSessionData(null);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleString();
  };

  return (
    <Box sx={{ p: 3 }}>
      <Paper elevation={3} sx={{ p: 3, mb: 3 }}>
        <Typography variant="h5" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <SearchIcon color="primary" />
          Reference Number Verification System
        </Typography>
        
        <Typography variant="body2" color="text.secondary" paragraph>
          Enter a reference number to verify its validity and view associated session data.
          This system is used for consent withdrawal and data verification purposes.
        </Typography>

        {/* Verification Input */}
        <Box sx={{ mb: 3 }}>
          <TextField
            fullWidth
            label="Reference Number"
            placeholder="ICU-XXXXXXXXX-XXXXXX"
            value={referenceNumber}
            onChange={(e) => setReferenceNumber(e.target.value.toUpperCase())}
            sx={{ mb: 2 }}
            helperText="Enter the reference number exactly as provided to the user"
          />
          
          <Box sx={{ display: 'flex', gap: 2 }}>
            <Button
              variant="contained"
              onClick={handleVerify}
              disabled={loading}
              startIcon={<SearchIcon />}
            >
              {loading ? 'Verifying...' : 'Verify Reference Number'}
            </Button>
            
            <Button
              variant="outlined"
              onClick={handleClear}
              disabled={loading}
            >
              Clear
            </Button>
          </Box>
        </Box>

        {/* Verification Result */}
        {verificationResult && (
          <Alert 
            severity={verificationResult.valid ? 'success' : 'error'}
            icon={verificationResult.valid ? <VerifiedIcon /> : <ErrorIcon />}
            sx={{ mb: 3 }}
          >
            <Typography variant="subtitle2">
              {verificationResult.message}
            </Typography>
            {verificationResult.basicInfo && (
              <Box sx={{ mt: 1 }}>
                <Typography variant="body2">
                  Session completed: {formatDate(verificationResult.basicInfo.timestamp)}
                </Typography>
                <Typography variant="body2">
                  Total recordings: {verificationResult.basicInfo.totalRecordings}
                </Typography>
                <Typography variant="body2">
                  Phrases completed: {verificationResult.basicInfo.phrasesCompleted}
                </Typography>
                <Typography variant="body2">
                  Session duration: {verificationResult.basicInfo.sessionDuration}
                </Typography>
              </Box>
            )}
          </Alert>
        )}

        {/* Detailed Session Data */}
        {sessionData && (
          <Accordion sx={{ mb: 3 }}>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography variant="h6">Detailed Session Information</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Grid container spacing={3}>
                {/* Demographics */}
                <Grid item xs={12} md={6}>
                  <Card variant="outlined">
                    <CardContent>
                      <Typography variant="h6" gutterBottom color="primary">
                        Demographics
                      </Typography>
                      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                        <Chip label={`Age Group: ${sessionData.demographics.ageGroup || 'Not specified'}`} variant="outlined" />
                        <Chip label={`Gender: ${sessionData.demographics.gender || 'Not specified'}`} variant="outlined" />
                        <Chip label={`Ethnicity: ${sessionData.demographics.ethnicity || 'Not specified'}`} variant="outlined" />
                        <Chip label={`User ID: ${sessionData.demographics.userId || 'Not specified'}`} variant="outlined" />
                      </Box>
                    </CardContent>
                  </Card>
                </Grid>

                {/* Session Summary */}
                <Grid item xs={12} md={6}>
                  <Card variant="outlined">
                    <CardContent>
                      <Typography variant="h6" gutterBottom color="primary">
                        Session Summary
                      </Typography>
                      <Typography variant="body2" gutterBottom>
                        <strong>Start Time:</strong> {formatDate(sessionData.sessionStartTime)}
                      </Typography>
                      <Typography variant="body2" gutterBottom>
                        <strong>End Time:</strong> {formatDate(sessionData.sessionEndTime)}
                      </Typography>
                      <Typography variant="body2" gutterBottom>
                        <strong>Duration:</strong> {sessionData.sessionDurationFormatted}
                      </Typography>
                      <Typography variant="body2" gutterBottom>
                        <strong>Total Recordings:</strong> {sessionData.totalRecordingsInSession}
                      </Typography>
                      <Typography variant="body2" gutterBottom>
                        <strong>Phrases Completed:</strong> {sessionData.totalPhrasesCompleted}
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>

                {/* Phrases Recorded */}
                <Grid item xs={12}>
                  <Card variant="outlined">
                    <CardContent>
                      <Typography variant="h6" gutterBottom color="primary">
                        Phrases Recorded
                      </Typography>
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                        {sessionData.phrasesRecorded.map((phrase, index) => (
                          <Chip
                            key={index}
                            label={`${phrase.phraseKey}: ${phrase.recordingCount} recordings`}
                            color={phrase.completed ? 'success' : 'default'}
                            variant={phrase.completed ? 'filled' : 'outlined'}
                          />
                        ))}
                      </Box>
                    </CardContent>
                  </Card>
                </Grid>

                {/* Consent Information */}
                <Grid item xs={12}>
                  <Card variant="outlined" sx={{ bgcolor: '#fff3e0' }}>
                    <CardContent>
                      <Typography variant="h6" gutterBottom color="warning.main">
                        Consent Withdrawal Information
                      </Typography>
                      <Typography variant="body2" gutterBottom>
                        <strong>Can Withdraw:</strong> {sessionData.consentWithdrawalInfo.canWithdraw ? 'Yes' : 'No'}
                      </Typography>
                      <Typography variant="body2">
                        <strong>Instructions:</strong> {sessionData.consentWithdrawalInfo.contactInfo}
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            </AccordionDetails>
          </Accordion>
        )}
      </Paper>

      {/* Statistics Panel */}
      {statistics && (
        <Paper elevation={3} sx={{ p: 3 }}>
          <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <InfoIcon color="primary" />
            System Statistics
          </Typography>
          
          <Grid container spacing={2}>
            <Grid item xs={6} md={3}>
              <Card variant="outlined">
                <CardContent sx={{ textAlign: 'center' }}>
                  <Typography variant="h4" color="primary">
                    {statistics.totalSessions}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Total Sessions
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            
            <Grid item xs={6} md={3}>
              <Card variant="outlined">
                <CardContent sx={{ textAlign: 'center' }}>
                  <Typography variant="h4" color="primary">
                    {statistics.totalRecordings}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Total Recordings
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            
            <Grid item xs={6} md={3}>
              <Card variant="outlined">
                <CardContent sx={{ textAlign: 'center' }}>
                  <Typography variant="h4" color="primary">
                    {statistics.averageRecordingsPerSession}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Avg per Session
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            
            <Grid item xs={6} md={3}>
              <Card variant="outlined">
                <CardContent sx={{ textAlign: 'center' }}>
                  <Typography variant="h6" color="primary">
                    {statistics.newestSession ? formatDate(statistics.newestSession).split(',')[0] : 'N/A'}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Latest Session
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </Paper>
      )}
    </Box>
  );
};

export default ReferenceNumberVerifier;
