import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  FormControl,
  RadioGroup,
  Radio,
  FormControlLabel,
  Button,
  Stepper,
  Step,
  StepLabel,
  FormHelperText,
  Divider
} from '@mui/material';

const DemographicForm = ({ onSubmit, onBackToConsent, currentDemographics }) => {
  const [activeStep, setActiveStep] = useState(0);
  const [demographics, setDemographics] = useState({
    gender: '',
    ageGroup: '',
    ethnicity: ''
  });
  const [errors, setErrors] = useState({});

  useEffect(() => {
    window.scrollTo(0, 0);

    // Use current demographics from parent component if available (for same-session navigation)
    // This allows editing during the session while ensuring fresh start on page refresh
    if (currentDemographics) {
      setDemographics({
        gender: currentDemographics.gender || '',
        ageGroup: currentDemographics.ageGroup || '',
        ethnicity: currentDemographics.ethnicity || ''
      });
      console.log('📝 Demographic form populated with current session data:', currentDemographics);
    } else {
      console.log('📝 Demographic form initialized with empty fields');
    }
  }, [currentDemographics]);

  const handleChange = (event) => {
    const { name, value } = event.target;
    setDemographics(prev => ({
      ...prev,
      [name]: value
    }));

    // Clear error for this field if it exists
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateStep = () => {
    const newErrors = {};
    
    if (activeStep === 0) {
      if (!demographics.gender) {
        newErrors.gender = 'Please select a gender option';
      }
      if (!demographics.ageGroup) {
        newErrors.ageGroup = 'Please select an age group';
      }
    } else if (activeStep === 1 && !demographics.ethnicity) {
      newErrors.ethnicity = 'Please select an ethnic background';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    if (validateStep()) {
      if (activeStep === 1) {
        onSubmit(demographics);
      } else {
        setActiveStep(prev => prev + 1);
      }
    }
  };

  const handleBack = () => {
    if (activeStep === 0) {
      if (typeof onBackToConsent === 'function') {
        onBackToConsent();
      } else {
        console.error('onBackToConsent is not a function');
      }
    } else {
      setActiveStep(prev => prev - 1);
    }
  };

  const steps = ['Gender and Age', 'Ethnic Background'];

  return (
    <Box className="consent-container">
      <Box className="consent-header">
        <Typography variant="h4" component="h1" gutterBottom>
          Demographic Information
        </Typography>
        <Typography variant="subtitle1" color="text.secondary">
          Please provide the following information to help us categorise the data
        </Typography>
      </Box>

      <Stepper activeStep={activeStep} sx={{ mb: 4 }}>
        {steps.map((label) => (
          <Step key={label}>
            <StepLabel>{label}</StepLabel>
          </Step>
        ))}
      </Stepper>

      {activeStep === 0 && (
        <Box className="consent-section">
          <Typography variant="h6" gutterBottom color="primary">
            Gender and Age Information
          </Typography>
          
          <Box sx={{ mb: 4 }}>
            <Typography variant="subtitle1" fontWeight="medium" gutterBottom>
              Gender
            </Typography>
            <Typography paragraph color="text.secondary">
              Please select your gender identity:
            </Typography>
            
            <FormControl component="fieldset" error={!!errors.gender} sx={{ width: '100%' }}>
              <RadioGroup
                name="gender"
                value={demographics.gender}
                onChange={handleChange}
                row
              >
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2, width: '100%' }}>
                  {['male', 'female', 'nonbinary'].map((gender) => (
                    <Paper
                      key={gender}
                      elevation={1}
                      sx={{
                        p: 2,
                        borderRadius: 2,
                        border: demographics.gender === gender ? '2px solid #009688' : '1px solid rgba(0,0,0,0.12)',
                        bgcolor: demographics.gender === gender ? 'rgba(0,150,136,0.05)' : 'transparent',
                        flex: '1 0 30%',
                        minWidth: '150px',
                        cursor: 'pointer'
                      }}
                    >
                      <FormControlLabel
                        value={gender}
                        control={<Radio color="primary" />}
                        label={{
                          'male': 'Male',
                          'female': 'Female',
                          'nonbinary': 'Non-binary'
                        }[gender]}
                      />
                    </Paper>
                  ))}
                </Box>
              </RadioGroup>
              {errors.gender && <FormHelperText error>{errors.gender}</FormHelperText>}
            </FormControl>
          </Box>
          
          <Box sx={{ mb: 4 }}>
            <Typography variant="subtitle1" fontWeight="medium" gutterBottom>
              Age Group
            </Typography>
            <Typography paragraph color="text.secondary">
              Please select your age group:
            </Typography>
            
            <FormControl component="fieldset" error={!!errors.ageGroup} sx={{ width: '100%' }}>
              <RadioGroup
                name="ageGroup"
                value={demographics.ageGroup}
                onChange={handleChange}
                row
              >
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2, width: '100%' }}>
                  {['18to39', '40to64', '65plus'].map((ageGroup) => (
                    <Paper
                      key={ageGroup}
                      elevation={1}
                      sx={{
                        p: 2,
                        borderRadius: 2,
                        border: demographics.ageGroup === ageGroup ? '2px solid #009688' : '1px solid rgba(0,0,0,0.12)',
                        bgcolor: demographics.ageGroup === ageGroup ? 'rgba(0,150,136,0.05)' : 'transparent',
                        flex: '1 0 30%',
                        minWidth: '150px',
                        cursor: 'pointer'
                      }}
                    >
                      <FormControlLabel
                        value={ageGroup}
                        control={<Radio color="primary" />}
                        label={{
                          '18to39': '18 to 39 years',
                          '40to64': '40 to 64 years',
                          '65plus': '65 years and over'
                        }[ageGroup]}
                      />
                    </Paper>
                  ))}
                </Box>
              </RadioGroup>
              {errors.ageGroup && <FormHelperText error>{errors.ageGroup}</FormHelperText>}
            </FormControl>
          </Box>
        </Box>
      )}

      {activeStep === 1 && (
        <Box className="consent-section">
          <Typography variant="h6" gutterBottom color="primary">
            Ethnic Background
          </Typography>
          
          <Typography paragraph color="text.secondary">
            Please select your ethnic background:
          </Typography>
          
          <FormControl component="fieldset" error={!!errors.ethnicity} sx={{ width: '100%' }}>
            <RadioGroup
              name="ethnicity"
              value={demographics.ethnicity}
              onChange={handleChange}
            >
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                {[
                  { value: 'caucasian', label: 'Caucasian / White' },
                  { value: 'asian', label: 'Asian (e.g. South Asian, East Asian, Southeast Asian)' },
                  { value: 'african', label: 'African Descent / Black' },
                  { value: 'latinx', label: 'Hispanic / Latin American' },
                  { value: 'middle_eastern', label: 'Middle Eastern / North African' },
                  { value: 'pacific_islander', label: 'Pacific Islander' },
                  { value: 'aboriginal', label: 'Aboriginal / Torres Strait Islander' },
                  { value: 'indigenous', label: 'Indigenous from overseas (i.e. Canadian Inuit)' },
                  { value: 'mixed', label: 'Mixed / Multiple ethnicities' }
                ].map(({ value, label }) => (
                  <Paper
                    key={value}
                    elevation={1}
                    sx={{
                      p: 2,
                      borderRadius: 2,
                      border: demographics.ethnicity === value ? '2px solid #009688' : '1px solid rgba(0,0,0,0.12)',
                      bgcolor: demographics.ethnicity === value ? 'rgba(0,150,136,0.05)' : 'transparent',
                      cursor: 'pointer'
                    }}
                  >
                    <FormControlLabel
                      value={value}
                      control={<Radio color="primary" />}
                      label={label}
                    />
                  </Paper>
                ))}
              </Box>
            </RadioGroup>
            {errors.ethnicity && <FormHelperText error>{errors.ethnicity}</FormHelperText>}
          </FormControl>
        </Box>
      )}

      <Divider sx={{ my: 3 }} />

      <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 3 }}>
        <Button
          variant="contained"
          onClick={handleBack}
          disabled={false} // Always allow navigation back
          sx={{
            px: 3,
            ...(activeStep === 0
              ? { bgcolor: '#f44336', color: 'white', '&:hover': { bgcolor: '#d32f2f' } }
              : {})
          }}
        >
          {activeStep === 0 ? 'Back to Consent' : 'Back'}
        </Button>
        <Button
          variant="contained"
          color="primary"
          onClick={handleNext}
          disabled={false} // Always allow navigation forward
          sx={{ px: 4 }}
        >
          {activeStep === steps.length - 1 ? 'Submit' : 'Next'}
        </Button>
      </Box>
    </Box>
  );
};

export default DemographicForm;
