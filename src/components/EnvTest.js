import React, { useEffect, useState } from 'react';
import { Box, Typography, Paper } from '@mui/material';

const EnvTest = () => {
  const [envVars, setEnvVars] = useState({
    connectionString: 'Not found',
    containerName: 'Not found',
    recordingDuration: 'Not found'
  });

  useEffect(() => {
    setEnvVars({
      identityPoolId: process.env.REACT_APP_AWS_IDENTITY_POOL_ID ? 
        'Found (ID: ' + process.env.REACT_APP_AWS_IDENTITY_POOL_ID + ')' : 
        'Not found',
      region: process.env.REACT_APP_AWS_REGION || 'ap-southeast-2',
      bucket: process.env.REACT_APP_AWS_S3_BUCKET || 'icudatasetphrasesfortesting',
      recordingDuration: process.env.REACT_APP_RECORDING_DURATION || 'Not found'
    });
  }, []);

  return (
    <Paper elevation={3} sx={{ p: 3, m: 2 }}>
      <Typography variant="h5" gutterBottom>Environment Variables Test</Typography>
      <Box sx={{ mt: 2 }}>
        <Typography><strong>Connection String:</strong> {envVars.connectionString}</Typography>
        <Typography><strong>Container Name:</strong> {envVars.containerName}</Typography>
        <Typography><strong>Recording Duration:</strong> {envVars.recordingDuration}</Typography>
      </Box>
    </Paper>
  );
};

export default EnvTest;
