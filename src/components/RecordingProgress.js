import React from 'react';
import {
  Box,
  Typography,
  Paper,
  LinearProgress,
  Tooltip,
  IconButton,
  Collapse,
  List,
  ListItem,
  ListItemText,
  ListItemIcon
} from '@mui/material';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import { phraseCollectionConfig } from '../phrases';
import RadioButtonUncheckedIcon from '@mui/icons-material/RadioButtonUnchecked';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import { styled } from '@mui/material/styles';

// Styled component for the expand button rotation
const ExpandButton = styled(IconButton)(({ theme, expanded }) => ({
  transform: expanded ? 'rotate(180deg)' : 'rotate(0deg)',
  transition: theme.transitions.create('transform', {
    duration: theme.transitions.duration.shortest,
  }),
}));

const RecordingProgress = ({ categories, phrases, recordings }) => {
  const [expandedCategories, setExpandedCategories] = React.useState({});

  // Get recordings per phrase from configuration
  const RECORDINGS_PER_PHRASE = phraseCollectionConfig.recordingsPerPhrase;

  // Debug logging for props
  React.useEffect(() => {
    console.log('🎯 RecordingProgress component received props:');
    console.log('  categories:', categories);
    console.log('  phrases:', phrases);
    console.log('  recordings:', recordings);
  }, [categories, phrases, recordings]);

  // Calculate progress statistics in real-time
  const calculateStats = React.useCallback(() => {
    console.log('🧮 RecordingProgress calculateStats called');
    console.log('  Input recordings:', recordings);
    console.log('  Input phrases:', phrases);
    console.log('  Input categories:', categories);

    // Convert flat recordings structure to nested structure
    const nestedRecordings = {};
    Object.entries(recordings || {}).forEach(([key, count]) => {
      if (key.includes(':')) {
        const [category, phrase] = key.split(':');
        if (!nestedRecordings[category]) {
          nestedRecordings[category] = {};
        }
        nestedRecordings[category][phrase] = count;
      }
    });

    console.log('  Converted nestedRecordings:', nestedRecordings);

    const totalPhrases = Object.values(phrases || {}).reduce((sum, arr) => sum + arr.length, 0);
    const completedPhrases = Object.entries(nestedRecordings).reduce((sum, [category, catRecordings]) => {
      return sum + Object.values(catRecordings).filter(count => count >= RECORDINGS_PER_PHRASE).length;
    }, 0);

    console.log('  Calculated totalPhrases:', totalPhrases);
    console.log('  Calculated completedPhrases:', completedPhrases);
    
    const totalRecordings = Object.entries(nestedRecordings).reduce((sum, [_, catRecordings]) => {
      return sum + Object.values(catRecordings).reduce((catSum, count) => catSum + count, 0);
    }, 0);

    const categoryStats = categories.reduce((acc, category) => {
      const categoryPhrases = phrases[category] || [];
      const categoryRecordings = nestedRecordings[category] || {};
      const completedInCategory = Object.values(categoryRecordings)
        .filter(count => count >= RECORDINGS_PER_PHRASE).length;
      
      acc[category] = {
        total: categoryPhrases.length,
        completed: completedInCategory,
        percentage: categoryPhrases.length ? 
          (completedInCategory / categoryPhrases.length) * 100 : 0
      };
      return acc;
    }, {});
    
    const result = {
      totalPhrases,
      completedPhrases,
      totalRecordings,
      percentComplete: totalPhrases ? Math.round((completedPhrases / totalPhrases) * 100) : 0,
      categoryStats,
      nestedRecordings
    };

    console.log('  Final calculated stats:', result);
    return result;
  }, [recordings, phrases, categories]);

  const [stats, setStats] = React.useState(calculateStats());

  // Update stats whenever recordings change
  React.useEffect(() => {
    console.log('🔄 RecordingProgress useEffect triggered - updating stats');
    setStats(calculateStats());
  }, [calculateStats]);

  // Toggle category expansion
  const toggleCategory = (category) => {
    setExpandedCategories(prev => ({
      ...prev,
      [category]: !prev[category]
    }));
  };
  
  // Add safety check for empty data
  if (!recordings || Object.keys(recordings).length === 0) {
    console.log('⚠️ RecordingProgress: No recordings data available');
    return (
      <Box sx={{ mb: 2 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
          <Typography variant="subtitle1" sx={{ fontWeight: 'medium' }}>
            Recording Progress
          </Typography>
          <Typography variant="body2" color="text.secondary">
            No recordings yet - start recording to see progress
          </Typography>
        </Box>
        <LinearProgress
          variant="determinate"
          value={0}
          sx={{
            height: 6,
            borderRadius: 3,
            backgroundColor: 'rgba(0, 150, 136, 0.1)',
            '& .MuiLinearProgress-bar': {
              backgroundColor: '#009688'
            }
          }}
        />
      </Box>
    );
  }

  return (
    <Box sx={{ mb: 2 }}>

      {/* Overall progress - Compact version for bottom bar */}
      <Box sx={{ mb: 2 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
          <Typography variant="subtitle1" sx={{ fontWeight: 'medium' }}>
            Recording Progress
          </Typography>
          <Typography variant="body2" color="text.secondary">
            {stats.completedPhrases}/{stats.totalPhrases} phrases completed ({Math.round(stats.percentComplete)}%)
          </Typography>
        </Box>
        <Tooltip title={`${Math.round(stats.percentComplete)}% complete`}>
          <LinearProgress
            variant="determinate"
            value={stats.percentComplete}
            sx={{
              height: 6,
              borderRadius: 3,
              backgroundColor: 'rgba(0, 150, 136, 0.1)',
              '& .MuiLinearProgress-bar': {
                backgroundColor: '#009688'
              }
            }}
          />
        </Tooltip>
      </Box>

      {/* Category-wise progress - Compact horizontal layout */}
      <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
        {categories.map((category) => {
          const categoryData = stats.categoryStats[category] || { completed: 0, total: 0, percentage: 0 };
          return (
            <Box key={category} sx={{ flex: 1, minWidth: '200px' }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
                <Typography variant="body2" sx={{ fontWeight: 'medium', mr: 1 }}>
                  {category}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  ({categoryData.completed}/{categoryData.total})
                </Typography>
              </Box>
              <LinearProgress
                variant="determinate"
                value={categoryData.percentage}
                sx={{
                  height: 4,
                  borderRadius: 2,
                  backgroundColor: 'rgba(0, 150, 136, 0.1)',
                  '& .MuiLinearProgress-bar': {
                    backgroundColor: '#009688'
                  }
                }}
              />
            </Box>
          );
        })}
      </Box>
    </Box>
  );
};

export default RecordingProgress;
