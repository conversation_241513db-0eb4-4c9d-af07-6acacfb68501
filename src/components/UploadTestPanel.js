import React, { useState } from 'react';
import { 
  Box, 
  Button, 
  Typography, 
  Paper, 
  Alert, 
  CircularProgress,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemIcon
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import ErrorIcon from '@mui/icons-material/Error';
import WarningIcon from '@mui/icons-material/Warning';
import InfoIcon from '@mui/icons-material/Info';
import { uploadDebugger, debugUploadProcess } from '../utils/uploadDebugger';

const UploadTestPanel = () => {
  const [testing, setTesting] = useState(false);
  const [testResults, setTestResults] = useState(null);
  const [debugReport, setDebugReport] = useState(null);

  // Create a mock video blob for testing
  const createMockVideoBlob = () => {
    // Create a simple mock video blob (1KB of data)
    const mockData = new Uint8Array(1024);
    for (let i = 0; i < mockData.length; i++) {
      mockData[i] = Math.floor(Math.random() * 256);
    }
    return new Blob([mockData], { type: 'video/webm' });
  };

  // Mock demographics data
  const mockDemographics = {
    userId: 'test01',
    ageGroup: '40to64',
    gender: 'female',
    ethnicity: 'caucasian',
    category: 'test',
    recordingNumber: 1
  };

  const runUploadTest = async () => {
    setTesting(true);
    setTestResults(null);
    setDebugReport(null);

    try {
      console.log('🧪 Starting upload functionality test...');
      
      // Create mock video blob
      const mockVideoBlob = createMockVideoBlob();
      const testPhrase = 'test_phrase_upload_verification';

      // Run debug process
      const report = await debugUploadProcess(
        mockVideoBlob,
        testPhrase,
        mockDemographics,
        'test',
        1
      );

      setDebugReport(report);

      // Test configuration checks
      const configTests = {
        frontendAWSConfig: {
          name: 'Frontend AWS Configuration',
          passed: !!(process.env.REACT_APP_AWS_IDENTITY_POOL_ID && 
                    process.env.REACT_APP_AWS_REGION && 
                    process.env.REACT_APP_S3_BUCKET),
          details: {
            identityPoolId: process.env.REACT_APP_AWS_IDENTITY_POOL_ID || 'Not set',
            region: process.env.REACT_APP_AWS_REGION || 'Not set',
            bucket: process.env.REACT_APP_S3_BUCKET || 'Not set'
          }
        },
        backendEndpoint: {
          name: 'Backend Upload Endpoint',
          passed: false,
          details: {}
        },
        videoBlobValidation: {
          name: 'Video Blob Validation',
          passed: mockVideoBlob.size > 0 && mockVideoBlob.type.includes('video'),
          details: {
            size: mockVideoBlob.size,
            type: mockVideoBlob.type,
            sizeInMB: (mockVideoBlob.size / (1024 * 1024)).toFixed(2)
          }
        },
        demographicsValidation: {
          name: 'Demographics Validation',
          passed: !!(mockDemographics.userId && mockDemographics.ageGroup && 
                    mockDemographics.gender && mockDemographics.ethnicity),
          details: mockDemographics
        }
      };

      // Test backend endpoint availability
      try {
        const backendUrl = process.env.REACT_APP_BACKEND_URL || 'http://localhost:5000';
        const response = await fetch(`${backendUrl}/health`);
        configTests.backendEndpoint.passed = response.ok;
        configTests.backendEndpoint.details = {
          status: response.status,
          statusText: response.statusText,
          available: response.ok,
          url: `${backendUrl}/health`
        };
      } catch (error) {
        configTests.backendEndpoint.details = {
          error: error.message,
          available: false
        };
      }

      // Test actual upload (if backend is available)
      let uploadTest = {
        name: 'Upload Functionality',
        passed: false,
        details: {}
      };

      if (configTests.backendEndpoint.passed) {
        try {
          const formData = new FormData();
          formData.append('video', mockVideoBlob, `test_${Date.now()}.webm`);
          formData.append('phrase', testPhrase);
          formData.append('category', 'test');
          formData.append('recordingNumber', '1');
          formData.append('demographics', JSON.stringify(mockDemographics));

          const backendUrl = process.env.REACT_APP_BACKEND_URL || 'http://localhost:5000';
          const uploadResponse = await fetch(`${backendUrl}/upload`, {
            method: 'POST',
            body: formData
          });

          const uploadResult = await uploadResponse.json();
          
          uploadTest.passed = uploadResponse.ok && uploadResult.success;
          uploadTest.details = {
            status: uploadResponse.status,
            response: uploadResult,
            s3Path: uploadResult.filePath,
            url: uploadResult.url
          };

        } catch (error) {
          uploadTest.details = {
            error: error.message,
            failed: true
          };
        }
      } else {
        uploadTest.details = {
          skipped: 'Backend not available',
          reason: 'Backend endpoint test failed'
        };
      }

      setTestResults({
        ...configTests,
        uploadTest,
        summary: {
          totalTests: Object.keys(configTests).length + 1,
          passedTests: Object.values(configTests).filter(test => test.passed).length + (uploadTest.passed ? 1 : 0),
          timestamp: new Date().toISOString()
        }
      });

    } catch (error) {
      console.error('Upload test failed:', error);
      setTestResults({
        error: error.message,
        timestamp: new Date().toISOString()
      });
    } finally {
      setTesting(false);
    }
  };

  const getTestIcon = (passed) => {
    if (passed === true) return <CheckCircleIcon color="success" />;
    if (passed === false) return <ErrorIcon color="error" />;
    return <WarningIcon color="warning" />;
  };

  const getTestColor = (passed) => {
    if (passed === true) return 'success';
    if (passed === false) return 'error';
    return 'warning';
  };

  return (
    <Paper elevation={3} sx={{ p: 3, mb: 3 }}>
      <Typography variant="h6" gutterBottom>
        🧪 Upload Functionality Test Panel
      </Typography>
      
      <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
        This panel tests the complete video upload pipeline including configuration, 
        backend connectivity, and S3 upload functionality.
      </Typography>

      <Button 
        variant="contained" 
        onClick={runUploadTest}
        disabled={testing}
        startIcon={testing ? <CircularProgress size={20} /> : null}
        sx={{ mb: 2 }}
      >
        {testing ? 'Running Tests...' : 'Run Upload Test'}
      </Button>

      {testResults && (
        <Box sx={{ mt: 2 }}>
          {testResults.error ? (
            <Alert severity="error" sx={{ mb: 2 }}>
              Test failed: {testResults.error}
            </Alert>
          ) : (
            <>
              <Alert 
                severity={testResults.summary?.passedTests === testResults.summary?.totalTests ? 'success' : 'warning'}
                sx={{ mb: 2 }}
              >
                Test Summary: {testResults.summary?.passedTests}/{testResults.summary?.totalTests} tests passed
              </Alert>

              {Object.entries(testResults).map(([key, test]) => {
                if (key === 'summary' || key === 'error') return null;
                
                return (
                  <Accordion key={key} sx={{ mb: 1 }}>
                    <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        {getTestIcon(test.passed)}
                        <Typography>{test.name}</Typography>
                        <Chip 
                          label={test.passed ? 'PASS' : 'FAIL'} 
                          color={getTestColor(test.passed)}
                          size="small"
                        />
                      </Box>
                    </AccordionSummary>
                    <AccordionDetails>
                      <Typography variant="body2" component="pre" sx={{ whiteSpace: 'pre-wrap' }}>
                        {JSON.stringify(test.details, null, 2)}
                      </Typography>
                    </AccordionDetails>
                  </Accordion>
                );
              })}
            </>
          )}
        </Box>
      )}

      {debugReport && (
        <Accordion sx={{ mt: 2 }}>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <InfoIcon color="info" />
              <Typography>Debug Report</Typography>
              <Chip 
                label={`${debugReport.totalLogs} logs, ${debugReport.errors?.length || 0} errors`}
                color="info"
                size="small"
              />
            </Box>
          </AccordionSummary>
          <AccordionDetails>
            <Typography variant="body2" component="pre" sx={{ whiteSpace: 'pre-wrap', maxHeight: 400, overflow: 'auto' }}>
              {JSON.stringify(debugReport, null, 2)}
            </Typography>
            <Button 
              variant="outlined" 
              size="small" 
              onClick={() => uploadDebugger.exportLogs()}
              sx={{ mt: 1 }}
            >
              Export Debug Logs
            </Button>
          </AccordionDetails>
        </Accordion>
      )}
    </Paper>
  );
};

export default UploadTestPanel;
