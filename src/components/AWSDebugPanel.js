import React, { useState, useEffect } from 'react';
import { Box, Typography, Paper, Chip, Alert } from '@mui/material';
import { testAWSConnection } from '../services/awsStorage';

const AWSDebugPanel = () => {
  const [awsStatus, setAwsStatus] = useState({ loading: true, result: null });

  useEffect(() => {
    const checkAWS = async () => {
      try {
        const result = await testAWSConnection();
        setAwsStatus({ loading: false, result });
      } catch (error) {
        setAwsStatus({ 
          loading: false, 
          result: { success: false, error: error.message } 
        });
      }
    };

    checkAWS();
  }, []);

  const isAWSConfigured = () => {
    return process.env.REACT_APP_AWS_IDENTITY_POOL_ID &&
           process.env.REACT_APP_AWS_IDENTITY_POOL_ID !== 'your-identity-pool-id-here';
  };

  return (
    <Paper sx={{ p: 2, mb: 2, bgcolor: 'grey.50' }}>
      <Typography variant="h6" gutterBottom>
        🔍 AWS Configuration Debug
      </Typography>
      
      <Box sx={{ mb: 2 }}>
        <Typography variant="subtitle2" gutterBottom>Environment Variables:</Typography>
        <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 2fr', gap: 1, fontSize: '0.75rem' }}>
          <Typography><strong>Identity Pool:</strong></Typography>
          <Typography sx={{ wordBreak: 'break-all', fontFamily: 'monospace' }}>
            {process.env.REACT_APP_AWS_IDENTITY_POOL_ID || 'Not set'}
          </Typography>
          
          <Typography><strong>Region:</strong></Typography>
          <Typography sx={{ fontFamily: 'monospace' }}>
            {process.env.REACT_APP_AWS_REGION || 'Not set'}
          </Typography>
          
          <Typography><strong>S3 Bucket:</strong></Typography>
          <Typography sx={{ fontFamily: 'monospace' }}>
            {process.env.REACT_APP_S3_BUCKET || 'Not set'}
          </Typography>
        </Box>
      </Box>

      <Box sx={{ mb: 2 }}>
        <Typography variant="subtitle2" gutterBottom>Configuration Status:</Typography>
        <Chip 
          label={isAWSConfigured() ? 'AWS Configured' : 'AWS Not Configured'}
          color={isAWSConfigured() ? 'success' : 'warning'}
          size="small"
          sx={{ mr: 1 }}
        />
        <Chip 
          label={isAWSConfigured() ? 'Real Uploads' : 'Simulation Mode'}
          color={isAWSConfigured() ? 'primary' : 'default'}
          size="small"
        />
      </Box>

      <Box sx={{ mb: 2 }}>
        <Typography variant="subtitle2" gutterBottom>Connection Test:</Typography>
        {awsStatus.loading ? (
          <Chip label="Testing..." color="info" size="small" />
        ) : awsStatus.result?.success ? (
          <Alert severity="success" sx={{ py: 0 }}>
            ✅ Connection successful! Bucket: {awsStatus.result.bucket}
          </Alert>
        ) : (
          <Alert severity="error" sx={{ py: 0 }}>
            ❌ Connection failed: {awsStatus.result?.error}
          </Alert>
        )}
      </Box>

      <Typography variant="caption" color="text.secondary">
        This panel shows the current AWS configuration and connection status.
        If "AWS Not Configured" is shown, uploads will be simulated.
        If "Connection failed" is shown, check your AWS credentials and permissions.
      </Typography>
    </Paper>
  );
};

export default AWSDebugPanel;
