/**
 * AutoAdvanceTest - Test component to debug auto-advance functionality
 * This component simulates recording completion to test auto-advance logic
 */

import React, { useState } from 'react';
import { Button, Box, Typography, Paper } from '@mui/material';
import { useRecordingSession } from '../providers/RecordingSessionProvider';

const AutoAdvanceTest = () => {
  const {
    selectedPhrases,
    currentPhraseIndex,
    currentRecordingNumber,
    recordingsCount,
    currentPhrase,
    RECORDINGS_PER_PHRASE,
    recordingCompleted,
    handleNextPhrase
  } = useRecordingSession();

  const [testLog, setTestLog] = useState([]);

  const addLog = (message) => {
    const timestamp = new Date().toLocaleTimeString();
    setTestLog(prev => [...prev, `[${timestamp}] ${message}`]);
    console.log(`🧪 TEST: ${message}`);
  };

  const simulateRecordingCompletion = () => {
    if (!selectedPhrases || selectedPhrases.length === 0) {
      addLog('❌ No phrases selected for testing');
      return;
    }

    const currentPhraseObj = selectedPhrases[currentPhraseIndex];
    if (!currentPhraseObj) {
      addLog('❌ No current phrase object found');
      return;
    }

    const metadata = {
      phrase: currentPhraseObj.phrase,
      category: currentPhraseObj.category,
      recordingNumber: currentRecordingNumber
    };

    addLog(`📹 Simulating recording completion for: "${currentPhraseObj.phrase}"`);
    addLog(`📊 Current recording number: ${currentRecordingNumber}`);
    addLog(`📊 Current phrase index: ${currentPhraseIndex}`);
    addLog(`📊 Current recordings count before: ${JSON.stringify(recordingsCount)}`);

    // Call the recording completed function
    recordingCompleted(metadata);

    addLog(`✅ Recording completion simulated`);

    // Check state after a short delay
    setTimeout(() => {
      addLog(`📊 Current recordings count after: ${JSON.stringify(recordingsCount)}`);
      addLog(`📊 Current phrase index after: ${currentPhraseIndex}`);
    }, 100);
  };

  const getCurrentPhraseStatus = () => {
    if (!selectedPhrases || selectedPhrases.length === 0) return null;
    
    const currentPhraseObj = selectedPhrases[currentPhraseIndex];
    if (!currentPhraseObj) return null;

    const phraseKey = `${currentPhraseObj.category}:${currentPhraseObj.phrase}`;
    const currentCount = recordingsCount[phraseKey] || 0;

    return {
      phrase: currentPhraseObj.phrase,
      category: currentPhraseObj.category,
      currentCount,
      required: RECORDINGS_PER_PHRASE,
      remaining: Math.max(0, RECORDINGS_PER_PHRASE - currentCount),
      isComplete: currentCount >= RECORDINGS_PER_PHRASE
    };
  };

  const status = getCurrentPhraseStatus();

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        🧪 Auto-Advance Test Component
      </Typography>

      <Paper sx={{ p: 2, mb: 2 }}>
        <Typography variant="h6" gutterBottom>Current Status</Typography>
        {status ? (
          <Box>
            <Typography>Phrase: "{status.phrase}"</Typography>
            <Typography>Category: {status.category}</Typography>
            <Typography>Recordings: {status.currentCount}/{status.required}</Typography>
            <Typography>Remaining: {status.remaining}</Typography>
            <Typography>Complete: {status.isComplete ? '✅ Yes' : '❌ No'}</Typography>
            <Typography>Current Phrase Index: {currentPhraseIndex}</Typography>
            <Typography>Total Phrases: {selectedPhrases?.length || 0}</Typography>
          </Box>
        ) : (
          <Typography>No phrases selected</Typography>
        )}
      </Paper>

      <Paper sx={{ p: 2, mb: 2 }}>
        <Typography variant="h6" gutterBottom>Test Actions</Typography>
        <Button 
          variant="contained" 
          onClick={simulateRecordingCompletion}
          disabled={!status}
          sx={{ mr: 1 }}
        >
          Simulate Recording Completion
        </Button>
        <Button
          variant="outlined"
          onClick={() => setTestLog([])}
        >
          Clear Log
        </Button>
        <Button
          variant="contained"
          color="secondary"
          onClick={() => {
            addLog('🚀 Testing handleNextPhrase directly');
            handleNextPhrase();
          }}
          disabled={!status}
          sx={{ ml: 1 }}
        >
          Test handleNextPhrase
        </Button>
      </Paper>

      <Paper sx={{ p: 2, mb: 2 }}>
        <Typography variant="h6" gutterBottom>All Recordings Count</Typography>
        <Box sx={{ fontFamily: 'monospace', fontSize: '0.8rem' }}>
          {Object.entries(recordingsCount).map(([phraseKey, count]) => (
            <Typography key={phraseKey}>
              {phraseKey}: {count}
            </Typography>
          ))}
        </Box>
      </Paper>

      <Paper sx={{ p: 2 }}>
        <Typography variant="h6" gutterBottom>Test Log</Typography>
        <Box 
          sx={{ 
            fontFamily: 'monospace', 
            fontSize: '0.8rem',
            maxHeight: 300,
            overflow: 'auto',
            backgroundColor: '#f5f5f5',
            p: 1,
            borderRadius: 1
          }}
        >
          {testLog.length === 0 ? (
            <Typography>No test actions performed yet</Typography>
          ) : (
            testLog.map((log, index) => (
              <Typography key={index}>{log}</Typography>
            ))
          )}
        </Box>
      </Paper>
    </Box>
  );
};

export default AutoAdvanceTest;
