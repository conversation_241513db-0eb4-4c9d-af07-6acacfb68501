import React from 'react';
import { Box, Typography } from '@mui/material';

const OvalGuide = ({ isRecording }) => {
  // Determine if device is mobile
  const isMobile = /iPhone|iPad|iPod|Android/i.test(navigator.userAgent);
  
  return (
    <Box
      className="oval-guide"
      sx={{
        // Simple oval shape
        width: { xs: '260px', sm: '300px', md: '350px', lg: '380px' },
        height: { xs: '325px', sm: '375px', md: '440px', lg: '480px' },
        border: '3px dashed',
        borderColor: isRecording ? 'error.main' : 'primary.main',
        borderRadius: '50% / 60%', // Oval shape (more height than width)
        position: 'absolute',
        top: '50%',
        left: '50%',
        transform: 'translate(-50%, -50%)',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        zIndex: 10,
        opacity: isRecording ? 0.9 : 0.8,
        transition: 'all 0.3s ease',
      }}
    >
      {/* Clear instruction text above the oval */}
      <Typography
        variant="h6"
        sx={{
          position: 'absolute',
          top: '-50px',
          left: '50%',
          transform: 'translateX(-50%)',
          color: 'white',
          backgroundColor: 'rgba(0, 150, 136, 0.9)',
          padding: '8px 16px',
          borderRadius: '4px',
          fontWeight: 'bold',
          textAlign: 'center',
          width: '100%',
          maxWidth: '300px',
          fontSize: { xs: '16px', sm: '18px', md: '20px' },
          boxShadow: '0 2px 4px rgba(0,0,0,0.2)',
          zIndex: 15
        }}
      >
        Position Face in Oval
      </Typography>
      
      {/* Simple instruction text inside the oval */}
      <Box
        sx={{
          position: 'absolute',
          bottom: '15%',
          left: '50%',
          transform: 'translateX(-50%)',
          color: 'white',
          fontSize: { xs: '16px', sm: '18px', md: '20px' },
          fontWeight: 'bold',
          textAlign: 'center',
          width: '80%',
          opacity: 1,
          pointerEvents: 'none',
          textShadow: '0px 0px 4px rgba(0,0,0,0.8)',
          display: 'block',
          padding: '8px',
          backgroundColor: 'rgba(0, 150, 136, 0.8)',
          borderRadius: '4px',
          zIndex: 15
        }}
      >
        {isRecording ? 'Speaking...' : 'Ready'}
      </Box>
      
      {isRecording && (
        <Box
          className="recording-indicator"
          sx={{
            position: 'absolute',
            top: '-15px',
            right: '-15px',
            width: '20px',
            height: '20px',
            borderRadius: '50%',
            bgcolor: 'error.main',
            animation: 'pulse 1s infinite',
          }}
        />
      )}
    </Box>
  );
};

export default OvalGuide;
