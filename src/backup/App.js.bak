import React, { useState, useEffect } from 'react';
import { Container, Typography, Box, Paper, Snackbar, Alert, CircularProgress, Button } from '@mui/material';
import './App.css';
import { phrases, phraseCollectionConfig } from './phrases';

// Import components
import ConsentPage from './components/ConsentPage';
import DemographicForm from './components/DemographicForm';
import CategorySelector from './components/CategorySelector';
import PhraseDisplay from './components/PhraseDisplay';
import VideoRecorder from './components/VideoRecorder';
import PhraseNavigation from './components/PhraseNavigation';
import ProgressTracker from './components/ProgressTracker';
import CollectionTracker from './components/CollectionTracker';
import PhraseSelector from './components/PhraseSelector';
import OvalGuide from './components/OvalGuide';

// Import services
import { uploadVideoToAzure } from './services/azureStorage';
import { 
  getSelectedPhrases,
  saveSelectedPhrases,
  hasSelectedPhrases,
  clearSelectedPhrases,
  getCompletionPercentage,
  getTotalRecordings,
  getMaxPhrasesPerParticipant
} from './services/phraseRotationService';

import React, { useState, useEffect } from 'react';
import { Container, Typography, Box, Paper, Snackbar, Alert, CircularProgress, Button } from '@mui/material';
import './App.css';
import { phrases, phraseCollectionConfig } from './phrases';

// Import components
import ConsentPage from './components/ConsentPage';
import DemographicForm from './components/DemographicForm';
import CategorySelector from './components/CategorySelector';
import PhraseDisplay from './components/PhraseDisplay';
import VideoRecorder from './components/VideoRecorder';
import PhraseNavigation from './components/PhraseNavigation';
import ProgressTracker from './components/ProgressTracker';
import CollectionTracker from './components/CollectionTracker';
import PhraseSelector from './components/PhraseSelector';
import OvalGuide from './components/OvalGuide';

// Import services
import { uploadVideoToAzure } from './services/azureStorage';
import { 
  getSelectedPhrases,
  saveSelectedPhrases,
  hasSelectedPhrases,
  clearSelectedPhrases,
  getCompletionPercentage,
  getTotalRecordings,
  getMaxPhrasesPerParticipant
} from './services/phraseRotationService';

const App = () => {
  const [hasConsent, setHasConsent] = useState(false);
  const [demographicsCompleted, setDemographicsCompleted] = useState(false);
  const [demographicInfo, setDemographicInfo] = useState(null);
  const [selectedCategory, setSelectedCategory] = useState('');
  const [currentPhraseIndex, setCurrentPhraseIndex] = useState(0);
  const [uploading, setUploading] = useState(false);
  const [notification, setNotification] = useState({ open: false, message: '', severity: 'info' });
  const [recordingsCount, setRecordingsCount] = useState({});
  const [currentRecordingNumber, setCurrentRecordingNumber] = useState(1);
  const [showCollectionTracker, setShowCollectionTracker] = useState(false);
  const [sessionRecordingsCount, setSessionRecordingsCount] = useState(0);
  const [selectedPhrases, setSelectedPhrases] = useState(null);
  const [phrasesSelected, setPhrasesSelected] = useState(false);
  const RECORDINGS_PER_PHRASE = phraseCollectionConfig.recordingsPerPhrase;
  const PHRASES_PER_PARTICIPANT = phraseCollectionConfig.phrasesPerParticipant;
  const COLLECTION_GOAL = PHRASES_PER_PARTICIPANT * RECORDINGS_PER_PHRASE;
  
  // Detect if device is mobile or laptop
  const isMobile = /iPhone|iPad|iPod|Android/i.test(navigator.userAgent);
  const isLaptop = !isMobile;

  // Handle consent given
  const handleConsent = () => {
    setHasConsent(true);
    // Store consent in local storage to persist between sessions
    localStorage.setItem('icuAppConsent', 'true');
    setNotification({
      open: true,
      message: 'Thank you for your consent. Please provide your demographic information.',
      severity: 'success'
    });
  };
  
  // Handle going back to consent page
  const handleBackToConsent = () => {
    // Reset consent state
    setHasConsent(false);
    // Remove from local storage
    localStorage.removeItem('icuAppConsent');
    // Also reset demographics to ensure clean state
    setDemographicsCompleted(false);
    console.log('Navigating back to consent page - consent reset');
  };
  
  // Handle demographic form submission
  const handleDemographicSubmit = (demographics) => {
    setDemographicInfo(demographics);
    setDemographicsCompleted(true);
    // Store demographics in local storage to persist between sessions
    localStorage.setItem('icuAppDemographics', JSON.stringify(demographics));
    setNotification({
      open: true,
      message: 'Thank you for providing your demographic information. You can now start recording phrases.',
      severity: 'success'
    });
  };
  
  // Load recordings count and check for saved selected phrases on component mount
  useEffect(() => {
    // Load recordings count
    const savedRecordingsCount = localStorage.getItem('icuAppRecordingsCount');
    if (savedRecordingsCount) {
      setRecordingsCount(JSON.parse(savedRecordingsCount));
    }
    
    // Check if user has already selected phrases
    const savedPhrases = getSelectedPhrases();
    if (savedPhrases) {
      setSelectedPhrases(savedPhrases);
      setPhrasesSelected(true);
      
      // Set the category to the first phrase's category
      if (savedPhrases.length > 0) {
        setSelectedCategory(savedPhrases[0].category);
      }
      
      setNotification({
        open: true,
        message: 'Your previously selected phrases have been loaded.',
        severity: 'info'
      });
    }
  }, []);

  // Check if consent and demographics were previously given
  useEffect(() => {
    const storedConsent = localStorage.getItem('icuAppConsent');
    if (storedConsent === 'true') {
      setHasConsent(true);
      
      // Check for stored demographic information
      const storedDemographics = localStorage.getItem('icuAppDemographics');
      if (storedDemographics) {
        try {
          const parsedDemographics = JSON.parse(storedDemographics);
          setDemographicInfo(parsedDemographics);
          setDemographicsCompleted(true);
        } catch (error) {
          console.error('Error parsing stored demographics:', error);
        }
      }
    }
  }, []);
  
  const categories = Object.keys(phrases);
  const currentPhrase = selectedCategory ? phrases[selectedCategory][currentPhraseIndex] : '';
  const totalPhrases = selectedCategory ? phrases[selectedCategory].length : 0;
  
  // Handle phrase selection
  const handlePhrasesSelected = (phrases) => {
    setSelectedPhrases(phrases);
    setPhrasesSelected(true);
    saveSelectedPhrases(phrases);
    
    // Set the category to the first phrase's category
    if (phrases.length > 0) {
      setSelectedCategory(phrases[0].category);
    }
    
    setCurrentPhraseIndex(0);
    setCurrentRecordingNumber(1);
    
    setNotification({
      open: true,
      message: `Great! You've selected ${phrases.length} phrases to record. Let's get started!`,
      severity: 'success'
    });
  };
  
  // Handle category selection change
  const handleCategoryChange = (event) => {
    const newCategory = event.target.value;
    setSelectedCategory(newCategory);
    
    // Find the first phrase index in the selected phrases that matches this category
    if (selectedPhrases) {
      const firstPhraseIndex = selectedPhrases.findIndex(item => item.category === newCategory);
      if (firstPhraseIndex !== -1) {
        setCurrentPhraseIndex(firstPhraseIndex);
      } else {
        setCurrentPhraseIndex(0);
      }
    } else {
      setCurrentPhraseIndex(0);
    }
    
    setCurrentRecordingNumber(1);
  };
  
  // Handle navigation between phrases
  const handleNextPhrase = () => {
    if (currentPhraseIndex < phrases[selectedCategory].length - 1) {
      setCurrentPhraseIndex(currentPhraseIndex + 1);
      setCurrentRecordingNumber(1);
    }
  };
  
  const handlePreviousPhrase = () => {
    if (currentPhraseIndex > 0) {
      setCurrentPhraseIndex(currentPhraseIndex - 1);
      setCurrentRecordingNumber(1);
    }
  };
  
  // Add keyboard navigation for laptop users
  React.useEffect(() => {
    const handleKeyDown = (e) => {
      if (!selectedCategory || uploading) return;
      
      // Right arrow for next phrase
      if (e.code === 'ArrowRight' && currentPhraseIndex < phrases[selectedCategory].length - 1) {
        e.preventDefault();
        handleNextPhrase();
      }
      
      // Left arrow for previous phrase
      if (e.code === 'ArrowLeft' && currentPhraseIndex > 0) {
        e.preventDefault();
        handlePreviousPhrase();
      }
    };
    
    window.addEventListener('keydown', handleKeyDown);
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [selectedCategory, currentPhraseIndex, uploading]);
  
  // Handle video recording completion
  const handleVideoRecorded = async (blob) => {
    setUploading(true);
    
    try {
      // Get current phrase information
      const currentPhraseObj = selectedPhrases[currentPhraseIndex];
      const category = currentPhraseObj.category;
      const phrase = currentPhraseObj.phrase;
      
      // Find the index of this phrase within its category
      const categoryPhraseIndex = phrases[category].indexOf(phrase);
      
      // Create unique filename for the recording
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const filename = `${category}_${categoryPhraseIndex}_${phrase.replace(/\s+/g, '_')}_${timestamp}.webm`;
      
      // Upload video to Azure
      await uploadVideoToAzure(
        blob, 
        category, 
        phrase, 
        currentRecordingNumber,
        demographicInfo
      );
      
      // Update recordings count
      const newRecordingsCount = { ...recordingsCount };
      if (!newRecordingsCount[category]) {
        newRecordingsCount[category] = {};
      }
      if (!newRecordingsCount[category][categoryPhraseIndex] === undefined) {
        newRecordingsCount[category][categoryPhraseIndex] = 0;
      }
      newRecordingsCount[category][categoryPhraseIndex] += 1;
      setRecordingsCount(newRecordingsCount);
      
      // Update session recordings count
      setSessionRecordingsCount(prevCount => prevCount + 1);
      
      // Store recordings count in local storage
      localStorage.setItem('icuAppRecordingsCount', JSON.stringify(newRecordingsCount));
      
      // Update current recording number
      if (currentRecordingNumber < RECORDINGS_PER_PHRASE) {
        setCurrentRecordingNumber(currentRecordingNumber + 1);
      } else {
        // Move to next phrase if we've completed all recordings for this phrase
        setCurrentRecordingNumber(1);
        if (currentPhraseIndex < selectedPhrases.length - 1) {
          setCurrentPhraseIndex(currentPhraseIndex + 1);
          
          // Update category if the next phrase is from a different category
          const nextPhraseObj = selectedPhrases[currentPhraseIndex + 1];
          if (nextPhraseObj.category !== category) {
            setSelectedCategory(nextPhraseObj.category);
          }
        } else {
          // If we've completed all selected phrases
          setShowCollectionTracker(true);
          setNotification({
            open: true,
            message: `Congratulations! You've completed all ${selectedPhrases.length} phrases!`,
            severity: 'success'
          });
        }
      }
    } catch (error) {
      console.error('Error uploading to Azure:', error);
      setNotification({
        open: true,
        message: `Upload failed: ${error.message}`,
        severity: 'error'
      });
    } finally {
      setUploading(false);
    }
  };
  
  const handleCloseNotification = () => {
    setNotification({ ...notification, open: false });
  };
  
  return (
    <div className="App">
      <Container maxWidth="lg" disableGutters={isMobile} sx={{ px: isMobile ? 1 : 2 }}>
        {!hasConsent ? (
          <ConsentPage onConsent={handleConsent} />
        ) : !demographicsCompleted ? (
          <DemographicForm onSubmit={handleDemographicSubmit} onBackToConsent={handleBackToConsent} />
        ) : (
          <>
            <Box className="app-header" sx={{ p: isMobile ? 2 : 3 }}>
              <Typography variant="h4" component="h1" gutterBottom sx={{ fontSize: { xs: '1.75rem', sm: '2.125rem' } }}>
                ICU Communication Phrase Collection
              </Typography>
              <Typography variant="subtitle1" align="center">
                Help train a lip-reading phone application to assist ICU patients who cannot use their voice
              </Typography>
              <Button
                variant="outlined"
                size="small"
                onClick={() => setDemographicsCompleted(false)}
                sx={{ mt: 2, fontSize: '0.8rem' }}
              >
                Edit Demographics
            ) : (
              <>
                <Box className="app-header" sx={{ p: isMobile ? 2 : 3 }}>
                  <Typography variant="h4" component="h1" gutterBottom sx={{ fontSize: { xs: '1.75rem', sm: '2.125rem' } }}>
                    ICU Communication Phrase Collection
                  </Typography>
                  <Typography variant="subtitle1" align="center">
                    Help train a lip-reading phone application to assist ICU patients who cannot use their voice
                  </Typography>
                  <Button
                    variant="outlined"
                    size="small"
                    onClick={() => setDemographicsCompleted(false)}
                    sx={{ mt: 2, fontSize: '0.8rem' }}
                  >
                    Edit Demographics
                  </Button>
                </Box>
              
                <Container maxWidth="md">
                  <Paper elevation={3} sx={{ p: 3, mb: 4 }}>
                    <Typography variant="h5" gutterBottom>
                      ICU Communication Phrase Video Collection
                  totalPhrases={totalPhrases}
                  currentRecording={currentRecordingNumber}
                  totalRecordings={RECORDINGS_PER_PHRASE}
                />
                
                {/* Video Recorder Component */}
                <VideoRecorder 
                  onVideoRecorded={handleVideoRecorded} 
                  disabled={uploading} 
                />
                
                {/* Progress Tracker Component */}
                <ProgressTracker 
                  recordingsCount={recordingsCount}
                  currentCategory={selectedCategory}
                  currentPhraseIndex={currentPhraseIndex}
                  totalPhrases={totalPhrases}
                  recordingsPerPhrase={RECORDINGS_PER_PHRASE}
                  categoryName={selectedCategory}
                />
                
                {/* Phrase Navigation Component */}
                <PhraseNavigation 
                  onPrevious={handlePreviousPhrase} 
                  onNext={handleNextPhrase} 
                  hasPrevious={currentPhraseIndex > 0} 
                  hasNext={currentPhraseIndex < totalPhrases - 1} 
                  disabled={uploading} 
                />
                
                {uploading && (
                  <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>
                    <CircularProgress size={24} sx={{ mr: 1 }} />
                    <Typography variant="body1">
                      Uploading video...
                    </Typography>
                  </Box>
                )}
              </>
            )}
            
            {showCollectionTracker && (
              <Box sx={{ display: 'flex', justifyContent: 'center', mt: 3 }}>
                <Button 
                  variant="outlined" 
                  color="primary" 
                  onClick={() => setShowCollectionTracker(false)}
                  sx={{ mr: 2 }}
                >
                  Continue Recording
                </Button>
                <Button 
                  variant="contained" 
                  color="primary" 
                  onClick={() => {
                    setSelectedCategory('');
                    setShowCollectionTracker(false);
                  }}
                >
                  Select New Category
                </Button>
              </Box>
            )}
          </Container>
        </>
      )}
      
      <Snackbar
        open={notification.open}
        autoHideDuration={6000}
        onClose={handleCloseNotification}
      >
        <Alert onClose={handleCloseNotification} severity={notification.severity}>
          {notification.message}
        </Alert>
      </Snackbar>
    </Container>
  </div>
);
};

export default App;
