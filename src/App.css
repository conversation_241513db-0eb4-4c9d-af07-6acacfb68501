.app-container {
  padding-top: 2rem;
  padding-bottom: 2rem;
  background: linear-gradient(to bottom, rgba(0, 150, 136, 0.05), rgba(38, 166, 154, 0.05));
  min-height: 100vh;
  max-width: 100%;
  overflow-x: hidden;
}

.webcam-container {
  position: relative;
  margin: 1.5rem auto;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 150, 136, 0.15);
  max-height: 80vh;
  width: 100%;
  max-width: 800px;
}

/* Laptop-specific styles */
@media (min-width: 1024px) {
  .webcam-container {
    margin: 2rem auto;
    border-radius: 12px;
    max-height: 60vh;
    width: 80%;
    max-width: 900px;
  }
}

/* Desktop-specific styles */
@media (min-width: 1440px) {
  .webcam-container {
    margin: 2.5rem auto;
    border-radius: 16px;
    box-shadow: 0 8px 30px rgba(0, 150, 136, 0.15);
    max-width: 1000px;
  }
}

.oval-guide {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10;
  box-shadow: 0 0 0 4px rgba(38, 166, 154, 0.2);
  animation: pulse-border 2s infinite;
}

@keyframes pulse-border {
  0% {
    box-shadow: 0 0 0 0 rgba(38, 166, 154, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(38, 166, 154, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(38, 166, 154, 0);
  }
}

.recording-indicator {
  box-shadow: 0 0 0 rgba(239, 83, 80, 0.4);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(239, 83, 80, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(239, 83, 80, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(239, 83, 80, 0);
  }
}

body {
  margin: 0;
  padding: 0;
  font-family: 'Roboto', sans-serif;
  background: linear-gradient(135deg, #f5f7fa 0%, #e4e8eb 100%);
  min-height: 100vh;
  overflow-x: hidden;
}

.App {
  text-align: center;
  max-width: 100%;
  overflow-x: hidden;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.phrase-display {
  margin: 1.5rem 0;
  padding: 1.5rem;
  background: linear-gradient(135deg, #ffffff 0%, #e0f2f1 100%);
  border-left: 5px solid #009688;
}

.controls {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin: 1.5rem 0;
}

@media (min-width: 1024px) {
  .controls {
    gap: 2rem;
    margin: 2rem 0;
  }
  
  .controls button {
    min-width: 180px;
    padding: 12px 24px;
    font-size: 1rem;
    transition: transform 0.2s ease;
  }

  .controls button:hover {
    transform: translateY(-2px);
  }
}

.app-header {
  background: linear-gradient(90deg, #009688 0%, #26a69a 100%);
  color: white;
  padding: 1.5rem;
  border-radius: 0 0 12px 12px;
  margin-bottom: 2rem;
  text-align: center;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.consent-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 150, 136, 0.15);
  padding: 2rem;
  margin-top: 2rem;
}

.consent-header {
  background: linear-gradient(90deg, #009688 0%, #26a69a 100%);
  color: white;
  padding: 1.5rem;
  border-radius: 12px 12px 0 0;
  margin: -2rem -2rem 2rem -2rem;
}

.consent-section {
  margin-bottom: 1.5rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid rgba(0, 150, 136, 0.2);
}

.consent-section:last-child {
  border-bottom: none;
}

.category-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  cursor: pointer;
  border: 2px solid transparent;
}

.category-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 6px 16px rgba(0, 150, 136, 0.2);
  border-color: rgba(38, 166, 154, 0.3);
}

.completed-phrase {
  background-color: #e8f5e9;
}

.progress-container {
  margin: 2rem 0;
  max-width: 900px;
  margin-left: auto;
  margin-right: auto;
}

/* Laptop keyboard shortcuts help */
.keyboard-shortcuts {
  background: rgba(0, 150, 136, 0.05);
  border-radius: 8px;
  padding: 1rem;
  margin: 1.5rem auto;
  max-width: 800px;
  border-left: 4px solid #009688;
}

.keyboard-shortcuts h4 {
  margin-top: 0;
  color: #00796b;
}

.keyboard-shortcuts ul {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 0.5rem;
  padding-left: 1.5rem;
}
