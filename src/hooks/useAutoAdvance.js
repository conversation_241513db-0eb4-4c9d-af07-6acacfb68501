/**
 * useAutoAdvance Hook
 * Isolated auto-advance logic with minimal dependencies to prevent race conditions
 * This hook is designed to work with the RecordingSessionProvider
 */

import { useEffect, useCallback } from 'react';
import { useRecordingSession } from '../providers/RecordingSessionProvider';
import { useAppState } from '../providers/AppStateProvider';

export const useAutoAdvance = () => {
  const {
    selectedPhrases,
    currentPhraseIndex,
    recordingsCount,
    RECORDINGS_PER_PHRASE,
    setCurrentPhraseIndex,
    setCompletionPrompt
  } = useRecordingSession();
  
  const { showNotification } = useAppState();

  // Manual phrase advancement (for navigation buttons)
  const advanceToNextPhrase = useCallback(() => {
    if (!selectedPhrases || currentPhraseIndex >= selectedPhrases.length - 1) {
      console.log('🏁 Cannot advance: at last phrase or no phrases selected');
      return false;
    }

    const nextIndex = currentPhraseIndex + 1;
    const nextPhrase = selectedPhrases[nextIndex];
    
    console.log('📝 Manual advance to next phrase:', nextPhrase.phrase);
    setCurrentPhraseIndex(nextIndex);
    
    showNotification(`Moving to: "${nextPhrase.phrase}"`, 'info');
    return true;
  }, [selectedPhrases, currentPhraseIndex, setCurrentPhraseIndex, showNotification]);

  // Manual phrase retreat (for navigation buttons)
  const retreatToPreviousPhrase = useCallback(() => {
    if (currentPhraseIndex <= 0) {
      console.log('🔙 Cannot retreat: at first phrase');
      return false;
    }

    const prevIndex = currentPhraseIndex - 1;
    const prevPhrase = selectedPhrases[prevIndex];
    
    console.log('🔙 Manual retreat to previous phrase:', prevPhrase.phrase);
    setCurrentPhraseIndex(prevIndex);
    
    showNotification(`Back to: "${prevPhrase.phrase}"`, 'info');
    return true;
  }, [currentPhraseIndex, selectedPhrases, setCurrentPhraseIndex, showNotification]);

  // Check if auto-advance should trigger (used by recording completion)
  const shouldAutoAdvance = useCallback(() => {
    if (!selectedPhrases || selectedPhrases.length === 0 || currentPhraseIndex < 0) {
      return false;
    }

    const currentPhraseObj = selectedPhrases[currentPhraseIndex];
    if (!currentPhraseObj) return false;

    // CRITICAL FIX: For consumer trial categories with repeated phrases, include position index
    const isConsumerTrialCategory = currentPhraseObj.category.startsWith('Consumer trial word/phrases');
    const phraseKey = isConsumerTrialCategory
      ? `${currentPhraseObj.category}:${currentPhraseObj.phrase}:${currentPhraseIndex}`
      : `${currentPhraseObj.category}:${currentPhraseObj.phrase}`;
    const currentCount = recordingsCount[phraseKey] || 0;

    return currentCount >= RECORDINGS_PER_PHRASE;
  }, [selectedPhrases, currentPhraseIndex, recordingsCount, RECORDINGS_PER_PHRASE]);

  // Check if all phrases are completed
  const areAllPhrasesCompleted = useCallback(() => {
    if (!selectedPhrases || selectedPhrases.length === 0) {
      return false;
    }

    return selectedPhrases.every((phrase, index) => {
      // CRITICAL FIX: For consumer trial categories with repeated phrases, include position index
      const isConsumerTrialCategory = phrase.category.startsWith('Consumer trial word/phrases');
      const phraseKey = isConsumerTrialCategory
        ? `${phrase.category}:${phrase.phrase}:${index}`
        : `${phrase.category}:${phrase.phrase}`;
      const count = recordingsCount[phraseKey] || 0;
      return count >= RECORDINGS_PER_PHRASE;
    });
  }, [selectedPhrases, recordingsCount, RECORDINGS_PER_PHRASE]);

  // Get completion status for current phrase
  const getCurrentPhraseCompletion = useCallback(() => {
    if (!selectedPhrases || currentPhraseIndex < 0 || currentPhraseIndex >= selectedPhrases.length) {
      return { current: 0, required: RECORDINGS_PER_PHRASE, completed: false };
    }

    const currentPhraseObj = selectedPhrases[currentPhraseIndex];
    // CRITICAL FIX: For consumer trial categories with repeated phrases, include position index
    const isConsumerTrialCategory = currentPhraseObj.category.startsWith('Consumer trial word/phrases');
    const phraseKey = isConsumerTrialCategory
      ? `${currentPhraseObj.category}:${currentPhraseObj.phrase}:${currentPhraseIndex}`
      : `${currentPhraseObj.category}:${currentPhraseObj.phrase}`;
    const current = recordingsCount[phraseKey] || 0;

    return {
      current,
      required: RECORDINGS_PER_PHRASE,
      completed: current >= RECORDINGS_PER_PHRASE,
      remaining: Math.max(0, RECORDINGS_PER_PHRASE - current)
    };
  }, [selectedPhrases, currentPhraseIndex, recordingsCount, RECORDINGS_PER_PHRASE]);

  // Navigation helpers
  const canAdvance = selectedPhrases && currentPhraseIndex < selectedPhrases.length - 1;
  const canRetreat = currentPhraseIndex > 0;
  const isLastPhrase = selectedPhrases && currentPhraseIndex >= selectedPhrases.length - 1;
  const isFirstPhrase = currentPhraseIndex === 0;

  return {
    // Actions
    advanceToNextPhrase,
    retreatToPreviousPhrase,
    
    // Status checks
    shouldAutoAdvance,
    areAllPhrasesCompleted,
    getCurrentPhraseCompletion,
    
    // Navigation state
    canAdvance,
    canRetreat,
    isLastPhrase,
    isFirstPhrase,
    
    // Current phrase info
    currentPhraseCompletion: getCurrentPhraseCompletion()
  };
};

export default useAutoAdvance;
