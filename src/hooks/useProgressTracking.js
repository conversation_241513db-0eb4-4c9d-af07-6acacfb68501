/**
 * useProgressTracking Hook
 * Comprehensive real-time progress tracking for ICU Dataset Application
 * Provides dynamic recording counts from AWS S3 bucket for all phrases
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import progressService from '../services/s3ProgressService';

// Use the singleton instance exported from the service

export const useProgressTracking = (options = {}) => {
  const {
    autoRefresh = true,
    refreshInterval = 60 * 60 * 1000, // 1 hour (optimised for performance)
    forceRefreshOnMount = false,
    enableCaching = true
  } = options;

  // State management
  const [progressData, setProgressData] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [lastUpdated, setLastUpdated] = useState(null);

  // Refs for cleanup
  const refreshIntervalRef = useRef(null);
  const mountedRef = useRef(true);

  /**
   * Fetch progress data from S3
   */
  const fetchProgress = useCallback(async (forceRefresh = false) => {
    if (!mountedRef.current) return;

    setIsLoading(true);
    setError(null);

    try {
      console.log('🔄 useProgressTracking: Fetching progress data...');
      const data = await progressService.fetchProgressData(forceRefresh);

      if (mountedRef.current) {
        setProgressData(data);
        setLastUpdated(data.lastUpdated);
        console.log('✅ useProgressTracking: Progress data updated', {
          totalRecordings: data.overall.totalRecordings,
          completedPhrases: data.overall.completedPhrases,
          totalPhrases: data.overall.totalPhrases,
          hasData: !!data
        });
      }
    } catch (err) {
      console.error('❌ useProgressTracking: Error fetching progress:', err);
      if (mountedRef.current) {
        setError(err.message);
        // Set empty progress data on error to prevent infinite loading
        setProgressData({
          overall: { totalRecordings: 0, completedPhrases: 0, totalPhrases: 0, progress: 0 },
          phrases: {},
          categories: {},
          lastUpdated: new Date().toISOString(),
          error: err.message
        });
      }
    } finally {
      if (mountedRef.current) {
        setIsLoading(false);
      }
    }
  }, []);

  /**
   * Force refresh progress data
   */
  const refreshProgress = useCallback(() => {
    return fetchProgress(true);
  }, [fetchProgress]);

  /**
   * Get progress for a specific phrase
   */
  const getPhraseProgress = useCallback((phrase) => {
    if (!progressData?.phrases) return null;
    return progressData.phrases[phrase] || {
      current: 0,
      target: 20,
      progress: 0,
      completed: false,
      remaining: 20
    };
  }, [progressData]);

  /**
   * Get progress for a specific category
   */
  const getCategoryProgress = useCallback((category) => {
    if (!progressData?.categories) return null;
    return progressData.categories[category] || {
      current: 0,
      target: 0,
      progress: 0,
      completed: 0,
      total: 0,
      remaining: 0
    };
  }, [progressData]);

  /**
   * Get formatted progress string for display (e.g., "5/20")
   */
  const getProgressString = useCallback((phrase) => {
    const progress = getPhraseProgress(phrase);
    return `${progress.current}/${progress.target}`;
  }, [getPhraseProgress]);

  /**
   * Check if phrase has reached target
   */
  const isPhraseCompleted = useCallback((phrase) => {
    const progress = getPhraseProgress(phrase);
    return progress.completed;
  }, [getPhraseProgress]);

  /**
   * Get visual indicator type for phrase
   */
  const getProgressIndicator = useCallback((phrase) => {
    const progress = getPhraseProgress(phrase);
    if (progress.completed) return 'completed';
    if (progress.current > 0) return 'in-progress';
    return 'not-started';
  }, [getPhraseProgress]);

  /**
   * Setup auto-refresh interval
   */
  useEffect(() => {
    if (autoRefresh && refreshInterval > 0) {
      refreshIntervalRef.current = setInterval(() => {
        console.log('⏰ useProgressTracking: Auto-refreshing progress data');
        fetchProgress(false); // Use cache if valid
      }, refreshInterval);

      return () => {
        if (refreshIntervalRef.current) {
          clearInterval(refreshIntervalRef.current);
        }
      };
    }
  }, [autoRefresh, refreshInterval, fetchProgress]);

  /**
   * Initial data fetch on mount
   */
  useEffect(() => {
    fetchProgress(forceRefreshOnMount);
  }, [fetchProgress, forceRefreshOnMount]);

  /**
   * Cleanup on unmount
   */
  useEffect(() => {
    return () => {
      mountedRef.current = false;
      if (refreshIntervalRef.current) {
        clearInterval(refreshIntervalRef.current);
      }
    };
  }, []);

  // Return hook interface
  return {
    // Data
    progressData,
    isLoading,
    error,
    lastUpdated,

    // Actions
    refreshProgress,
    fetchProgress,

    // Helpers
    getPhraseProgress,
    getCategoryProgress,
    getProgressString,
    isPhraseCompleted,
    getProgressIndicator,

    // Overall stats (convenience accessors)
    overallProgress: progressData?.overall?.progress || 0,
    totalRecordings: progressData?.overall?.totalRecordings || 0,
    completedPhrases: progressData?.overall?.completedPhrases || 0,
    totalPhrases: progressData?.overall?.totalPhrases || 0,

    // Status flags
    hasData: !!progressData,
    isReady: !!progressData && !isLoading,
    needsRefresh: !progressData || (lastUpdated && Date.now() - new Date(lastUpdated).getTime() > refreshInterval)
  };
};

/**
 * Hook for phrase-specific progress tracking
 * Optimised for individual phrase components
 */
export const usePhraseProgress = (phrase) => {
  const { getPhraseProgress, getProgressString, isPhraseCompleted, getProgressIndicator, refreshProgress } = useProgressTracking();

  const progress = getPhraseProgress(phrase);

  return {
    progress,
    progressString: getProgressString(phrase),
    isCompleted: isPhraseCompleted(phrase),
    indicator: getProgressIndicator(phrase),
    refreshProgress,
    
    // Direct access to common values
    current: progress?.current || 0,
    target: progress?.target || 20,
    remaining: progress?.remaining || 20,
    percentage: progress?.progress || 0
  };
};

/**
 * Hook for category-specific progress tracking
 * Optimised for category overview components
 */
export const useCategoryProgress = (category) => {
  const { getCategoryProgress, refreshProgress } = useProgressTracking();

  const progress = getCategoryProgress(category);

  return {
    progress,
    refreshProgress,
    
    // Direct access to common values
    current: progress?.current || 0,
    target: progress?.target || 0,
    completed: progress?.completed || 0,
    total: progress?.total || 0,
    remaining: progress?.remaining || 0,
    percentage: progress?.progress || 0
  };
};

export default useProgressTracking;
