import React, { useState, useEffect } from 'react';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { Container, Typography, Box, Paper, Snackbar, Alert, CircularProgress, Button, CssBaseline } from '@mui/material';
import './App.css';
import { phrases, phraseCollectionConfig } from './phrases';

// Import components
import ConsentPage from './components/ConsentPage';
import DemographicForm from './components/DemographicForm';
import CategorySelector from './components/CategorySelector';
import PhraseDisplay from './components/PhraseDisplay';
import VideoRecorder from './components/VideoRecorder';
import PhraseNavigation from './components/PhraseNavigation';
import ProgressTracker from './components/ProgressTracker';
import CollectionTracker from './components/CollectionTracker';
import PhraseSelector from './components/PhraseSelector';
import OvalGuide from './components/OvalGuide';
import NavigationMenu from './components/NavigationMenu';
import TrainingVideoPage from './components/TrainingVideoPage';
import EnvTest from './components/EnvTest';

// Import services
import { uploadVideoToAzure } from './services/azureStorage';
import { 
  getSelectedPhrases,
  saveSelectedPhrases,
  hasSelectedPhrases,
  clearSelectedPhrases,
  getCompletionPercentage,
  getTotalRecordings,
  getMaxPhrasesPerVolunteer
} from './services/phraseRotationService';

const App = () => {
  const [hasConsent, setHasConsent] = useState(false);
  const [demographicsCompleted, setDemographicsCompleted] = useState(false);
  const [demographicInfo, setDemographicInfo] = useState(null);
  const [trainingVideoCompleted, setTrainingVideoCompleted] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState('');
  const [currentPhraseIndex, setCurrentPhraseIndex] = useState(0);
  const [uploading, setUploading] = useState(false);
  const [notification, setNotification] = useState({ open: false, message: '', severity: 'info' });
  const [recordingsCount, setRecordingsCount] = useState({});
  const [currentRecordingNumber, setCurrentRecordingNumber] = useState(1);
  const [showCollectionTracker, setShowCollectionTracker] = useState(false);
  const [sessionRecordingsCount, setSessionRecordingsCount] = useState(0);
  const [selectedPhrases, setSelectedPhrases] = useState(null);
  const [phrasesSelected, setPhrasesSelected] = useState(false);
  const [currentStep, setCurrentStep] = useState('home'); // Track current navigation step
  
  const RECORDINGS_PER_PHRASE = phraseCollectionConfig.recordingsPerPhrase;
  const PHRASES_PER_VOLUNTEER = phraseCollectionConfig.phrasesPerVolunteer;
  const COLLECTION_GOAL = PHRASES_PER_VOLUNTEER * RECORDINGS_PER_PHRASE;
  
  // Detect if device is mobile or laptop
  const isMobile = /iPhone|iPad|iPod|Android/i.test(navigator.userAgent);
  const isLaptop = !isMobile;

  // Handle consent given
  const handleConsent = () => {
    setHasConsent(true);
    // Store consent in local storage to persist between sessions
    localStorage.setItem('icuAppConsent', 'true');
    setCurrentStep('demographics');
    setNotification({
      open: true,
      message: 'Thank you for your consent. Please provide your demographic information.',
      severity: 'success'
    });
  };
  
  // Handle going back to consent page
  const handleBackToConsent = () => {
    // Reset consent state
    setHasConsent(false);
    // Remove from local storage
    localStorage.removeItem('icuAppConsent');
    // Also reset demographics to ensure clean state
    setDemographicsCompleted(false);
    setCurrentStep('home');
  };
  
  // Handle navigation between different sections of the app
  const handleNavigation = (step) => {
    console.log('Navigation requested to:', step);
    
    // Basic validation - always allow navigation to home
    if (step === 'home') {
      setCurrentStep(step);
      setShowCollectionTracker(false);
      return;
    }
    
    // For other steps, check prerequisites
    if (hasConsent) {
      // Demographics requires consent - always allow navigation back to demographics
      if (step === 'demographics') {
        // Force the app to show the demographics form
        setCurrentStep('demographics');
        setShowCollectionTracker(false);
        return;
      }
      
      // Training video requires demographics
      if (step === 'training' && demographicsCompleted) {
        setCurrentStep(step);
        setShowCollectionTracker(false);
        return;
      }
      
      // Phrases selection requires training video
      if (step === 'phrases' && demographicsCompleted && trainingVideoCompleted) {
        setCurrentStep(step);
        setShowCollectionTracker(false);
        return;
      }
      
      // Recording requires phrase selection
      if (step === 'recording' && demographicsCompleted && phrasesSelected) {
        setCurrentStep(step);
        setShowCollectionTracker(false);
        return;
      }
      
      // Progress can be viewed if demographics are completed
      if (step === 'progress' && demographicsCompleted) {
        setCurrentStep(step);
        setShowCollectionTracker(true);
        return;
      }
    }
    
    // If we get here, the navigation wasn't allowed
    console.log('Navigation not allowed to:', step);
    
    // Show notification about why navigation isn't allowed
    if (!hasConsent) {
      setNotification({
        open: true,
        message: 'Please provide consent first before accessing this section.',
        severity: 'warning'
      });
    } else if (!demographicsCompleted && (step !== 'home' && step !== 'demographics')) {
      setNotification({
        open: true,
        message: 'Please complete your demographic information first.',
        severity: 'warning'
      });
    } else if (!trainingVideoCompleted && (step === 'phrases' || step === 'recording')) {
      setNotification({
        open: true,
        message: 'Please watch the training video first.',
        severity: 'warning'
      });
    } else if (!phrasesSelected && step === 'recording') {
      setNotification({
        open: true,
        message: 'Please select phrases to record first.',
        severity: 'warning'
      });
    }
  };
  
  // Handle demographic form submission
  const handleDemographicSubmit = (demographics) => {
    setDemographicInfo(demographics);
    setDemographicsCompleted(true);
    // Store demographics in local storage to persist between sessions
    localStorage.setItem('icuAppDemographics', JSON.stringify(demographics));
    setCurrentStep('training');
    setNotification({
      open: true,
      message: 'Thank you for providing your demographic information. Please watch the training video before proceeding.',
      severity: 'success'
    });
  };
  
  // Handle training video completion
  const handleTrainingVideoComplete = () => {
    setTrainingVideoCompleted(true);
    // Store training video completion in local storage
    localStorage.setItem('icuAppTrainingCompleted', 'true');
    setCurrentStep('phrases');
    setNotification({
      open: true,
      message: 'Thank you for watching the training video. You can now select phrases to record.',
      severity: 'success'
    });
  };
  
  // Load recordings count and check for saved selected phrases on component mount
  useEffect(() => {
    // Load recordings count
    const savedRecordingsCount = localStorage.getItem('icuAppRecordingsCount');
    if (savedRecordingsCount) {
      setRecordingsCount(JSON.parse(savedRecordingsCount));
    }
    
    // Check if user has already selected phrases
    const savedPhrases = getSelectedPhrases();
    if (savedPhrases) {
      setSelectedPhrases(savedPhrases);
      setPhrasesSelected(true);
      
      // Set the category to the first phrase's category
      if (savedPhrases.length > 0) {
        setSelectedCategory(savedPhrases[0].category);
      }
      
      setNotification({
        open: true,
        message: 'Your previously selected phrases have been loaded.',
        severity: 'info'
      });
    }
  }, []);

  // Check if consent and demographics were previously given
  useEffect(() => {
    const storedConsent = localStorage.getItem('icuAppConsent');
    if (storedConsent === 'true') {
      setHasConsent(true);
      
      // Check for stored demographic information
      const storedDemographics = localStorage.getItem('icuAppDemographics');
      if (storedDemographics) {
        try {
          const parsedDemographics = JSON.parse(storedDemographics);
          setDemographicInfo(parsedDemographics);
          setDemographicsCompleted(true);
        } catch (error) {
          console.error('Error parsing stored demographics:', error);
        }
      }
    }
  }, []);
  
  const categories = Object.keys(phrases);
  
  // Get current phrase from selected phrases
  const getCurrentPhrase = () => {
    if (!selectedPhrases || currentPhraseIndex >= selectedPhrases.length) {
      return '';
    }
    return selectedPhrases[currentPhraseIndex].phrase;
  };
  
  const currentPhrase = getCurrentPhrase();
  
  // Handle phrase selection
  const handlePhrasesSelected = (phrases) => {
    setSelectedPhrases(phrases);
    setPhrasesSelected(true);
    saveSelectedPhrases(phrases);
    
    // Set the first category as selected
    if (phrases.length > 0) {
      const firstCategory = phrases[0].category;
      setSelectedCategory(firstCategory);
      setCurrentPhraseIndex(0);
    }
    
    setCurrentStep('recording');
    
    setNotification({
      open: true,
      message: `${phrases.length} phrases selected. You can now start recording.`,
      severity: 'success'
    });
  };
  
  // Handle category selection change
  const handleCategoryChange = (event) => {
    const newCategory = event.target.value;
    setSelectedCategory(newCategory);
    
    // Find the first phrase index in the selected phrases that matches this category
    if (selectedPhrases) {
      const firstPhraseIndex = selectedPhrases.findIndex(item => item.category === newCategory);
      if (firstPhraseIndex !== -1) {
        setCurrentPhraseIndex(firstPhraseIndex);
      } else {
        setCurrentPhraseIndex(0);
      }
    } else {
      setCurrentPhraseIndex(0);
    }
    
    setCurrentRecordingNumber(1);
  };
  
  // Handle navigation between phrases
  const handleNextPhrase = () => {
    if (selectedPhrases && currentPhraseIndex < selectedPhrases.length - 1) {
      setCurrentPhraseIndex(currentPhraseIndex + 1);
      setCurrentRecordingNumber(1);
      
      // Update category if needed
      const nextPhrase = selectedPhrases[currentPhraseIndex + 1];
      if (nextPhrase.category !== selectedCategory) {
        setSelectedCategory(nextPhrase.category);
      }
    }
  };
  
  const handlePreviousPhrase = () => {
    if (currentPhraseIndex > 0) {
      setCurrentPhraseIndex(currentPhraseIndex - 1);
      setCurrentRecordingNumber(1);
      
      // Update category if needed
      const prevPhrase = selectedPhrases[currentPhraseIndex - 1];
      if (prevPhrase.category !== selectedCategory) {
        setSelectedCategory(prevPhrase.category);
      }
    }
  };
  
  // Add keyboard navigation for laptop users
  React.useEffect(() => {
    const handleKeyDown = (e) => {
      if (!phrasesSelected || uploading) return;
      
      // Right arrow for next phrase
      if (e.code === 'ArrowRight' && selectedPhrases && currentPhraseIndex < selectedPhrases.length - 1) {
        e.preventDefault();
        handleNextPhrase();
      }
      
      // Left arrow for previous phrase
      if (e.code === 'ArrowLeft' && currentPhraseIndex > 0) {
        e.preventDefault();
        handlePreviousPhrase();
      }
    };
    
    window.addEventListener('keydown', handleKeyDown);
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [selectedPhrases, currentPhraseIndex, uploading, phrasesSelected]);
  
  // Handle video recording completion
  const handleVideoRecorded = async (blob) => {
    setUploading(true);
    
    try {
      if (!selectedPhrases || currentPhraseIndex >= selectedPhrases.length) {
        throw new Error('No phrase selected');
      }
      
      // Get current phrase information
      const currentPhraseObj = selectedPhrases[currentPhraseIndex];
      const category = currentPhraseObj.category;
      const phrase = currentPhraseObj.phrase;
      
      // Find the index of this phrase within its category
      const categoryPhraseIndex = phrases[category].indexOf(phrase);
      
      // Create unique filename for the recording
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const filename = `${category}_${categoryPhraseIndex}_${phrase.replace(/\s+/g, '_')}_${timestamp}.webm`;
      
      // Upload video to Azure
      await uploadVideoToAzure(
        blob, 
        category, 
        phrase, 
        currentRecordingNumber,
        demographicInfo
      );
      
      // Update recordings count
      const newRecordingsCount = { ...recordingsCount };
      if (!newRecordingsCount[category]) {
        newRecordingsCount[category] = {};
      }
      if (newRecordingsCount[category][categoryPhraseIndex] === undefined) {
        newRecordingsCount[category][categoryPhraseIndex] = 0;
      }
      newRecordingsCount[category][categoryPhraseIndex] += 1;
      setRecordingsCount(newRecordingsCount);
      
      // Update session recordings count
      setSessionRecordingsCount(prevCount => prevCount + 1);
      
      // Store recordings count in local storage
      localStorage.setItem('icuAppRecordingsCount', JSON.stringify(newRecordingsCount));
      
      // Update current recording number
      if (currentRecordingNumber < RECORDINGS_PER_PHRASE) {
        setCurrentRecordingNumber(currentRecordingNumber + 1);
      } else {
        // Move to next phrase if we've completed all recordings for this phrase
        setCurrentRecordingNumber(1);
        if (currentPhraseIndex < selectedPhrases.length - 1) {
          setCurrentPhraseIndex(currentPhraseIndex + 1);
          
          // Update category if the next phrase is from a different category
          const nextPhraseObj = selectedPhrases[currentPhraseIndex + 1];
          if (nextPhraseObj.category !== category) {
            setSelectedCategory(nextPhraseObj.category);
          }
        } else {
          // If we've completed all selected phrases
          setShowCollectionTracker(true);
          setNotification({
            open: true,
            message: `Congratulations! You've completed all ${selectedPhrases.length} phrases!`,
            severity: 'success'
          });
        }
      }
    } catch (error) {
      console.error('Error uploading to Azure:', error);
      setNotification({
        open: true,
        message: `Upload failed: ${error.message}`,
        severity: 'error'
      });
    } finally {
      setUploading(false);
    }
  };
  
  const handleCloseNotification = () => {
    setNotification({ ...notification, open: false });
  };
  
  // Get total phrases for the current category
  const getTotalPhrasesForCategory = () => {
    if (!selectedPhrases) return 0;
    return selectedPhrases.filter(p => p.category === selectedCategory).length;
  };
  
  // Get current phrase index within its category
  const getCurrentPhraseIndexInCategory = () => {
    if (!selectedPhrases || currentPhraseIndex >= selectedPhrases.length) return 0;
    
    const currentCategoryPhrases = selectedPhrases.filter(p => p.category === selectedCategory);
    const currentPhrase = selectedPhrases[currentPhraseIndex];
    return currentCategoryPhrases.findIndex(p => p.phrase === currentPhrase.phrase);
  };
  
  return (
    <div className="App">
      {/* Navigation Menu */}
      {hasConsent && (
        <NavigationMenu 
          onNavigate={handleNavigation}
          currentStep={currentStep}
          hasConsent={hasConsent}
          demographicsCompleted={demographicsCompleted}
          phrasesSelected={phrasesSelected}
        />
      )}
      <Container maxWidth="lg" disableGutters={isMobile} sx={{ px: isMobile ? 1 : 2 }}>
        {!hasConsent ? (
          <>
            <ConsentPage onConsent={handleConsent} />
            <EnvTest />
          </>
        ) : currentStep === 'demographics' || (!demographicsCompleted) ? (
          <DemographicForm onSubmit={handleDemographicSubmit} onBack={handleBackToConsent} />
        ) : currentStep === 'training' ? (
          <TrainingVideoPage onComplete={handleTrainingVideoComplete} />
        ) : currentStep === 'phrases' ? (
          <PhraseSelector onPhrasesSelected={handlePhrasesSelected} />
        ) : currentStep === 'recording' ? (
          <>
            <Paper elevation={3} sx={{ p: 3, mb: 4 }}>
              <Typography variant="h5" gutterBottom>
                ICU Communication Phrase Video Collection
              </Typography>
              <Typography paragraph variant="body1">
                Thank you for participating in our data collection. Please record yourself saying each selected phrase clearly.
              </Typography>
              <Button
                variant="outlined"
                color="primary"
                size="small"
                onClick={() => setShowCollectionTracker(true)}
                sx={{ mb: 2 }}
              >
                Show Progress
              </Button>
            </Paper>
            
            {/* Show Collection Tracker or Phrase Selection UI */}
            {showCollectionTracker ? (
              <CollectionTracker 
                recordingsCount={recordingsCount} 
                totalGoal={COLLECTION_GOAL} 
                sessionCount={sessionRecordingsCount}
              />
            ) : (
              <Container maxWidth="md">
            <Paper elevation={3} sx={{ p: 3, mb: 4 }}>
              <Typography variant="h5" gutterBottom>
                ICU Communication Phrase Video Collection
              </Typography>
              <Typography paragraph variant="body1">
                Thank you for participating in our data collection. Please record yourself saying each selected phrase clearly.
              </Typography>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="subtitle1">
                  Instructions: 1. Position your face within the oval guide. 2. Click "Start Recording" or press Space bar. 3. The recording will automatically stop after 5 seconds and be uploaded.
                </Typography>
              </Box>
              <Typography variant="body2" sx={{ mb: 2, fontStyle: 'italic' }}>
                {isLaptop ? 'Laptop Tips: Use arrow keys to navigate between phrases (←/→), and Space bar to start/stop recording.' : 'Tap the recording button to start and stop recording.'}
              </Typography>
              {isLaptop && (
                <Typography paragraph variant="body1" sx={{ mt: 2, bgcolor: 'rgba(0,150,136,0.05)', p: 1, borderRadius: 1 }}>
                  <strong>Laptop Tips:</strong> Use arrow keys to navigate between phrases (←/→), and Space bar to start/stop recording.
                </Typography>
              )}
            </Paper>
            
            {/* Category Selector for navigation between selected phrases */}
            <CategorySelector 
              categories={Array.from(new Set(selectedPhrases.map(p => p.category)))} 
              selectedCategory={selectedCategory} 
              onCategoryChange={handleCategoryChange} 
            />
            
            {/* Phrase Display Component */}
            <PhraseDisplay 
              phrase={currentPhrase} 
              categoryName={selectedCategory} 
              currentIndex={getCurrentPhraseIndexInCategory()} 
              totalPhrases={getTotalPhrasesForCategory()}
              currentRecording={currentRecordingNumber}
              totalRecordings={RECORDINGS_PER_PHRASE}
            />
            
            {/* Video Recorder Component */}
            <VideoRecorder 
              onVideoRecorded={handleVideoRecorded} 
              disabled={uploading}
              phrase={selectedPhrases && selectedPhrases[currentPhraseIndex] ? selectedPhrases[currentPhraseIndex].text : ''}
              category={selectedCategory}
              recordingNumber={currentRecordingNumber}
              demographics={demographicInfo}
            />
            
            {/* Progress Tracker Component */}
            <ProgressTracker 
              recordingsCount={recordingsCount}
              currentCategory={selectedCategory}
              currentPhraseIndex={currentPhraseIndex}
              totalPhrases={selectedPhrases ? selectedPhrases.length : 0}
              recordingsPerPhrase={RECORDINGS_PER_PHRASE}
              categoryName={selectedCategory}
            />
            
            {/* Collection Tracker */}
            <CollectionTracker
              recordingsCount={recordingsCount}
              totalPhrases={selectedPhrases ? selectedPhrases.length : 0}
              recordingsPerPhrase={RECORDINGS_PER_PHRASE}
              sessionRecordings={sessionRecordingsCount}
              totalGoal={COLLECTION_GOAL}
            />
            
            {/* Phrase Navigation Component */}
            <PhraseNavigation 
              onPrevious={handlePreviousPhrase} 
              onNext={handleNextPhrase} 
              hasPrevious={currentPhraseIndex > 0} 
              hasNext={selectedPhrases && currentPhraseIndex < selectedPhrases.length - 1} 
              disabled={uploading} 
            />
            
            {uploading && (
              <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>
                <CircularProgress size={24} sx={{ mr: 1 }} />
                <Typography variant="body1">
                  Uploading video...
                </Typography>
              </Box>
            )}
              </Container>
            )}
          </>
        )}
      </Container>
      
      <Snackbar 
        open={notification.open} 
        autoHideDuration={6000} 
        onClose={handleCloseNotification}
      >
        <Alert onClose={handleCloseNotification} severity={notification.severity} sx={{ width: '100%' }}>
          {notification.message}
        </Alert>
      </Snackbar>
    </div>
  );
};

export default App;
