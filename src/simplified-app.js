import React, { useState } from 'react';
import ReactDOM from 'react-dom';
import './index.css';

// Simple phrases data
const phrases = {
  "Pain and Discomfort": [
    "I'm in pain.",
    "My back hurts.",
    "My chest hurts."
  ],
  "Breathing": [
    "I need to cough.",
    "I need suctioning.",
    "I can't breathe."
  ],
  "Medical Requests": [
    "I need a medication.",
    "I want something for the pain.",
    "I need mouth care."
  ],
  "Positioning": [
    "I need to move.",
    "I need to sit up.",
    "I need to lie down."
  ]
};

function SimplifiedApp() {
  const [selectedCategory, setSelectedCategory] = useState('');
  const [currentPhrase, setCurrentPhrase] = useState('');
  
  const handleCategoryClick = (category) => {
    console.log('Category clicked:', category);
    setSelectedCategory(category);
    if (phrases[category] && phrases[category].length > 0) {
      setCurrentPhrase(phrases[category][0]);
    }
  };
  
  return (
    <div style={{ maxWidth: '800px', margin: '0 auto', padding: '20px' }}>
      <h1>ICU Communication App</h1>
      
      {!selectedCategory ? (
        <div>
          <h2>Select a Category</h2>
          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '10px' }}>
            {Object.keys(phrases).map(category => (
              <div 
                key={category}
                onClick={() => handleCategoryClick(category)}
                style={{
                  padding: '20px',
                  border: '1px solid #ccc',
                  borderRadius: '8px',
                  cursor: 'pointer',
                  backgroundColor: '#f5f5f5'
                }}
              >
                <h3>{category}</h3>
                <p>Click to select</p>
              </div>
            ))}
          </div>
        </div>
      ) : (
        <div>
          <h2>{selectedCategory}</h2>
          <div style={{ marginBottom: '20px' }}>
            <h3>Current Phrase:</h3>
            <div style={{ 
              padding: '20px', 
              backgroundColor: '#e1f5fe', 
              borderRadius: '8px',
              fontSize: '24px',
              textAlign: 'center'
            }}>
              {currentPhrase}
            </div>
          </div>
          
          <div>
            <h3>All Phrases in this Category:</h3>
            <ul>
              {phrases[selectedCategory].map((phrase, index) => (
                <li 
                  key={index}
                  onClick={() => setCurrentPhrase(phrase)}
                  style={{ 
                    padding: '10px', 
                    cursor: 'pointer',
                    backgroundColor: currentPhrase === phrase ? '#e8f5e9' : 'transparent',
                    borderRadius: '4px',
                    marginBottom: '5px'
                  }}
                >
                  {phrase}
                </li>
              ))}
            </ul>
          </div>
          
          <button 
            onClick={() => {
              setSelectedCategory('');
              setCurrentPhrase('');
            }}
            style={{
              padding: '10px 20px',
              backgroundColor: '#f44336',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer',
              marginTop: '20px'
            }}
          >
            Back to Categories
          </button>
        </div>
      )}
    </div>
  );
}

export default SimplifiedApp;
