/**
 * SessionStateProvider - Session Data Management
 * Handles demographic information, session statistics, and localStorage persistence
 */

import React, { createContext, useContext, useReducer, useEffect, useCallback } from 'react';
import referenceNumberService from '../services/referenceNumberService';

// Action types
const SESSION_ACTIONS = {
  SET_DEMOGRAPHIC_INFO: 'SET_DEMOGRAPHIC_INFO',
  SET_SESSION_STATS: 'SET_SESSION_STATS',
  UPDATE_SESSION_RECORDINGS: 'UPDATE_SESSION_RECORDINGS',
  SET_SESSION_REFERENCE: 'SET_SESSION_REFERENCE',
  RESET_SESSION: 'RESET_SESSION',
  ADD_RECENT_RECORDING: 'ADD_RECENT_RECORDING'
};

// Initial state
const initialState = {
  demographicInfo: null,
  sessionStats: {
    startTime: new Date(),
    recentRecordings: []
  },
  sessionRecordingsCount: 0,
  currentSessionReference: '',
  
  // Early exit tracking
  earlyExit: false
};

// Reducer
const sessionStateReducer = (state, action) => {
  switch (action.type) {
    case SESSION_ACTIONS.SET_DEMOGRAPHIC_INFO:
      return {
        ...state,
        demographicInfo: action.payload
      };
      
    case SESSION_ACTIONS.SET_SESSION_STATS:
      return {
        ...state,
        sessionStats: action.payload
      };
      
    case SESSION_ACTIONS.UPDATE_SESSION_RECORDINGS:
      return {
        ...state,
        sessionRecordingsCount: state.sessionRecordingsCount + 1
      };
      
    case SESSION_ACTIONS.SET_SESSION_REFERENCE:
      return {
        ...state,
        currentSessionReference: action.payload
      };
      
    case SESSION_ACTIONS.ADD_RECENT_RECORDING:
      return {
        ...state,
        sessionStats: {
          ...state.sessionStats,
          recentRecordings: [
            action.payload,
            ...state.sessionStats.recentRecordings
          ].slice(0, 10) // Keep only last 10 recordings
        }
      };
      
    case SESSION_ACTIONS.RESET_SESSION:
      return {
        ...initialState,
        sessionStats: {
          startTime: new Date(),
          recentRecordings: []
        }
      };
      
    default:
      return state;
  }
};

// Context
const SessionStateContext = createContext();

// Provider component
export const SessionStateProvider = ({ children }) => {
  const [state, dispatch] = useReducer(sessionStateReducer, initialState);

  // Clear demographics on page refresh to ensure fresh sessions
  useEffect(() => {
    // Use multiple methods to detect page refresh more reliably
    let isPageRefresh = false;

    // Method 1: performance.navigation.type (legacy but widely supported)
    if (performance.navigation && performance.navigation.type === 1) {
      isPageRefresh = true;
    }

    // Method 2: Modern Navigation API (if available)
    if (window.navigation && window.navigation.type === 'reload') {
      isPageRefresh = true;
    }

    // Method 3: Check if sessionStorage was cleared (indicates page refresh)
    const isFirstLoad = !sessionStorage.getItem('icuAppSessionActive');

    // Clear demographics on page refresh OR first load to ensure fresh sessions
    if (isPageRefresh || isFirstLoad) {
      // Clear demographics for fresh session
      try {
        localStorage.removeItem('icuAppDemographics');
        console.log('🔄 Fresh session detected: Demographics cleared for new session');
        console.log('  - Page refresh:', isPageRefresh);
        console.log('  - First load:', isFirstLoad);
      } catch (error) {
        console.warn('⚠️ Failed to clear demographics:', error);
      }

      // Mark session as active
      sessionStorage.setItem('icuAppSessionActive', 'true');
    } else {
      // Load demographics if session is already active (within-session navigation)
      try {
        const savedDemographics = localStorage.getItem('icuAppDemographics');
        if (savedDemographics) {
          const demographicData = JSON.parse(savedDemographics);
          dispatch({
            type: SESSION_ACTIONS.SET_DEMOGRAPHIC_INFO,
            payload: demographicData
          });
          console.log('📱 Demographics loaded from localStorage for session persistence');
        }
      } catch (error) {
        console.warn('⚠️ Failed to load demographics from localStorage:', error);
      }
    }
  }, []);

  // Action creators
  const actions = {
    setDemographicInfo: useCallback((demographicData) => {
      dispatch({ 
        type: SESSION_ACTIONS.SET_DEMOGRAPHIC_INFO, 
        payload: demographicData 
      });
      
      // Save to localStorage for within-session persistence
      try {
        localStorage.setItem('icuAppDemographics', JSON.stringify(demographicData));
        console.log('📱 Demographics saved to localStorage for session persistence');
      } catch (error) {
        console.warn('⚠️ Failed to save demographics to localStorage:', error);
      }
    }, []),
    
    updateSessionStats: useCallback((updates) => {
      dispatch({ 
        type: SESSION_ACTIONS.SET_SESSION_STATS, 
        payload: updates 
      });
    }, []),
    
    incrementSessionRecordings: useCallback(() => {
      dispatch({ type: SESSION_ACTIONS.UPDATE_SESSION_RECORDINGS });
    }, []),
    
    addRecentRecording: useCallback((recordingData) => {
      dispatch({ 
        type: SESSION_ACTIONS.ADD_RECENT_RECORDING, 
        payload: {
          ...recordingData,
          timestamp: new Date().toISOString()
        }
      });
    }, []),
    
    generateSessionReference: useCallback((sessionParams) => {
      try {
        console.log('🔢 Generating session reference number...');
        
        const result = referenceNumberService.createSessionReference({
          ...sessionParams,
          demographicInfo: state.demographicInfo,
          sessionStats: state.sessionStats,
          sessionRecordingsCount: state.sessionRecordingsCount
        });
        
        if (result.success) {
          dispatch({ 
            type: SESSION_ACTIONS.SET_SESSION_REFERENCE, 
            payload: result.referenceNumber 
          });
          
          console.log('✅ Session reference generated successfully:', result.referenceNumber);
          return result.referenceNumber;
        } else {
          console.error('❌ Failed to generate session reference:', result.error);
          return null;
        }
      } catch (error) {
        console.error('❌ Error in generateSessionReference:', error);
        return null;
      }
    }, [state.demographicInfo, state.sessionStats, state.sessionRecordingsCount]),
    
    resetSession: useCallback(() => {
      dispatch({ type: SESSION_ACTIONS.RESET_SESSION });

      // Clear session-specific localStorage data
      try {
        localStorage.removeItem('icuAppDemographics');
        localStorage.removeItem('icu_selected_phrases'); // Correct key used by phraseRotationService
        localStorage.removeItem('icuAppSelectedPhrases'); // Legacy key cleanup
        sessionStorage.removeItem('icuAppSessionActive');
        console.log('🔄 Session data cleared');
      } catch (error) {
        console.warn('⚠️ Failed to clear session data:', error);
      }
    }, [])
  };

  const value = {
    ...state,
    ...actions
  };

  return (
    <SessionStateContext.Provider value={value}>
      {children}
    </SessionStateContext.Provider>
  );
};

// Hook to use session state
export const useSessionState = () => {
  const context = useContext(SessionStateContext);
  if (!context) {
    throw new Error('useSessionState must be used within a SessionStateProvider');
  }
  return context;
};

export default SessionStateProvider;
