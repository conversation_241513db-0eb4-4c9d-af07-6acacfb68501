import { S3Client, PutO<PERSON>Command, GetObjectCommand, HeadObjectCommand } from '@aws-sdk/client-s3';
import { fromCognitoIdentityPool } from '@aws-sdk/credential-providers';

const BUCKET_NAME = process.env.REACT_APP_S3_BUCKET || 'icudatasetphrasesfortesting';
const REGION = process.env.REACT_APP_AWS_REGION || 'ap-southeast-2';

// Check if AWS credentials are properly configured
const isAWSConfigured = () => {
  return process.env.REACT_APP_AWS_IDENTITY_POOL_ID &&
         process.env.REACT_APP_AWS_IDENTITY_POOL_ID !== 'your-identity-pool-id-here';
};

let s3Client = null;

// Only initialize S3 client if AWS is properly configured
if (isAWSConfigured()) {
  try {
    s3Client = new S3Client({
      region: REGION,
      credentials: fromCognitoIdentityPool({
        clientConfig: { region: REGION },
        identityPoolId: process.env.REACT_APP_AWS_IDENTITY_POOL_ID
      })
    });
    console.log('AWS S3 client initialized successfully for metadata service');
  } catch (error) {
    console.error('Failed to initialize AWS S3 client for metadata service:', error);
    s3Client = null;
  }
} else {
  console.warn('AWS credentials not configured. Metadata operations will be simulated.');
}

// Generate a unique recording ID
const generateRecordingId = (userId, timestamp) => {
  return `${userId}_${timestamp}_${Math.random().toString(36).substr(2, 9)}`;
};

// Create metadata object with all necessary information
export const createMetadata = ({
  videoBlob,
  phrase,
  category,
  demographics,
  qualityMetrics,
  recordingNumber,
  deviceInfo
}) => {
  const timestamp = new Date().toISOString();
  const recordingId = generateRecordingId(demographics.userId, timestamp);
  
  return {
    // Recording identification
    recordingId,
    filename: `${recordingId}.mp4`,
    timestamp,
    recordingNumber,
    
    // Participant demographics
    userId: demographics.userId,
    ageGroup: demographics.ageGroup,
    gender: demographics.gender,
    ethnicity: demographics.ethnicity,
    
    // Phrase information
    phrase,
    category: category || demographics.category || 'general', // For auto-advance compatibility
    phraseCategory: demographics.category || 'general', // Legacy field
    
    // Technical details
    fileSize: videoBlob.size,
    duration: videoBlob.duration || 0,
    mimeType: videoBlob.type,
    
    // Quality metrics
    brightness: qualityMetrics.brightness,
    sharpness: qualityMetrics.sharpness,
    faceConfidence: qualityMetrics.faceConfidence,
    overallQuality: qualityMetrics.overall,
    
    // Device information
    deviceType: deviceInfo.type || 'unknown',
    browser: deviceInfo.browser || 'unknown',
    os: deviceInfo.os || 'unknown',
    screenResolution: deviceInfo.screenResolution || 'unknown',
    cameraResolution: deviceInfo.cameraResolution || 'unknown',
    
    // Processing status
    processingStatus: 'uploaded',
    lipnetProcessed: false,
    lipnetConfidence: null,
    lipnetTranscription: null,
    
    // Validation
    validated: false,
    validationNotes: '',
    validatedBy: '',
    validatedAt: null
  };
};

// Update the metadata manifest in S3
export const updateMetadataManifest = async (metadata) => {
  try {
    console.log('=== METADATA SERVICE: Starting manifest update ===');
    console.log('Metadata received:', metadata);
    console.log('AWS configured:', isAWSConfigured());
    console.log('S3 client available:', !!s3Client);

    // If AWS is not configured, simulate the manifest update
    if (!s3Client) {
      console.log('=== METADATA SERVICE: AWS not configured, simulating manifest update ===');
      console.log('🔄 SIMULATION MODE: Metadata will be processed locally');
      console.log('Metadata details:', {
        recordingId: metadata.recordingId || metadata.id,
        phrase: metadata.phrase,
        userId: metadata.userId,
        ageGroup: metadata.ageGroup,
        gender: metadata.gender,
        ethnicity: metadata.ethnicity,
        timestamp: metadata.timestamp
      });
      console.log(`📁 Target bucket: ${BUCKET_NAME}`);
      console.log(`🌍 Target region: ${REGION}`);

      console.log('⏳ Simulating manifest update delay...');
      // Simulate processing delay
      await new Promise(resolve => setTimeout(resolve, 500));

      console.log('✅ METADATA SERVICE: Simulated manifest update completed successfully!');
      console.log('💡 Note: To enable real AWS metadata operations, configure AWS credentials in .env file');
      return;
    }

    const manifestKey = 'metadata/manifest.json';
    let manifest = {
      version: '1.0',
      lastUpdated: new Date().toISOString(),
      totalRecordings: 0,
      demographics: {
        ageGroups: {
          '18to39': 0,
          '40to64': 0,
          '65plus': 0
        },
        genders: {
          'male': 0,
          'female': 0,
          'nonbinary': 0
        },
        ethnicities: {
          'asian': 0,
          'caucasian': 0,
          'aboriginal': 0,
          'african': 0,
          'latinx': 0,
          'middle_eastern': 0,
          'pacific_islander': 0,
          'indigenous': 0
        }
      },
      qualityMetrics: {
        totalEvaluated: 0,
        averageBrightness: 0,
        averageSharpness: 0,
        averageFaceConfidence: 0,
        averageOverallQuality: 0
      },
      sampleCounts: {},
      recordings: {}
    };

    console.log('🔄 Attempting to get existing manifest...');
    // Try to get existing manifest
    try {
      const getResult = await s3Client.send(
        new GetObjectCommand({
          Bucket: BUCKET_NAME,
          Key: manifestKey
        })
      );
      const existingContent = await getResult.Body.transformToString();
      manifest = JSON.parse(existingContent);
      console.log('✅ Existing manifest loaded successfully');
    } catch (err) {
      console.log('📝 Creating new metadata manifest (existing not found)');
    }
    
    // Validate demographic values
    const validAgeGroups = ['18to39', '40to64', '65plus'];
    const validGenders = ['male', 'female', 'nonbinary'];
    const validEthnicities = [
      'asian',
      'caucasian',
      'aboriginal',
      'african',
      'latinx',
      'middle_eastern',
      'pacific_islander',
      'indigenous'
    ];

    metadata.ageGroup = validAgeGroups.find(ag => ag === metadata.ageGroup?.toLowerCase()) || '40to64';
    metadata.gender = validGenders.find(g => g === metadata.gender?.toLowerCase()) || 'female';
    metadata.ethnicity = validEthnicities.find(e => e === metadata.ethnicity?.toLowerCase()) || 'not_specified';

    // Add new recording metadata
    manifest.recordings[metadata.recordingId] = metadata;
    manifest.totalRecordings = Object.keys(manifest.recordings).length;
    
    // Update demographic statistics
    manifest.demographics.ageGroups = Object.values(manifest.recordings).reduce((acc, rec) => {
      acc[rec.ageGroup] = (acc[rec.ageGroup] || 0) + 1;
      return acc;
    }, { ...manifest.demographics.ageGroups });
    
    manifest.demographics.genders = Object.values(manifest.recordings).reduce((acc, rec) => {
      acc[rec.gender] = (acc[rec.gender] || 0) + 1;
      return acc;
    }, { ...manifest.demographics.genders });
    
    manifest.demographics.ethnicities = Object.values(manifest.recordings).reduce((acc, rec) => {
      acc[rec.ethnicity] = (acc[rec.ethnicity] || 0) + 1;
      return acc;
    }, { ...manifest.demographics.ethnicities });
    
    // Update quality metrics
    const recordings = Object.values(manifest.recordings);
    const evaluatedRecordings = recordings.filter(r => r.overallQuality !== null);
    manifest.qualityMetrics = {
      totalEvaluated: evaluatedRecordings.length,
      averageBrightness: evaluatedRecordings.reduce((sum, r) => sum + (r.brightness || 0), 0) / evaluatedRecordings.length || 0,
      averageSharpness: evaluatedRecordings.reduce((sum, r) => sum + (r.sharpness || 0), 0) / evaluatedRecordings.length || 0,
      averageFaceConfidence: evaluatedRecordings.reduce((sum, r) => sum + (r.faceConfidence || 0), 0) / evaluatedRecordings.length || 0,
      averageOverallQuality: evaluatedRecordings.reduce((sum, r) => sum + (r.overallQuality || 0), 0) / evaluatedRecordings.length || 0
    };
    
    // Update sample counts by phrase
    manifest.sampleCounts = recordings.reduce((acc, rec) => {
      const key = `${rec.phrase}_${rec.ageGroup}_${rec.gender}_${rec.ethnicity}`;
      acc[key] = (acc[key] || 0) + 1;
      return acc;
    }, {});
    
    manifest.lastUpdated = new Date().toISOString();
    
    console.log('🔄 Uploading updated manifest...');
    // Upload updated manifest
    await s3Client.send(
      new PutObjectCommand({
        Bucket: BUCKET_NAME,
        Key: manifestKey,
        Body: JSON.stringify(manifest, null, 2),
        ContentType: 'application/json'
      })
    );
    console.log('✅ Manifest uploaded successfully');

    console.log('🔄 Saving individual metadata file...');
    // Also save individual metadata file for this recording
    await s3Client.send(
      new PutObjectCommand({
        Bucket: BUCKET_NAME,
        Key: `metadata/recordings/${metadata.recordingId}.json`,
        Body: JSON.stringify(metadata, null, 2),
        ContentType: 'application/json'
      })
    );
    console.log('✅ Individual metadata file saved successfully');

    console.log('🔄 Generating CSV exports...');
    // Generate CSV export for compatibility
    await generateCSVManifest(manifest);
    console.log('✅ CSV exports generated successfully');

    console.log('✅ METADATA SERVICE: Manifest update completed successfully!');

  } catch (error) {
    console.error('=== METADATA SERVICE: Error updating metadata manifest ===');
    console.error('Error details:', error);
    console.error('Error stack:', error.stack);

    // Provide more specific error messages
    if (error.message) {
      if (error.message.includes('AWS credentials not configured')) {
        console.log('💡 AWS not configured - this is expected in development mode');
      } else if (error.message.includes('Access denied')) {
        console.error('❌ AWS access denied - check S3 bucket permissions');
      } else if (error.message.includes('NoSuchBucket')) {
        console.error('❌ S3 bucket does not exist');
      } else {
        console.error('❌ Unexpected error during metadata update:', error.message);
      }
    }

    // Don't throw - we don't want to fail the video upload if manifest update fails
    console.log('⚠️ Continuing without metadata manifest update (non-critical)');
  }
};

// Generate CSV manifest for compatibility and easy data analysis
const generateCSVManifest = async (manifest) => {
  // If AWS is not configured, skip CSV generation
  if (!s3Client) {
    console.log('⏭️ Skipping CSV generation (AWS not configured)');
    return;
  }
  // Generate recordings CSV
  const recordingHeaders = [
    'recordingId',
    'timestamp',
    'userId',
    'ageGroup',
    'gender',
    'ethnicity',
    'phrase',
    'phraseCategory',
    'recordingNumber',
    'fileSize',
    'duration',
    'brightness',
    'sharpness',
    'faceConfidence',
    'overallQuality',
    'deviceType',
    'processingStatus',
    'lipnetProcessed',
    'lipnetConfidence',
    'validated'
  ];
  
  // Generate statistics CSV
  const statsHeaders = [
    'category',
    'key',
    'count',
    'percentage'
  ];
  
  let recordingsContent = recordingHeaders.join(',') + '\n';
  let statsContent = statsHeaders.join(',') + '\n';
  
  // Add recordings data
  Object.values(manifest.recordings).forEach(recording => {
    const row = recordingHeaders.map(header => {
      const value = recording[header];
      if (value === null || value === undefined) return '';
      if (typeof value === 'string' && value.includes(',')) return `"${value}"`;
      return value;
    });
    recordingsContent += row.join(',') + '\n';
  });
  
  // Add demographic statistics
  const totalRecordings = manifest.totalRecordings;
  
  // Age groups
  Object.entries(manifest.demographics.ageGroups).forEach(([key, count]) => {
    statsContent += `ageGroup,${key},${count},${((count / totalRecordings) * 100).toFixed(2)}%\n`;
  });
  
  // Genders
  Object.entries(manifest.demographics.genders).forEach(([key, count]) => {
    statsContent += `gender,${key},${count},${((count / totalRecordings) * 100).toFixed(2)}%\n`;
  });
  
  // Ethnicities
  Object.entries(manifest.demographics.ethnicities).forEach(([key, count]) => {
    statsContent += `ethnicity,${key},${count},${((count / totalRecordings) * 100).toFixed(2)}%\n`;
  });
  
  // Quality metrics
  Object.entries(manifest.qualityMetrics).forEach(([key, value]) => {
    if (key === 'totalEvaluated') {
      statsContent += `quality,${key},${value},${((value / totalRecordings) * 100).toFixed(2)}%\n`;
    } else {
      statsContent += `quality,${key},${value.toFixed(2)},\n`;
    }
  });
  
  // Upload recordings CSV
  await s3Client.send(
    new PutObjectCommand({
      Bucket: BUCKET_NAME,
      Key: 'metadata/recordings.csv',
      Body: recordingsContent,
      ContentType: 'text/csv'
    })
  );
  
  // Upload statistics CSV
  await s3Client.send(
    new PutObjectCommand({
      Bucket: BUCKET_NAME,
      Key: 'metadata/statistics.csv',
      Body: statsContent,
      ContentType: 'text/csv'
    })
  );
  
  // Upload sample counts CSV
  let sampleCountsContent = 'phrase,ageGroup,gender,ethnicity,count\n';
  Object.entries(manifest.sampleCounts).forEach(([key, count]) => {
    const [phrase, ageGroup, gender, ethnicity] = key.split('_');
    sampleCountsContent += `"${phrase}",${ageGroup},${gender},${ethnicity},${count}\n`;
  });
  
  await s3Client.send(
    new PutObjectCommand({
      Bucket: BUCKET_NAME,
      Key: 'metadata/sample_counts.csv',
      Body: sampleCountsContent,
      ContentType: 'text/csv'
    })
  );
};

// Get device information
export const getDeviceInfo = () => {
  const ua = navigator.userAgent;
  const screenResolution = `${window.screen.width}x${window.screen.height}`;
  
  return {
    type: /Mobile|Android|iPhone|iPad|iPod/i.test(ua) ? 'mobile' : 'desktop',
    browser: getBrowserInfo(ua),
    os: getOSInfo(ua),
    screenResolution,
    cameraResolution: 'pending' // Will be updated when camera is active
  };
};

// Helper function to get browser information
const getBrowserInfo = (ua) => {
  if (ua.includes('Chrome')) return 'Chrome';
  if (ua.includes('Firefox')) return 'Firefox';
  if (ua.includes('Safari')) return 'Safari';
  if (ua.includes('Edge')) return 'Edge';
  return 'unknown';
};

// Helper function to get OS information
const getOSInfo = (ua) => {
  if (ua.includes('Windows')) return 'Windows';
  if (ua.includes('Mac OS')) return 'macOS';
  if (ua.includes('Linux')) return 'Linux';
  if (ua.includes('Android')) return 'Android';
  if (ua.includes('iOS')) return 'iOS';
  return 'unknown';
};

// Update camera resolution once video stream is available
export const updateCameraResolution = (video) => {
  if (video) {
    return `${video.videoWidth}x${video.videoHeight}`;
  }
  return 'unknown';
};
