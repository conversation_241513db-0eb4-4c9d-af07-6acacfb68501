/**
 * S3 Direct Progress Service
 * Frontend-only service for fetching progress data directly from S3
 * Used when no backend is available (e.g., Netlify deployment)
 */

import { S3Client, ListObjectsV2Command } from '@aws-sdk/client-s3';
import { fromCognitoIdentityPool } from '@aws-sdk/credential-providers';
import { getAllPhrases, getCategoryNames, getPhrasesByCategory } from '../phrases';

const BUCKET_NAME = process.env.REACT_APP_S3_BUCKET || 'icudatasetphrasesfortesting';
const REGION = process.env.REACT_APP_AWS_REGION || 'ap-southeast-2';
const DEFAULT_TARGET_PER_PHRASE = 20;

// Check if AWS credentials are properly configured for direct access
const isAWSConfigured = () => {
  const hasIdentityPool = !!process.env.REACT_APP_AWS_IDENTITY_POOL_ID;
  const hasRegion = !!process.env.REACT_APP_AWS_REGION;
  const hasBucket = !!process.env.REACT_APP_S3_BUCKET;
  
  return hasIdentityPool && hasRegion && hasBucket;
};

let s3Client = null;

// Initialize S3 client for direct access
if (isAWSConfigured()) {
  try {
    s3Client = new S3Client({
      region: REGION,
      credentials: fromCognitoIdentityPool({
        clientConfig: { region: REGION },
        identityPoolId: process.env.REACT_APP_AWS_IDENTITY_POOL_ID
      })
    });
    console.log('✅ S3 Direct Progress Service: AWS S3 client initialized');
  } catch (error) {
    console.error('❌ S3 Direct Progress Service: Failed to initialize S3 client:', error);
    s3Client = null;
  }
} else {
  console.warn('⚠️ S3 Direct Progress Service: AWS credentials not configured');
}

/**
 * Parse filename to extract metadata
 * Format: [PHRASE_LABEL]__[USER_ID]__[AGEGROUP]__[GENDER]__[ETHNICITY]__[TIMESTAMP].mp4
 */
const parseFilename = (filename) => {
  const parts = filename.replace(/\.(mp4|webm)$/, '').split('__');
  if (parts.length !== 6) return null;
  
  return {
    phrase: parts[0],
    userId: parts[1],
    ageGroup: parts[2],
    gender: parts[3],
    ethnicity: parts[4],
    timestamp: parts[5]
  };
};

/**
 * Fetch sample counts directly from S3 by listing objects
 */
export const fetchSampleCountsDirectly = async () => {
  if (!s3Client) {
    throw new Error('S3 client not initialized - check AWS credentials');
  }

  try {
    console.log('🔄 Fetching sample counts directly from S3...');

    const listCommand = new ListObjectsV2Command({
      Bucket: BUCKET_NAME,
      Prefix: 'icu-videos/',
      MaxKeys: 1000 // Adjust based on expected number of files
    });

    const response = await s3Client.send(listCommand);
    
    if (!response.Contents) {
      console.log('📊 No objects found in S3 bucket');
      return {
        success: true,
        counts: {
          byPhrase: {},
          byGender: { male: 0, female: 0, nonbinary: 0 },
          byAgeGroup: { '18to39': 0, '40to64': 0, '65plus': 0 },
          byEthnicity: {},
          total: 0
        },
        lastUpdated: new Date().toISOString(),
        source: 's3-direct-empty'
      };
    }

    // Initialize counters
    const sampleCounts = {
      byPhrase: {},
      byGender: { male: 0, female: 0, nonbinary: 0 },
      byAgeGroup: { '18to39': 0, '40to64': 0, '65plus': 0 },
      byEthnicity: {},
      total: 0
    };

    // Process each file
    for (const object of response.Contents) {
      if (object.Key.endsWith('.mp4') || object.Key.endsWith('.webm')) {
        const filename = object.Key.split('/').pop();
        const metadata = parseFilename(filename);
        
        if (metadata) {
          // Count by phrase
          if (!sampleCounts.byPhrase[metadata.phrase]) {
            sampleCounts.byPhrase[metadata.phrase] = 0;
          }
          sampleCounts.byPhrase[metadata.phrase]++;
          
          // Count by demographics
          if (sampleCounts.byGender[metadata.gender] !== undefined) {
            sampleCounts.byGender[metadata.gender]++;
          }
          
          if (sampleCounts.byAgeGroup[metadata.ageGroup] !== undefined) {
            sampleCounts.byAgeGroup[metadata.ageGroup]++;
          }
          
          if (!sampleCounts.byEthnicity[metadata.ethnicity]) {
            sampleCounts.byEthnicity[metadata.ethnicity] = 0;
          }
          sampleCounts.byEthnicity[metadata.ethnicity]++;
          
          sampleCounts.total++;
        }
      }
    }

    console.log('✅ Sample counts fetched directly from S3:', sampleCounts);

    return {
      success: true,
      counts: sampleCounts,
      lastUpdated: new Date().toISOString(),
      source: 's3-direct'
    };

  } catch (error) {
    console.error('❌ Error fetching sample counts directly from S3:', error);
    
    // Check if it's a CORS error
    if (error.message.includes('CORS') || error.message.includes('Access-Control')) {
      throw new Error('CORS error: S3 bucket CORS policy needs to include your domain');
    }
    
    throw new Error(`S3 direct access failed: ${error.message}`);
  }
};

/**
 * Check if a phrase has met the target number of recordings
 */
export const hasMetSampleTarget = (sampleCounts, phraseLabel, target = DEFAULT_TARGET_PER_PHRASE) => {
  if (!sampleCounts?.counts?.byPhrase) return false;
  
  const count = sampleCounts.counts.byPhrase[phraseLabel] || 0;
  return count >= target;
};

/**
 * Get sample progress as a percentage
 */
export const getSampleProgress = (sampleCounts, phraseLabel, target = DEFAULT_TARGET_PER_PHRASE) => {
  if (!sampleCounts?.counts?.byPhrase) return 0;
  
  const count = sampleCounts.counts.byPhrase[phraseLabel] || 0;
  return Math.min(100, Math.round((count / target) * 100));
};

/**
 * Test S3 connectivity
 */
export const testS3Connectivity = async () => {
  if (!s3Client) {
    return {
      success: false,
      error: 'S3 client not initialized - check AWS credentials'
    };
  }

  try {
    const listCommand = new ListObjectsV2Command({
      Bucket: BUCKET_NAME,
      MaxKeys: 1
    });

    await s3Client.send(listCommand);
    
    return {
      success: true,
      message: 'S3 connectivity test successful'
    };
    
  } catch (error) {
    return {
      success: false,
      error: error.message
    };
  }
};

export default {
  fetchSampleCountsDirectly,
  hasMetSampleTarget,
  getSampleProgress,
  testS3Connectivity
};
