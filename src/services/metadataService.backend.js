/**
 * Metadata Service - Backend Routing Version
 * Routes metadata operations through backend server to avoid CORS issues
 * This is an alternative to direct S3 access from the browser
 */

// Configuration
const BACKEND_URL = process.env.REACT_APP_BACKEND_URL || 'http://localhost:5000';

// Generate a unique recording ID
const generateRecordingId = (userId, timestamp) => {
  return `${userId}_${timestamp}_${Math.random().toString(36).substr(2, 9)}`;
};

// Create metadata object with all necessary information
export const createMetadata = ({
  videoBlob,
  phrase,
  category,
  demographics,
  qualityMetrics,
  recordingNumber,
  deviceInfo
}) => {
  const timestamp = new Date().toISOString();
  const recordingId = generateRecordingId(demographics.userId, timestamp);

  return {
    recordingId,
    timestamp,
    userId: demographics.userId,
    ageGroup: demographics.ageGroup,
    gender: demographics.gender,
    ethnicity: demographics.ethnicity,
    phrase,
    phraseCategory: category,
    recordingNumber,
    fileSize: videoBlob?.size || 0,
    duration: 5000, // 5 seconds
    brightness: qualityMetrics?.brightness || null,
    sharpness: qualityMetrics?.sharpness || null,
    faceConfidence: qualityMetrics?.faceConfidence || null,
    overallQuality: qualityMetrics?.overallQuality || null,
    deviceType: deviceInfo?.deviceType || 'unknown',
    processingStatus: 'pending',
    lipnetProcessed: false,
    lipnetConfidence: null,
    validated: false
  };
};

// Update metadata manifest via backend API
export const updateMetadataManifest = async (metadata) => {
  console.log('=== METADATA SERVICE (Backend): Updating metadata manifest ===');
  console.log('Metadata to add:', {
    recordingId: metadata.recordingId || metadata.id,
    phrase: metadata.phrase,
    userId: metadata.userId,
    ageGroup: metadata.ageGroup,
    gender: metadata.gender,
    ethnicity: metadata.ethnicity,
    timestamp: metadata.timestamp
  });

  try {
    // Step 1: Fetch existing manifest from backend
    console.log('🔄 Fetching existing manifest from backend...');
    const fetchResponse = await fetch(`${BACKEND_URL}/api/metadata/manifest`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      },
      credentials: 'include'
    });

    if (!fetchResponse.ok) {
      throw new Error(`Failed to fetch manifest: ${fetchResponse.status} ${fetchResponse.statusText}`);
    }

    const fetchResult = await fetchResponse.json();
    
    if (!fetchResult.success) {
      throw new Error(`Backend error: ${fetchResult.error}`);
    }

    let manifest = fetchResult.manifest;
    console.log('✅ Existing manifest loaded successfully');

    // Step 2: Update manifest with new recording
    manifest.totalRecordings = (manifest.totalRecordings || 0) + 1;
    
    // Validate demographic values
    const validAgeGroups = ['18to39', '40to64', '65plus'];
    const validGenders = ['male', 'female', 'nonbinary'];
    const validEthnicities = [
      'asian', 'caucasian', 'aboriginal', 'african',
      'latinx', 'middle_eastern', 'pacific_islander', 'indigenous'
    ];

    const ageGroup = validAgeGroups.includes(metadata.ageGroup) ? metadata.ageGroup : '18to39';
    const gender = validGenders.includes(metadata.gender) ? metadata.gender : 'male';
    const ethnicity = validEthnicities.includes(metadata.ethnicity) ? metadata.ethnicity : 'caucasian';

    // Update demographic counts
    manifest.demographics.ageGroups[ageGroup] = (manifest.demographics.ageGroups[ageGroup] || 0) + 1;
    manifest.demographics.genders[gender] = (manifest.demographics.genders[gender] || 0) + 1;
    manifest.demographics.ethnicities[ethnicity] = (manifest.demographics.ethnicities[ethnicity] || 0) + 1;

    // Add recording to manifest
    const recordingId = metadata.recordingId || metadata.id || generateRecordingId(metadata.userId, metadata.timestamp);
    manifest.recordings[recordingId] = {
      ...metadata,
      id: recordingId
    };

    // Update quality metrics
    const recordings = Object.values(manifest.recordings);
    const evaluatedRecordings = recordings.filter(r => r.overallQuality !== null);
    manifest.qualityMetrics = {
      totalEvaluated: evaluatedRecordings.length,
      averageBrightness: evaluatedRecordings.reduce((sum, r) => sum + (r.brightness || 0), 0) / evaluatedRecordings.length || 0,
      averageSharpness: evaluatedRecordings.reduce((sum, r) => sum + (r.sharpness || 0), 0) / evaluatedRecordings.length || 0,
      averageFaceConfidence: evaluatedRecordings.reduce((sum, r) => sum + (r.faceConfidence || 0), 0) / evaluatedRecordings.length || 0,
      averageOverallQuality: evaluatedRecordings.reduce((sum, r) => sum + (r.overallQuality || 0), 0) / evaluatedRecordings.length || 0
    };

    // Update sample counts by phrase
    manifest.sampleCounts = recordings.reduce((acc, rec) => {
      const key = `${rec.phrase}_${rec.ageGroup}_${rec.gender}_${rec.ethnicity}`;
      acc[key] = (acc[key] || 0) + 1;
      return acc;
    }, {});

    manifest.lastUpdated = new Date().toISOString();

    // Step 3: Send updated manifest to backend
    console.log('🔄 Uploading updated manifest via backend...');
    const updateResponse = await fetch(`${BACKEND_URL}/api/metadata/manifest`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      credentials: 'include',
      body: JSON.stringify({ manifest })
    });

    if (!updateResponse.ok) {
      throw new Error(`Failed to update manifest: ${updateResponse.status} ${updateResponse.statusText}`);
    }

    const updateResult = await updateResponse.json();
    
    if (!updateResult.success) {
      throw new Error(`Backend error: ${updateResult.error}`);
    }

    console.log('✅ METADATA SERVICE (Backend): Manifest updated successfully!');
    console.log('📊 Updated statistics:', {
      totalRecordings: manifest.totalRecordings,
      ageGroups: manifest.demographics.ageGroups,
      genders: manifest.demographics.genders,
      ethnicities: manifest.demographics.ethnicities
    });

  } catch (error) {
    console.error('=== METADATA SERVICE (Backend): Error updating metadata manifest ===');
    console.error('Error details:', error);
    console.error('Error stack:', error.stack);

    // Provide more specific error messages
    if (error.message) {
      if (error.message.includes('Failed to fetch')) {
        console.error('❌ Backend connection failed - check if server is running on', BACKEND_URL);
      } else if (error.message.includes('CORS')) {
        console.error('❌ CORS error - backend CORS policy may need updating');
      } else {
        console.error('❌ Unexpected error during metadata update:', error.message);
      }
    }

    throw error;
  }
};

// Export the service functions
export default {
  createMetadata,
  updateMetadataManifest
};
