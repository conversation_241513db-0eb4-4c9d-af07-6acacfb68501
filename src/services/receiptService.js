/**
 * Receipt Service for ICU Dataset Application
 * Manages sequential 6-digit receipt numbers with AWS S3 'receipt-numbers' bucket integration
 * Provides persistent receipt counter storage and video-receipt mapping
 */

import { S3Client, GetObjectCommand, PutObjectCommand, HeadObjectCommand } from '@aws-sdk/client-s3';
import { fromCognitoIdentityPool } from '@aws-sdk/credential-providers';

// Configuration
const RECEIPT_BUCKET_NAME = 'receipt-numbers'; // Dedicated receipt numbers bucket
const MAIN_BUCKET_NAME = process.env.REACT_APP_S3_BUCKET || 'icudatasetphrasesfortesting';
const REGION = process.env.REACT_APP_AWS_REGION || 'ap-southeast-2';
const RECEIPT_COUNTER_KEY = 'counter/receipt-counter.json';
const RECEIPT_LOG_KEY = 'logs/receipt-log.json';

// Check if AWS is configured
const isAWSConfigured = () => {
  return !!(
    process.env.REACT_APP_AWS_IDENTITY_POOL_ID &&
    process.env.REACT_APP_AWS_REGION
  );
};

let s3Client = null;

// Initialize S3 client for receipt operations
if (isAWSConfigured()) {
  try {
    s3Client = new S3Client({
      region: REGION,
      credentials: fromCognitoIdentityPool({
        clientConfig: { region: REGION },
        identityPoolId: process.env.REACT_APP_AWS_IDENTITY_POOL_ID
      })
    });
    console.log('✅ Receipt Service: AWS S3 client initialized successfully');
  } catch (error) {
    console.error('❌ Receipt Service: Failed to initialize AWS S3 client:', error);
    s3Client = null;
  }
} else {
  console.warn('⚠️ Receipt Service: AWS credentials not configured. Using localStorage fallback.');
}

class ReceiptService {
  constructor() {
    this.localStorageKey = 'icuAppReceiptCounter';
    this.receiptLogKey = 'icuAppReceiptLog';
    this.generationInProgress = false; // Prevent concurrent receipt generation
  }

  /**
   * Get current receipt counter from S3 or localStorage fallback
   * @returns {Promise<number>} Current counter value
   */
  async getCurrentCounter() {
    if (s3Client) {
      try {
        console.log('🔄 Fetching receipt counter from S3...');
        const command = new GetObjectCommand({
          Bucket: RECEIPT_BUCKET_NAME,
          Key: RECEIPT_COUNTER_KEY
        });

        const response = await s3Client.send(command);
        const counterData = await response.Body.transformToString();
        const parsed = JSON.parse(counterData);
        
        console.log('✅ Receipt counter fetched from S3:', parsed.counter);
        return parsed.counter || 0;
      } catch (error) {
        if (error.name === 'NoSuchKey' || error.name === 'NotFound') {
          console.log('📝 No receipt counter found in S3, starting from 0');
          return 0;
        }
        console.error('❌ Error fetching receipt counter from S3:', error);
        // Fall back to localStorage
        return this.getLocalStorageCounter();
      }
    } else {
      return this.getLocalStorageCounter();
    }
  }

  /**
   * Update receipt counter in S3 and localStorage
   * @param {number} newCounter - New counter value
   * @returns {Promise<boolean>} Success status
   */
  async updateCounter(newCounter) {
    // Always update localStorage as backup
    localStorage.setItem(this.localStorageKey, newCounter.toString());

    if (s3Client) {
      try {
        console.log('🔄 Updating receipt counter in S3...');
        const counterData = {
          counter: newCounter,
          lastUpdated: new Date().toISOString(),
          version: '1.0'
        };

        const command = new PutObjectCommand({
          Bucket: RECEIPT_BUCKET_NAME,
          Key: RECEIPT_COUNTER_KEY,
          Body: JSON.stringify(counterData, null, 2),
          ContentType: 'application/json'
        });

        await s3Client.send(command);
        console.log('✅ Receipt counter updated in S3:', newCounter);
        return true;
      } catch (error) {
        console.error('❌ Error updating receipt counter in S3:', error);
        console.log('📱 Counter updated in localStorage as fallback');
        return false;
      }
    } else {
      console.log('📱 Receipt counter updated in localStorage only');
      return true;
    }
  }

  /**
   * Generate next sequential receipt number
   * @returns {Promise<string>} 6-digit receipt number (e.g., "000001")
   */
  async generateReceiptNumber() {
    // Prevent concurrent receipt generation to avoid duplicate numbers
    if (this.generationInProgress) {
      console.log('⏳ Receipt generation already in progress, waiting...');
      // Wait a short time and try again
      await new Promise(resolve => setTimeout(resolve, 100));
      if (this.generationInProgress) {
        console.warn('⚠️ Receipt generation still in progress, using fallback');
        const fallbackNumber = Date.now().toString().slice(-6);
        console.log('🔄 Using timestamp fallback receipt number:', fallbackNumber);
        return fallbackNumber;
      }
    }

    try {
      this.generationInProgress = true;
      console.log('🧾 Generating new receipt number...');

      const currentCounter = await this.getCurrentCounter();
      const nextCounter = currentCounter + 1;

      // Update counter in storage
      await this.updateCounter(nextCounter);

      // Format as 6-digit number with leading zeros
      const receiptNumber = nextCounter.toString().padStart(6, '0');

      console.log('✅ Generated receipt number:', receiptNumber);
      return receiptNumber;
    } catch (error) {
      console.error('❌ Error generating receipt number:', error);
      // Fallback to timestamp-based number
      const fallbackNumber = Date.now().toString().slice(-6);
      console.log('🔄 Using fallback receipt number:', fallbackNumber);
      return fallbackNumber;
    } finally {
      this.generationInProgress = false;
    }
  }

  /**
   * Create receipt mapping entry for video recordings
   * @param {string} receiptNumber - Receipt number
   * @param {Array} videoUrls - Array of video URLs/paths
   * @param {Object} demographics - User demographic information
   * @param {Object} sessionInfo - Session information
   * @returns {Promise<boolean>} Success status
   */
  async createReceiptMapping(receiptNumber, videoUrls, demographics, sessionInfo) {
    const mappingEntry = {
      receiptNumber,
      timestamp: new Date().toISOString(),
      videos: videoUrls || [],
      demographics: demographics || {},
      sessionInfo: sessionInfo || {},
      videoCount: (videoUrls || []).length
    };

    // Store in localStorage
    this.storeLocalReceiptMapping(receiptNumber, mappingEntry);

    if (s3Client) {
      try {
        console.log('🔄 Creating receipt mapping in S3...');
        
        // Get existing receipt log
        let receiptLog = {};
        try {
          const getCommand = new GetObjectCommand({
            Bucket: RECEIPT_BUCKET_NAME,
            Key: RECEIPT_LOG_KEY
          });
          const response = await s3Client.send(getCommand);
          const logData = await response.Body.transformToString();
          receiptLog = JSON.parse(logData);
        } catch (error) {
          if (error.name !== 'NoSuchKey' && error.name !== 'NotFound') {
            console.warn('⚠️ Error reading existing receipt log:', error);
          }
          console.log('📝 Creating new receipt log');
        }

        // Add new mapping
        receiptLog[receiptNumber] = mappingEntry;

        // Update receipt log in S3
        const putCommand = new PutObjectCommand({
          Bucket: RECEIPT_BUCKET_NAME,
          Key: RECEIPT_LOG_KEY,
          Body: JSON.stringify(receiptLog, null, 2),
          ContentType: 'application/json'
        });

        await s3Client.send(putCommand);
        console.log('✅ Receipt mapping created in S3 for receipt:', receiptNumber);
        return true;
      } catch (error) {
        console.error('❌ Error creating receipt mapping in S3:', error);
        console.log('📱 Receipt mapping stored in localStorage as fallback');
        return false;
      }
    } else {
      console.log('📱 Receipt mapping stored in localStorage only');
      return true;
    }
  }

  /**
   * Get localStorage counter as fallback
   * @returns {number} Counter value
   */
  getLocalStorageCounter() {
    try {
      const stored = localStorage.getItem(this.localStorageKey);
      return parseInt(stored || '0', 10);
    } catch (error) {
      console.error('❌ Error reading localStorage counter:', error);
      return 0;
    }
  }

  /**
   * Store receipt mapping in localStorage
   * @param {string} receiptNumber - Receipt number
   * @param {Object} mappingEntry - Mapping entry data
   */
  storeLocalReceiptMapping(receiptNumber, mappingEntry) {
    try {
      const existingLog = JSON.parse(localStorage.getItem(this.receiptLogKey) || '{}');
      existingLog[receiptNumber] = mappingEntry;
      localStorage.setItem(this.receiptLogKey, JSON.stringify(existingLog));
      console.log('📱 Receipt mapping stored in localStorage:', receiptNumber);
    } catch (error) {
      console.error('❌ Error storing receipt mapping in localStorage:', error);
    }
  }

  /**
   * Get receipt mapping by receipt number
   * @param {string} receiptNumber - Receipt number to look up
   * @returns {Promise<Object|null>} Receipt mapping or null
   */
  async getReceiptMapping(receiptNumber) {
    if (s3Client) {
      try {
        const command = new GetObjectCommand({
          Bucket: RECEIPT_BUCKET_NAME,
          Key: RECEIPT_LOG_KEY
        });
        const response = await s3Client.send(command);
        const logData = await response.Body.transformToString();
        const receiptLog = JSON.parse(logData);
        return receiptLog[receiptNumber] || null;
      } catch (error) {
        console.error('❌ Error fetching receipt mapping from S3:', error);
      }
    }

    // Fallback to localStorage
    try {
      const localLog = JSON.parse(localStorage.getItem(this.receiptLogKey) || '{}');
      return localLog[receiptNumber] || null;
    } catch (error) {
      console.error('❌ Error reading receipt mapping from localStorage:', error);
      return null;
    }
  }

  /**
   * Test S3 connectivity for receipt operations
   * @returns {Promise<Object>} Test results
   */
  async testS3Connectivity() {
    if (!s3Client) {
      return {
        success: false,
        error: 'S3 client not initialized',
        fallbackAvailable: true
      };
    }

    try {
      // Test bucket access
      const testCommand = new HeadObjectCommand({
        Bucket: RECEIPT_BUCKET_NAME,
        Key: 'test-connectivity.json'
      });

      await s3Client.send(testCommand);
      
      return {
        success: true,
        message: 'S3 connectivity test passed',
        bucket: RECEIPT_BUCKET_NAME
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        fallbackAvailable: true,
        bucket: RECEIPT_BUCKET_NAME
      };
    }
  }
}

// Create and export singleton instance
const receiptService = new ReceiptService();
export default receiptService;
