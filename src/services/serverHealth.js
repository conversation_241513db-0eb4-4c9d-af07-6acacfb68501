/**
 * Server health check service
 * Provides functions to check if the backend server is running and accessible
 */

// Define API endpoints
const API_BASE = process.env.REACT_APP_API_BASE || 'http://localhost:3030/api'; // Reset to original port 3030
const HEALTH_CHECK_ENDPOINT = `${API_BASE}/health`;

/**
 * Check if the server is running and accessible
 * @returns {Promise<Object>} - Health check result with status and message
 */
export const checkServerHealth = async () => {
  console.log('Server health check bypassed for testing');
  
  // For testing UI changes, always return healthy
  return {
    healthy: true,
    message: 'Server health check bypassed for testing',
    timestamp: new Date().toISOString(),
    version: 'test',
  };
  
  // Original implementation (commented out)
  /*
  try {
    const response = await fetch(HEALTH_CHECK_ENDPOINT, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
      },
    });
    if (!response.ok) {
      return {
        healthy: false,
        message: `Server returned status: ${response.status}`,
      };
    }
    const data = await response.json();
    return {
      healthy: true,
      message: data.message || 'Server is running',
      timestamp: data.timestamp,
      version: data.version,
    };
  } catch (error) {
    return {
      healthy: false,
      message: `Cannot connect to server: ${error.message}`,
      error,
    };
  }
  */
};

/**
 * Check if the upload endpoint is accessible
 * @returns {Promise<Object>} - Health check result with status and message
 */
export const checkUploadEndpoint = async () => {
  try {
    const uploadEndpoint = `${API_BASE}/upload`;
    console.log('Checking upload endpoint at:', uploadEndpoint);
    
    // Just check if the endpoint exists with an OPTIONS request
    const response = await fetch(uploadEndpoint, {
      method: 'OPTIONS',
    });
    
    return {
      accessible: response.ok,
      status: response.status,
      message: response.ok ? 'Upload endpoint is accessible' : `Upload endpoint returned status: ${response.status}`,
    };
  } catch (error) {
    console.error('Upload endpoint check failed:', error);
    
    return {
      accessible: false,
      message: `Cannot access upload endpoint: ${error.message}`,
      error,
    };
  }
};
