import { phrases, phraseCollectionConfig } from '../phrases';

// Helper functions for phrase management
const getAllPhrases = () => {
  const allPhrases = [];
  Object.keys(phrases).forEach(category => {
    phrases[category].forEach(phrase => {
      allPhrases.push({
        text: phrase,
        category: category,
        label: phrase.toLowerCase().replace(/[^a-z0-9]/g, '_')
      });
    });
  });
  return allPhrases;
};

const getPhrasesByCategory = (category) => {
  return phrases[category] || [];
};

const getCategoryNames = () => {
  return Object.keys(phrases);
};

class PhraseRotationService {
  constructor() {
    this.currentCategoryIndex = 0;
    this.currentPhraseIndex = 0;
    this.categories = getCategoryNames();
    this.allPhrases = getAllPhrases();
    this.completedPhrases = this.loadCompletedPhrases();
  }

  // Load completed phrases from localStorage
  loadCompletedPhrases() {
    try {
      const stored = localStorage.getItem('icu_completed_phrases');
      return stored ? JSON.parse(stored) : {};
    } catch (error) {
      console.error('Error loading completed phrases:', error);
      return {};
    }
  }

  // Save completed phrases to localStorage
  saveCompletedPhrases() {
    try {
      localStorage.setItem('icu_completed_phrases', JSON.stringify(this.completedPhrases));
    } catch (error) {
      console.error('Error saving completed phrases:', error);
    }
  }

  // Mark a phrase as completed
  markPhraseCompleted(phraseLabel, ageGroup, gender, ethnicity) {
    const key = `${ageGroup}_${gender}_${ethnicity}`;
    if (!this.completedPhrases[key]) {
      this.completedPhrases[key] = [];
    }
    if (!this.completedPhrases[key].includes(phraseLabel)) {
      this.completedPhrases[key].push(phraseLabel);
      this.saveCompletedPhrases();
    }
  }

  // Check if a phrase is completed for specific demographics
  isPhraseCompleted(phraseLabel, ageGroup, gender, ethnicity) {
    const key = `${ageGroup}_${gender}_${ethnicity}`;
    return this.completedPhrases[key] && this.completedPhrases[key].includes(phraseLabel);
  }

  // Get next phrase in rotation
  getNextPhrase() {
    if (this.currentCategoryIndex >= this.categories.length) {
      this.currentCategoryIndex = 0;
    }

    const currentCategory = this.categories[this.currentCategoryIndex];
    const categoryPhrases = getPhrasesByCategory(currentCategory);

    if (this.currentPhraseIndex >= categoryPhrases.length) {
      this.currentPhraseIndex = 0;
      this.currentCategoryIndex++;
      return this.getNextPhrase();
    }

    const phrase = categoryPhrases[this.currentPhraseIndex];
    this.currentPhraseIndex++;

    return {
      text: phrase,
      category: currentCategory,
      label: phrase.toLowerCase().replace(/[^a-z0-9]/g, '_')
    };
  }

  // Get phrase by category and index
  getPhraseByIndex(category, index) {
    const categoryPhrases = getPhrasesByCategory(category);
    if (index >= 0 && index < categoryPhrases.length) {
      const phrase = categoryPhrases[index];
      return {
        text: phrase,
        category: category,
        label: phrase.toLowerCase().replace(/[^a-z0-9]/g, '_')
      };
    }
    return null;
  }

  // Get all phrases for a specific category
  getCategoryPhrases(category) {
    return getPhrasesByCategory(category).map(phrase => ({
      text: phrase,
      category: category,
      label: phrase.toLowerCase().replace(/[^a-z0-9]/g, '_')
    }));
  }

  // Get completion statistics
  getCompletionStats(ageGroup, gender, ethnicity) {
    const key = `${ageGroup}_${gender}_${ethnicity}`;
    const completed = this.completedPhrases[key] || [];
    const total = this.allPhrases.length;
    
    const categoryStats = {};
    this.categories.forEach(category => {
      const categoryPhrases = this.getCategoryPhrases(category);
      const categoryCompleted = categoryPhrases.filter(phrase => 
        completed.includes(phrase.label)
      ).length;
      
      categoryStats[category] = {
        completed: categoryCompleted,
        total: categoryPhrases.length,
        percentage: categoryPhrases.length > 0 ? (categoryCompleted / categoryPhrases.length) * 100 : 0
      };
    });

    return {
      overall: {
        completed: completed.length,
        total: total,
        percentage: total > 0 ? (completed.length / total) * 100 : 0
      },
      categories: categoryStats
    };
  }

  // Reset completion data
  resetCompletionData(ageGroup = null, gender = null, ethnicity = null) {
    if (ageGroup && gender && ethnicity) {
      const key = `${ageGroup}_${gender}_${ethnicity}`;
      delete this.completedPhrases[key];
    } else {
      this.completedPhrases = {};
    }
    this.saveCompletedPhrases();
  }

  // Get random phrase from specific category
  getRandomPhraseFromCategory(category) {
    const categoryPhrases = getPhrasesByCategory(category);
    if (categoryPhrases.length === 0) return null;
    
    const randomIndex = Math.floor(Math.random() * categoryPhrases.length);
    const phrase = categoryPhrases[randomIndex];
    
    return {
      text: phrase,
      category: category,
      label: phrase.toLowerCase().replace(/[^a-z0-9]/g, '_')
    };
  }
}

// Create service instance
const phraseRotationService = new PhraseRotationService();

// Re-export phraseCollectionConfig for components that need it
export { phraseCollectionConfig };

// Helper functions for phrase management
export const getSelectedPhrases = () => {
  try {
    const stored = localStorage.getItem('icu_selected_phrases');
    return stored ? JSON.parse(stored) : null;
  } catch (error) {
    console.error('Error loading selected phrases:', error);
    return null;
  }
};

export const saveSelectedPhrases = (phrases) => {
  try {
    localStorage.setItem('icu_selected_phrases', JSON.stringify(phrases));
    return true;
  } catch (error) {
    console.error('Error saving selected phrases:', error);
    return false;
  }
};

export const hasSelectedPhrases = () => {
  const phrases = getSelectedPhrases();
  return phrases && phrases.length > 0;
};

export const clearSelectedPhrases = () => {
  try {
    localStorage.removeItem('icu_selected_phrases');
    return true;
  } catch (error) {
    console.error('Error clearing selected phrases:', error);
    return false;
  }
};

export const getMaxPhrasesPerVolunteer = () => {
  return phraseCollectionConfig.phrasesPerVolunteer;
};

export const getTotalRecordings = (recordingsCount) => {
  if (!recordingsCount) return 0;
  return Object.values(recordingsCount).reduce((total, count) => total + count, 0);
};

export const getCompletionPercentage = (recordingsCount) => {
  const total = getTotalRecordings(recordingsCount);
  return Math.min(100, Math.round((total / phraseCollectionConfig.collectionGoal) * 100));
};

// Functions for PhraseSelector
export const getAllCategories = () => {
  return getCategoryNames();
};

export const getPhrasesForCategory = (category) => {
  return getPhrasesByCategory(category);
};

export const getPhraseCompletionData = (recordingsCount) => {
  // Return completion data for phrases
  const completionData = {};
  if (recordingsCount) {
    Object.keys(recordingsCount).forEach(phraseKey => {
      const count = recordingsCount[phraseKey] || 0;
      completionData[phraseKey] = {
        completed: count >= phraseCollectionConfig.recordingsPerPhrase,
        count: count,
        percentage: Math.min(100, Math.round((count / phraseCollectionConfig.recordingsPerPhrase) * 100))
      };
    });
  }
  return completionData;
};

export default phraseRotationService;
