/**
 * Logging Service
 * Handles client-side logging and error reporting to the server
 */

const SERVER_URL = process.env.REACT_APP_SERVER_URL || 'http://localhost:5000';

/**
 * Send error log to server
 * @param {Object} errorInfo - Error information
 * @returns {Promise<boolean>} Success status
 */
export const logErrorToServer = async (errorInfo) => {
  try {
    const response = await fetch(`${SERVER_URL}/api/log-error`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        error: errorInfo.message || 'Unknown error',
        context: errorInfo.context || 'Client',
        severity: errorInfo.severity || 'medium',
        timestamp: new Date().toISOString(),
        stack: errorInfo.stack,
        userAgent: navigator.userAgent,
        url: window.location.href
      })
    });

    return response.ok;
  } catch (error) {
    console.warn('Failed to log error to server:', error);
    return false;
  }
};

/**
 * Log application events for monitoring
 * @param {string} event - Event name
 * @param {Object} data - Event data
 */
export const logEvent = (event, data = {}) => {
  const logEntry = {
    timestamp: new Date().toISOString(),
    event,
    data,
    url: window.location.href,
    userAgent: navigator.userAgent
  };

  console.log('📊 EVENT:', logEntry);

  // In production, you might want to send this to an analytics service
  if (process.env.NODE_ENV === 'production') {
    // Example: Send to analytics service
    // sendToAnalytics(logEntry);
  }
};

/**
 * Log performance metrics
 * @param {string} operation - Operation name
 * @param {number} duration - Duration in milliseconds
 * @param {Object} metadata - Additional metadata
 */
export const logPerformance = (operation, duration, metadata = {}) => {
  const perfEntry = {
    timestamp: new Date().toISOString(),
    operation,
    duration,
    metadata,
    url: window.location.href
  };

  console.log('⚡ PERFORMANCE:', perfEntry);

  // Log slow operations as warnings
  if (duration > 5000) {
    console.warn('🐌 SLOW OPERATION:', perfEntry);
  }
};

/**
 * Start performance timing
 * @param {string} operation - Operation name
 * @returns {Function} End timing function
 */
export const startTiming = (operation) => {
  const startTime = performance.now();
  
  return (metadata = {}) => {
    const duration = performance.now() - startTime;
    logPerformance(operation, duration, metadata);
    return duration;
  };
};

/**
 * Log user interactions for UX analysis
 * @param {string} interaction - Interaction type
 * @param {Object} details - Interaction details
 */
export const logUserInteraction = (interaction, details = {}) => {
  const interactionEntry = {
    timestamp: new Date().toISOString(),
    interaction,
    details,
    url: window.location.href
  };

  console.log('👤 USER INTERACTION:', interactionEntry);
};

/**
 * Check server health
 * @returns {Promise<Object>} Health status
 */
export const checkServerHealth = async () => {
  try {
    const response = await fetch(`${SERVER_URL}/health`);
    const healthData = await response.json();
    
    logEvent('health_check', { status: healthData.status });
    
    return healthData;
  } catch (error) {
    const errorInfo = {
      message: 'Server health check failed',
      context: 'Health Check',
      severity: 'high',
      stack: error.stack
    };
    
    logErrorToServer(errorInfo);
    
    return {
      status: 'unreachable',
      error: error.message
    };
  }
};

export default {
  logErrorToServer,
  logEvent,
  logPerformance,
  startTiming,
  logUserInteraction,
  checkServerHealth
};
