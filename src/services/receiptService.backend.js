/**
 * Receipt Service - Backend Routing Version
 * Routes receipt operations through backend server to avoid CORS issues
 * This is an alternative to direct S3 access from the browser for the receipt-numbers bucket
 */

// Configuration
const BACKEND_URL = process.env.REACT_APP_BACKEND_URL || 'http://localhost:5000';

class ReceiptServiceBackend {
  constructor() {
    this.localStorageKey = 'icuAppReceiptCounter';
    this.receiptLogKey = 'icuAppReceiptLog';
  }

  /**
   * Generate next sequential receipt number via backend
   * @returns {Promise<string>} 6-digit receipt number (e.g., "000001")
   */
  async generateReceiptNumber() {
    try {
      console.log('🧾 Generating receipt number via backend...');
      
      const response = await fetch(`${BACKEND_URL}/api/receipt/generate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error(`Backend error: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();
      
      if (!result.success) {
        throw new Error(`Backend error: ${result.error}`);
      }

      console.log('✅ Receipt number generated via backend:', result.receiptNumber);
      
      // Also store in localStorage as backup
      localStorage.setItem(this.localStorageKey, result.counter.toString());
      
      return result.receiptNumber;
    } catch (error) {
      console.error('❌ Error generating receipt number via backend:', error);
      
      // Fallback to localStorage-based generation
      console.log('🔄 Falling back to localStorage-based receipt generation...');
      return this.generateLocalReceiptNumber();
    }
  }

  /**
   * Get current receipt counter via backend
   * @returns {Promise<number>} Current counter value
   */
  async getCurrentCounter() {
    try {
      console.log('🔄 Fetching receipt counter via backend...');
      
      const response = await fetch(`${BACKEND_URL}/api/receipt/counter`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        },
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error(`Backend error: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();
      
      if (!result.success) {
        throw new Error(`Backend error: ${result.error}`);
      }

      console.log('✅ Receipt counter fetched via backend:', result.counter);
      return result.counter;
    } catch (error) {
      console.error('❌ Error fetching receipt counter via backend:', error);
      
      // Fallback to localStorage
      return this.getLocalStorageCounter();
    }
  }

  /**
   * Update receipt counter via backend
   * @param {number} newCounter - New counter value
   * @returns {Promise<boolean>} Success status
   */
  async updateCounter(newCounter) {
    // Always update localStorage as backup
    localStorage.setItem(this.localStorageKey, newCounter.toString());

    try {
      console.log('🔄 Updating receipt counter via backend...');
      
      const response = await fetch(`${BACKEND_URL}/api/receipt/counter`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        credentials: 'include',
        body: JSON.stringify({ counter: newCounter })
      });

      if (!response.ok) {
        throw new Error(`Backend error: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();
      
      if (!result.success) {
        throw new Error(`Backend error: ${result.error}`);
      }

      console.log('✅ Receipt counter updated via backend:', newCounter);
      return true;
    } catch (error) {
      console.error('❌ Error updating receipt counter via backend:', error);
      console.log('📱 Counter updated in localStorage as fallback');
      return false;
    }
  }

  /**
   * Create receipt mapping entry for video recordings
   * @param {string} receiptNumber - Receipt number
   * @param {Array} videoUrls - Array of video URLs/paths
   * @param {Object} demographics - User demographic information
   * @param {Object} sessionInfo - Session information
   * @returns {Promise<boolean>} Success status
   */
  async createReceiptMapping(receiptNumber, videoUrls, demographics, sessionInfo) {
    const mappingEntry = {
      receiptNumber,
      timestamp: new Date().toISOString(),
      videos: videoUrls || [],
      demographics: demographics || {},
      sessionInfo: sessionInfo || {},
      videoCount: (videoUrls || []).length
    };

    // Store in localStorage as backup
    this.storeLocalReceiptMapping(receiptNumber, mappingEntry);

    // Note: Receipt mapping via backend would require additional endpoints
    // For now, we'll store in localStorage and log the mapping
    console.log('📱 Receipt mapping stored in localStorage:', receiptNumber);
    console.log('🔄 Backend receipt mapping endpoints not yet implemented');
    
    return true;
  }

  /**
   * Generate receipt number using localStorage fallback
   * @returns {string} 6-digit receipt number
   */
  generateLocalReceiptNumber() {
    try {
      const currentCounter = this.getLocalStorageCounter();
      const nextCounter = currentCounter + 1;
      
      // Update localStorage counter
      localStorage.setItem(this.localStorageKey, nextCounter.toString());
      
      // Format as 6-digit number with leading zeros
      const receiptNumber = nextCounter.toString().padStart(6, '0');
      
      console.log('📱 Generated localStorage receipt number:', receiptNumber);
      return receiptNumber;
    } catch (error) {
      console.error('❌ Error generating localStorage receipt number:', error);
      // Final fallback to timestamp-based number
      const fallbackNumber = Date.now().toString().slice(-6);
      console.log('🔄 Using timestamp fallback receipt number:', fallbackNumber);
      return fallbackNumber;
    }
  }

  /**
   * Get localStorage counter as fallback
   * @returns {number} Counter value
   */
  getLocalStorageCounter() {
    try {
      const stored = localStorage.getItem(this.localStorageKey);
      return parseInt(stored || '0', 10);
    } catch (error) {
      console.error('❌ Error reading localStorage counter:', error);
      return 0;
    }
  }

  /**
   * Store receipt mapping in localStorage
   * @param {string} receiptNumber - Receipt number
   * @param {Object} mappingEntry - Mapping entry data
   */
  storeLocalReceiptMapping(receiptNumber, mappingEntry) {
    try {
      const existingLog = JSON.parse(localStorage.getItem(this.receiptLogKey) || '{}');
      existingLog[receiptNumber] = mappingEntry;
      localStorage.setItem(this.receiptLogKey, JSON.stringify(existingLog));
      console.log('📱 Receipt mapping stored in localStorage:', receiptNumber);
    } catch (error) {
      console.error('❌ Error storing receipt mapping in localStorage:', error);
    }
  }

  /**
   * Get receipt mapping by receipt number
   * @param {string} receiptNumber - Receipt number to look up
   * @returns {Promise<Object|null>} Receipt mapping or null
   */
  async getReceiptMapping(receiptNumber) {
    // For now, only check localStorage
    // Backend receipt mapping lookup would require additional endpoints
    try {
      const localLog = JSON.parse(localStorage.getItem(this.receiptLogKey) || '{}');
      return localLog[receiptNumber] || null;
    } catch (error) {
      console.error('❌ Error reading receipt mapping from localStorage:', error);
      return null;
    }
  }

  /**
   * Test backend connectivity for receipt operations
   * @returns {Promise<Object>} Test results
   */
  async testBackendConnectivity() {
    try {
      const response = await fetch(`${BACKEND_URL}/api/receipt/counter`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        },
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error(`Backend error: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();
      
      return {
        success: true,
        message: 'Backend connectivity test passed',
        backendUrl: BACKEND_URL,
        counter: result.counter
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        fallbackAvailable: true,
        backendUrl: BACKEND_URL
      };
    }
  }
}

// Create and export singleton instance
const receiptServiceBackend = new ReceiptServiceBackend();
export default receiptServiceBackend;
