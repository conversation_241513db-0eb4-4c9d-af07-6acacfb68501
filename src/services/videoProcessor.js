/**
 * Video processor utility for LipNet-compatible video processing
 * Handles cropping, resizing, and formatting video for LipNet processing
 */

/**
 * Creates a cropped video focused on the oval viewport area for privacy compliance
 * This ensures recorded videos only contain the face area visible to users during recording
 *
 * @param {Blob} videoBlob - Original recorded video blob
 * @param {Object} cropArea - Oval crop area coordinates and dimensions
 * @param {Object} options - Processing options
 * @returns {Promise<Blob>} - Processed video blob cropped to oval area
 */
export const createOvalCroppedVideo = async (videoBlob, cropArea, options = {}) => {
  return new Promise((resolve, reject) => {
    try {
      const defaultOptions = {
        targetWidth: 400,
        targetHeight: 500,
        quality: 0.8,
        frameRate: 25
      };

      const processingOptions = { ...defaultOptions, ...options };

      // Create video element to read the source
      const sourceVideo = document.createElement('video');
      sourceVideo.muted = true;
      sourceVideo.playsInline = true;

      // Create canvas for processing
      const processCanvas = document.createElement('canvas');
      processCanvas.width = processingOptions.targetWidth;
      processCanvas.height = processingOptions.targetHeight;
      const ctx = processCanvas.getContext('2d');

      // Create output stream
      const outputStream = processCanvas.captureStream(processingOptions.frameRate);

      // Setup MediaRecorder for output
      const recordedChunks = [];
      const mediaRecorder = new MediaRecorder(outputStream, {
        mimeType: 'video/webm;codecs=vp9',
        videoBitsPerSecond: 1500000
      });

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          recordedChunks.push(event.data);
        }
      };

      mediaRecorder.onstop = () => {
        const processedBlob = new Blob(recordedChunks, { type: 'video/webm' });
        resolve(processedBlob);
      };

      mediaRecorder.onerror = (error) => {
        reject(new Error(`MediaRecorder error: ${error.message}`));
      };

      // Set video source
      sourceVideo.src = URL.createObjectURL(videoBlob);

      // When source video can play, start processing
      sourceVideo.oncanplay = () => {
        // Calculate oval crop area (default to center if not provided)
        const ovalCrop = cropArea || {
          x: (sourceVideo.videoWidth - processingOptions.targetWidth) / 2,
          y: (sourceVideo.videoHeight - processingOptions.targetHeight) / 2,
          width: processingOptions.targetWidth,
          height: processingOptions.targetHeight
        };

        // Start recording the processed stream
        mediaRecorder.start(100);

        // Start playback
        sourceVideo.play();

        // Process each frame with oval mask
        const processFrame = () => {
          // Clear canvas
          ctx.clearRect(0, 0, processCanvas.width, processCanvas.height);

          // Create oval clipping path
          ctx.save();
          ctx.beginPath();
          ctx.ellipse(
            processCanvas.width / 2,
            processCanvas.height / 2,
            processCanvas.width / 2 * 0.8,  // 80% width for oval
            processCanvas.height / 2 * 0.6, // 60% height for oval
            0, 0, 2 * Math.PI
          );
          ctx.clip();

          // Draw cropped video within oval
          ctx.drawImage(
            sourceVideo,
            ovalCrop.x, ovalCrop.y, ovalCrop.width, ovalCrop.height,
            0, 0, processCanvas.width, processCanvas.height
          );

          ctx.restore();

          // Continue processing until video ends
          if (!sourceVideo.paused && !sourceVideo.ended) {
            requestAnimationFrame(processFrame);
          } else {
            // Video ended, stop recording
            mediaRecorder.stop();
            sourceVideo.remove();
            URL.revokeObjectURL(sourceVideo.src);
          }
        };

        // Start frame processing
        processFrame();
      };

      sourceVideo.onerror = (error) => {
        reject(new Error(`Video loading error: ${error.message}`));
      };

    } catch (error) {
      reject(new Error(`Video processing setup error: ${error.message}`));
    }
  });
};

/**
 * Creates a cropped video focused on the mouth area for LipNet compatibility
 *
 * Note: This function processes the original recorded video blob, which is NOT mirrored.
 * The VideoRecorder component applies CSS mirroring only to the preview for user comfort,
 * but the actual recorded video and mouth position coordinates are in true orientation.
 *
 * @param {Blob} videoBlob - Original recorded video blob (non-mirrored)
 * @param {Object} mouthPosition - Mouth position coordinates and dimensions (non-mirrored)
 * @param {Object} options - Processing options
 * @returns {Promise<Blob>} - Processed video blob (non-mirrored)
 */
export const createLipNetVideo = async (videoBlob, mouthPosition, options = {}) => {
  return new Promise((resolve, reject) => {
    try {
      // Default options for LipNet
      const processingOptions = {
        targetWidth: 100,      // LipNet standard width
        targetHeight: 50,      // LipNet standard height
        frameRate: 30,         // LipNet standard frame rate
        greyscale: true,       // LipNet uses greyscale videos
        ...options
      };
      
      // Create video element for source
      const sourceVideo = document.createElement('video');
      sourceVideo.autoplay = false;
      sourceVideo.muted = true;
      sourceVideo.playsInline = true;
      
      // Create canvas for processing
      const processCanvas = document.createElement('canvas');
      processCanvas.width = processingOptions.targetWidth;
      processCanvas.height = processingOptions.targetHeight;
      const ctx = processCanvas.getContext('2d', { alpha: false });
      
      // Create media recorder for output
      const outputStream = processCanvas.captureStream(processingOptions.frameRate);
      const mediaRecorder = new MediaRecorder(outputStream, {
        mimeType: 'video/webm;codecs=vp8',
        videoBitsPerSecond: 1500000 // 1.5 Mbps for good quality but smaller size
      });
      
      // Prepare to collect chunks
      const chunks = [];
      mediaRecorder.ondataavailable = (e) => {
        if (e.data.size > 0) {
          chunks.push(e.data);
        }
      };
      
      // When recording completes
      mediaRecorder.onstop = () => {
        const processedBlob = new Blob(chunks, { type: 'video/webm' });
        console.log('LipNet video processing complete', processedBlob.size);
        resolve(processedBlob);
      };
      
      // When source video can play, start processing
      sourceVideo.oncanplay = () => {
        // Adjust mouth position if not provided
        const mouthROI = mouthPosition || {
          x: sourceVideo.videoWidth / 2 - processingOptions.targetWidth / 2,
          y: sourceVideo.videoHeight / 2 - processingOptions.targetHeight / 2,
          width: processingOptions.targetWidth * 1.5,  // Add some padding
          height: processingOptions.targetHeight * 1.5 // Add some padding
        };
        
        // Start recording the processed stream
        mediaRecorder.start(100); // Capture in small chunks
        
        // Start playback
        sourceVideo.play();
        
        // Process each frame
        const processFrame = () => {
          // Draw the mouth region to the canvas
          ctx.fillStyle = 'black';
          ctx.fillRect(0, 0, processCanvas.width, processCanvas.height);
          
          // Crop and resize the mouth region
          ctx.drawImage(
            sourceVideo,
            mouthROI.x, mouthROI.y, mouthROI.width, mouthROI.height,
            0, 0, processCanvas.width, processCanvas.height
          );
          
          // Apply greyscale filter if needed
          if (processingOptions.greyscale) {
            const imageData = ctx.getImageData(0, 0, processCanvas.width, processCanvas.height);
            const data = imageData.data;
            
            for (let i = 0; i < data.length; i += 4) {
              const avg = (data[i] + data[i + 1] + data[i + 2]) / 3;
              data[i] = avg;     // Red
              data[i + 1] = avg; // Green
              data[i + 2] = avg; // Blue
              // Keep alpha (data[i + 3]) unchanged
            }
            
            ctx.putImageData(imageData, 0, 0);
          }
          
          // Continue processing until video ends
          if (!sourceVideo.paused && !sourceVideo.ended) {
            requestAnimationFrame(processFrame);
          } else {
            // Video ended, stop recording
            mediaRecorder.stop();
            sourceVideo.remove();
          }
        };
        
        // Start frame processing
        processFrame();
      };
      
      // Handle errors
      sourceVideo.onerror = (err) => {
        console.error('Source video error:', err);
        reject(new Error('Failed to process the video'));
      };
      
      // Create object URL and set as source
      const videoURL = URL.createObjectURL(videoBlob);
      sourceVideo.src = videoURL;
      
      // Set timeout to prevent infinite waiting
      setTimeout(() => {
        if (chunks.length === 0) {
          reject(new Error('Video processing timed out'));
        }
      }, 30000); // 30 seconds max
    } catch (error) {
      console.error('Error in createLipNetVideo:', error);
      reject(error);
    }
  });
};

/**
 * Simple function to just extract a thumbnail from the video
 * 
 * @param {Blob} videoBlob - Original video blob 
 * @returns {Promise<string>} - Base64 encoded thumbnail image
 */
export const extractVideoThumbnail = (videoBlob) => {
  return new Promise((resolve, reject) => {
    try {
      const video = document.createElement('video');
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      
      video.onloadedmetadata = () => {
        // Set canvas size to match video
        canvas.width = video.videoWidth;
        canvas.height = video.videoHeight;
        
        // Seek to 1 second or video middle point
        video.currentTime = Math.min(1, video.duration / 2);
      };
      
      video.onseeked = () => {
        // Draw video frame to canvas
        ctx.drawImage(video, 0, 0);
        
        // Convert to base64
        const thumbnail = canvas.toDataURL('image/jpeg', 0.7);
        
        // Clean up
        URL.revokeObjectURL(video.src);
        resolve(thumbnail);
      };
      
      video.onerror = () => reject(new Error('Failed to load video for thumbnail extraction'));
      
      // Load the video
      video.src = URL.createObjectURL(videoBlob);
    } catch (error) {
      reject(error);
    }
  });
};

export default { createLipNetVideo, extractVideoThumbnail };
