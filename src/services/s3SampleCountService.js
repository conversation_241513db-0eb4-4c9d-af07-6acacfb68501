/**
 * S3-based Sample Count Service
 * Uses backend API when available, falls back to direct S3 access in production
 * This approach provides flexibility for both full-stack and frontend-only deployments
 */

import { fetchSampleCountsDirectly } from './s3DirectProgressService';

// Backend API configuration
const BACKEND_URL = process.env.REACT_APP_BACKEND_URL || 'http://localhost:5000';

// Check if we're in production without a backend
const isProductionWithoutBackend = process.env.NODE_ENV === 'production' && !process.env.REACT_APP_BACKEND_URL;
const SAMPLE_COUNTS_ENDPOINT = `${BACKEND_URL}/api/sample-counts`;

/**
 * Parse filename to extract metadata
 * Format: [PHRASE_LABEL]__[USER_ID]__[AGEGROUP]__[GENDER]__[ETHNICITY]__[TIMESTAMP].mp4
 */
const parseFilename = (filename) => {
  const parts = filename.replace('.mp4', '').split('__');
  if (parts.length !== 6) return null;
  
  return {
    phrase: parts[0],
    userId: parts[1],
    ageGroup: parts[2],
    gender: parts[3],
    ethnicity: parts[4],
    timestamp: parts[5]
  };
};

/**
 * Count samples by reading metadata.csv from S3
 * This is more efficient than listing all objects
 */
export const fetchSampleCountsFromMetadata = async () => {
  // If in production without backend, try direct S3 access
  if (isProductionWithoutBackend) {
    console.log('🔄 Production mode without backend - attempting direct S3 access');
    try {
      const directResult = await fetchSampleCountsDirectly();
      console.log('✅ Direct S3 access successful');
      return directResult;
    } catch (error) {
      console.error('❌ Direct S3 access failed:', error);
      console.log('🔄 Falling back to empty data structure');
      return {
        success: false,
        error: `failed to fetch s3 data: ${error.message}`,
        counts: {
          byPhrase: {},
          byGender: { male: 0, female: 0, nonbinary: 0 },
          byAgeGroup: { '18to39': 0, '40to64': 0, '65plus': 0 },
          byEthnicity: {},
          total: 0
        },
        lastUpdated: new Date().toISOString(),
        source: 'error-fallback'
      };
    }
  }

  try {
    console.log('🔄 Fetching sample counts from backend API...');

    const response = await fetch(SAMPLE_COUNTS_ENDPOINT, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    if (!data.success) {
      throw new Error(data.error || 'Failed to fetch sample counts');
    }

    console.log('✅ Sample counts fetched from backend:', data.counts);

    return {
      success: true,
      counts: data.counts,
      lastUpdated: data.lastUpdated || new Date().toISOString(),
      source: 'backend-api'
    };

  } catch (error) {
    console.error('❌ Error fetching sample counts from backend:', error);

    // Return empty counts structure on error
    return {
      success: false,
      error: error.message || 'Failed to fetch sample counts',
      counts: {
        byPhrase: {},
        byGender: { male: 0, female: 0, nonbinary: 0 },
        byAgeGroup: { '18to39': 0, '40to64': 0, '65plus': 0 },
        byEthnicity: {},
        total: 0
      },
      lastUpdated: new Date().toISOString(),
      source: 'error-fallback'
    };
  }
};

/**
 * Legacy function - now redirects to backend API
 * Kept for backward compatibility
 */
export const fetchSampleCountsFromObjects = async () => {
  console.log('⚠️ fetchSampleCountsFromObjects called - redirecting to backend API');
  return await fetchSampleCountsFromMetadata();
};



/**
 * Generate and upload sample count report to S3
 * Creates both CSV and JSON reports
 */
export const generateSampleCountReport = async () => {
  try {
    const sampleCounts = await fetchSampleCountsFromMetadata();
    
    if (!sampleCounts.success) {
      throw new Error('Failed to fetch sample counts');
    }
    
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    
    // Generate CSV report
    const csvReport = generateCSVReport(sampleCounts.counts);
    const csvKey = `reports/sample-counts-${timestamp}.csv`;
    
    // Generate JSON report
    const jsonReport = JSON.stringify({
      ...sampleCounts,
      generatedAt: new Date().toISOString()
    }, null, 2);
    const jsonKey = `reports/sample-counts-${timestamp}.json`;
    
    // Upload both reports to S3
    await Promise.all([
      s3Client.send(new PutObjectCommand({
        Bucket: BUCKET_NAME,
        Key: csvKey,
        Body: csvReport,
        ContentType: 'text/csv'
      })),
      s3Client.send(new PutObjectCommand({
        Bucket: BUCKET_NAME,
        Key: jsonKey,
        Body: jsonReport,
        ContentType: 'application/json'
      }))
    ]);
    
    return {
      success: true,
      csvReport: csvKey,
      jsonReport: jsonKey,
      data: sampleCounts
    };
    
  } catch (error) {
    console.error('Error generating sample count report:', error);
    throw error;
  }
};

/**
 * Generate CSV format report
 */
const generateCSVReport = (counts) => {
  let csv = 'Category,Subcategory,Count\n';
  
  // Phrases
  Object.entries(counts.byPhrase).forEach(([phrase, count]) => {
    csv += `Phrase,${phrase},${count}\n`;
  });
  
  // Gender
  Object.entries(counts.byGender).forEach(([gender, count]) => {
    csv += `Gender,${gender},${count}\n`;
  });
  
  // Age Groups
  Object.entries(counts.byAgeGroup).forEach(([ageGroup, count]) => {
    csv += `AgeGroup,${ageGroup},${count}\n`;
  });
  
  // Ethnicity
  Object.entries(counts.byEthnicity).forEach(([ethnicity, count]) => {
    csv += `Ethnicity,${ethnicity},${count}\n`;
  });
  
  // Total
  csv += `Total,All,${counts.total}\n`;
  
  return csv;
};

/**
 * Check if a phrase has met the target number of recordings
 */
export const hasMetSampleTarget = (sampleCounts, phraseLabel, target = 20) => {
  if (!sampleCounts?.counts?.byPhrase) return false;
  
  const count = sampleCounts.counts.byPhrase[phraseLabel] || 0;
  return count >= target;
};

/**
 * Get sample progress as a percentage
 */
export const getSampleProgress = (sampleCounts, phraseLabel, target = 20) => {
  if (!sampleCounts?.counts?.byPhrase) return 0;
  
  const count = sampleCounts.counts.byPhrase[phraseLabel] || 0;
  return Math.min(100, Math.round((count / target) * 100));
};

// Main export function that tries metadata first, falls back to object listing
export const fetchSampleCounts = fetchSampleCountsFromMetadata;
