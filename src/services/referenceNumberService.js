/**
 * Reference Number Service
 * Handles generation, storage, and verification of unique reference numbers
 * for ICU dataset application completion tracking and consent withdrawal
 */

class ReferenceNumberService {
  constructor() {
    this.storageKey = 'icuAppReferenceNumbers';
    this.currentSessionKey = 'icuAppCurrentSession';
  }

  /**
   * Generate a truly unique reference number
   * Format: ICU-[TIMESTAMP_BASE36]-[RANDOM_HASH]
   * @returns {string} Unique reference number
   */
  generateReferenceNumber() {
    const timestamp = Date.now();
    const timestampBase36 = timestamp.toString(36).toUpperCase();
    
    // Generate additional randomness for uniqueness
    const randomPart = Math.random().toString(36).substr(2, 6).toUpperCase();
    const additionalRandom = Math.random().toString(36).substr(2, 4).toUpperCase();
    
    // Create hash from timestamp and random values for extra uniqueness
    const hashInput = `${timestamp}-${randomPart}-${additionalRandom}-${Math.random()}`;
    let hash = 0;
    for (let i = 0; i < hashInput.length; i++) {
      const char = hashInput.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32bit integer
    }
    
    const hashStr = Math.abs(hash).toString(36).toUpperCase().substr(0, 6);
    
    const referenceNumber = `ICU-${timestampBase36}-${hashStr}`;
    
    // Verify uniqueness against stored reference numbers
    if (this.isReferenceNumberExists(referenceNumber)) {
      console.warn('⚠️ Reference number collision detected, regenerating...');
      return this.generateReferenceNumber(); // Recursive call for uniqueness
    }
    
    console.log('✅ Generated unique reference number:', referenceNumber);
    return referenceNumber;
  }

  /**
   * Check if a reference number already exists
   * @param {string} referenceNumber - Reference number to check
   * @returns {boolean} True if exists, false otherwise
   */
  isReferenceNumberExists(referenceNumber) {
    try {
      const storedData = localStorage.getItem(this.storageKey);
      if (!storedData) return false;
      
      const referenceData = JSON.parse(storedData);
      return referenceData.hasOwnProperty(referenceNumber);
    } catch (error) {
      console.error('Error checking reference number existence:', error);
      return false;
    }
  }

  /**
   * Create comprehensive session data structure
   * @param {Object} params - Session parameters
   * @returns {Object} Complete session data
   */
  createSessionData({
    demographicInfo,
    selectedPhrases,
    recordingsCount,
    sessionStats,
    sessionRecordingsCount
  }) {
    const sessionStartTime = sessionStats?.startTime || new Date();
    const sessionEndTime = new Date();
    const sessionDuration = sessionEndTime.getTime() - new Date(sessionStartTime).getTime();
    
    // Calculate phrases recorded and their details
    const phrasesRecorded = Object.keys(recordingsCount || {}).map(phraseKey => {
      const count = recordingsCount[phraseKey] || 0;
      return {
        phraseKey,
        recordingCount: count,
        completed: count >= 3
      };
    });

    const sessionData = {
      // Reference and timing information
      timestamp: sessionEndTime.toISOString(),
      sessionStartTime: sessionStartTime.toISOString(),
      sessionEndTime: sessionEndTime.toISOString(),
      sessionDuration: sessionDuration, // in milliseconds
      sessionDurationFormatted: this.formatDuration(sessionDuration),
      
      // User demographic information
      demographics: {
        ageGroup: demographicInfo?.ageGroup || null,
        gender: demographicInfo?.gender || null,
        ethnicity: demographicInfo?.ethnicity || null,
        userId: demographicInfo?.userId || null
      },
      
      // Recording session details
      selectedPhrases: selectedPhrases || [],
      phrasesRecorded: phrasesRecorded,
      totalRecordingsInSession: sessionRecordingsCount || 0,
      totalPhrasesCompleted: phrasesRecorded.filter(p => p.completed).length,
      
      // Detailed recording counts
      recordingCounts: recordingsCount || {},
      
      // Session statistics
      sessionStats: {
        recentRecordings: sessionStats?.recentRecordings || [],
        startTime: sessionStartTime.toISOString()
      },
      
      // Metadata
      applicationVersion: '21.6.25',
      dataStructureVersion: '1.0',
      consentWithdrawalInfo: {
        canWithdraw: true,
        contactInfo: 'Contact the ICU Dataset project team with this reference number to withdraw consent'
      }
    };

    return sessionData;
  }

  /**
   * Store reference number with associated session data
   * @param {string} referenceNumber - The reference number
   * @param {Object} sessionData - Complete session data
   * @returns {boolean} Success status
   */
  storeReferenceData(referenceNumber, sessionData) {
    try {
      // Get existing reference data
      const existingData = this.getAllReferenceData();
      
      // Add new reference data
      existingData[referenceNumber] = sessionData;
      
      // Store updated data
      localStorage.setItem(this.storageKey, JSON.stringify(existingData));
      
      // Also store current session reference for easy access
      localStorage.setItem(this.currentSessionKey, referenceNumber);
      
      console.log('✅ Reference data stored successfully:', referenceNumber);
      console.log('📊 Session data summary:', {
        demographics: sessionData.demographics,
        totalRecordings: sessionData.totalRecordingsInSession,
        phrasesCompleted: sessionData.totalPhrasesCompleted,
        sessionDuration: sessionData.sessionDurationFormatted
      });
      
      return true;
    } catch (error) {
      console.error('❌ Error storing reference data:', error);
      return false;
    }
  }

  /**
   * Generate reference number and store session data
   * @param {Object} sessionParams - Session parameters
   * @returns {Object} Reference number and success status
   */
  createSessionReference(sessionParams) {
    try {
      const referenceNumber = this.generateReferenceNumber();
      const sessionData = this.createSessionData(sessionParams);
      const success = this.storeReferenceData(referenceNumber, sessionData);

      return {
        referenceNumber,
        success,
        sessionData: success ? sessionData : null
      };
    } catch (error) {
      console.error('❌ Error creating session reference:', error);
      return {
        referenceNumber: null,
        success: false,
        sessionData: null,
        error: error.message
      };
    }
  }

  /**
   * Retrieve session data by reference number
   * @param {string} referenceNumber - Reference number to look up
   * @returns {Object|null} Session data or null if not found
   */
  getSessionData(referenceNumber) {
    try {
      const allData = this.getAllReferenceData();
      return allData[referenceNumber] || null;
    } catch (error) {
      console.error('❌ Error retrieving session data:', error);
      return null;
    }
  }

  /**
   * Get all stored reference data
   * @returns {Object} All reference data
   */
  getAllReferenceData() {
    try {
      const storedData = localStorage.getItem(this.storageKey);
      return storedData ? JSON.parse(storedData) : {};
    } catch (error) {
      console.error('❌ Error loading reference data:', error);
      return {};
    }
  }

  /**
   * Get current session reference number
   * @returns {string|null} Current session reference number
   */
  getCurrentSessionReference() {
    try {
      return localStorage.getItem(this.currentSessionKey);
    } catch (error) {
      console.error('❌ Error getting current session reference:', error);
      return null;
    }
  }

  /**
   * Verify reference number exists and return basic info
   * @param {string} referenceNumber - Reference number to verify
   * @returns {Object} Verification result
   */
  verifyReferenceNumber(referenceNumber) {
    try {
      const sessionData = this.getSessionData(referenceNumber);

      if (!sessionData) {
        return {
          exists: false,
          valid: false,
          message: 'Reference number not found'
        };
      }

      return {
        exists: true,
        valid: true,
        message: 'Reference number is valid',
        basicInfo: {
          timestamp: sessionData.timestamp,
          totalRecordings: sessionData.totalRecordingsInSession,
          phrasesCompleted: sessionData.totalPhrasesCompleted,
          sessionDuration: sessionData.sessionDurationFormatted
        }
      };
    } catch (error) {
      console.error('❌ Error verifying reference number:', error);
      return {
        exists: false,
        valid: false,
        message: 'Error during verification',
        error: error.message
      };
    }
  }

  /**
   * Format duration from milliseconds to readable string
   * @param {number} duration - Duration in milliseconds
   * @returns {string} Formatted duration
   */
  formatDuration(duration) {
    const seconds = Math.floor(duration / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);

    if (hours > 0) {
      return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    } else {
      return `${seconds}s`;
    }
  }

  /**
   * Get statistics about all stored reference numbers
   * @returns {Object} Statistics summary
   */
  getStatistics() {
    try {
      const allData = this.getAllReferenceData();
      const referenceNumbers = Object.keys(allData);

      if (referenceNumbers.length === 0) {
        return {
          totalSessions: 0,
          totalRecordings: 0,
          averageRecordingsPerSession: 0,
          oldestSession: null,
          newestSession: null
        };
      }

      let totalRecordings = 0;
      let oldestTimestamp = null;
      let newestTimestamp = null;

      referenceNumbers.forEach(refNum => {
        const session = allData[refNum];
        totalRecordings += session.totalRecordingsInSession || 0;

        const sessionTime = new Date(session.timestamp);
        if (!oldestTimestamp || sessionTime < oldestTimestamp) {
          oldestTimestamp = sessionTime;
        }
        if (!newestTimestamp || sessionTime > newestTimestamp) {
          newestTimestamp = sessionTime;
        }
      });

      return {
        totalSessions: referenceNumbers.length,
        totalRecordings,
        averageRecordingsPerSession: Math.round(totalRecordings / referenceNumbers.length * 100) / 100,
        oldestSession: oldestTimestamp?.toISOString(),
        newestSession: newestTimestamp?.toISOString()
      };
    } catch (error) {
      console.error('❌ Error calculating statistics:', error);
      return null;
    }
  }

  /**
   * Clear all reference data (for testing purposes)
   * @returns {boolean} Success status
   */
  clearAllData() {
    try {
      localStorage.removeItem(this.storageKey);
      localStorage.removeItem(this.currentSessionKey);
      console.log('🗑️ All reference data cleared');
      return true;
    } catch (error) {
      console.error('❌ Error clearing reference data:', error);
      return false;
    }
  }

  /**
   * Export all reference data for backup
   * @returns {string} JSON string of all data
   */
  exportData() {
    try {
      const allData = this.getAllReferenceData();
      return JSON.stringify(allData, null, 2);
    } catch (error) {
      console.error('❌ Error exporting data:', error);
      return null;
    }
  }
}

// Create and export singleton instance
const referenceNumberService = new ReferenceNumberService();
export default referenceNumberService;
