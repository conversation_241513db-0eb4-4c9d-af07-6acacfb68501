/**
 * Sample Count Service
 * Uses S3-based counting for real-time sample tracking
 */

import { 
  fetchSampleCounts as fetchFromS3, 
  hasMetSampleTarget as checkTarget,
  getSampleProgress as getProgress,
  generateSampleCountReport
} from './s3SampleCountService';

/**
 * Fetch current sample counts from S3
 * @returns {Promise<Object>} - Sample counts data
 */
export const fetchSampleCounts = async () => {
  console.log('Fetching sample counts from S3');
  
  try {
    // Use S3-based counting for real data
    const data = await fetchFromS3();
    return data;
  } catch (error) {
    console.error('Error fetching sample counts:', error);
    // Return mock data structure on error
    return {
      success: false,
      error: error.message,
      counts: getMockSampleCounts().counts
    };
  }
};

/**
 * Generate mock sample count data for testing
 * @returns {Object} - Mock sample counts
 */
function getMockSampleCounts() {
  return {
    success: true,
    counts: {
      byPhrase: {
        "i_need_water": 15,
        "i_am_in_pain": 8,
        "i_cant_breathe": 12,
        "i_need_to_use_the_bathroom": 6,
        "please_adjust_my_position": 10,
        "i_am_cold": 5,
        "i_am_hot": 4,
        "i_feel_nauseous": 7,
        "i_need_my_medication": 9,
        "thank_you": 20
      },
      byGender: { male: 35, female: 42, nonbinary: 19 },
      byAgeGroup: { '18to39': 38, '40to64': 33, '65plus': 25 },
      byEthnicity: {
        'asian': 12,
        'caucasian': 18,
        'aboriginal': 6,
        'african': 15,
        'latinx': 17,
        'middle_eastern': 10,
        'pacific_islander': 8,
        'indigenous': 10
      },
      total: 96
    },
    lastUpdated: new Date().toISOString()
  };
}

/**
 * Check if a phrase has met the target number of recordings
 * @param {Object} sampleCounts - The sample counts data
 * @param {string} phraseLabel - The phrase to check
 * @param {number} target - The target number of recordings (default: 20)
 * @returns {boolean} - Whether the target has been met
 */
export const hasMetSampleTarget = checkTarget;

/**
 * Get sample progress as a percentage
 * @param {Object} sampleCounts - The sample counts data
 * @param {string} phraseLabel - The phrase to check
 * @param {number} target - The target number of recordings (default: 20) 
 * @returns {number} - The percentage completion (0-100)
 */
export const getSampleProgress = getProgress;

/**
 * Generate and upload sample count report
 * @returns {Promise<Object>} - Report generation result
 */
export const generateReport = async () => {
  try {
    return await generateSampleCountReport();
  } catch (error) {
    console.error('Error generating report:', error);
    throw error;
  }
};
