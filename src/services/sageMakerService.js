/**
 * SageMaker Service
 * Connects to AWS SageMaker for model evaluation metrics and demographic-based analysis
 */

// Define API endpoints
const API_BASE = process.env.REACT_APP_API_BASE || 'http://localhost:3030';
const METRICS_ENDPOINT = `${API_BASE}/api/metrics`;

/**
 * Submit evaluation metrics to SageMaker by demographic category
 * @param {Object} metrics - The evaluation metrics to submit
 * @param {Object} demographics - The demographic information for the metrics
 * @returns {Promise<Object>} - The response from the server
 */
export const submitEvaluationMetrics = async (metrics, demographics) => {
  try {
    // For testing UI changes without server connection, use mock data
    if (process.env.REACT_APP_MOCK_API === 'true') {
      return mockSubmitMetrics(metrics, demographics);
    }
    
    const response = await fetch(METRICS_ENDPOINT, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      body: JSON.stringify({
        metrics,
        demographics,
        timestamp: new Date().toISOString()
      }),
    });
    
    if (!response.ok) {
      throw new Error(`Server returned status: ${response.status}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error('Error submitting metrics to SageMaker:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * Get evaluation metrics by demographic category
 * @param {Object} filters - Optional filters to apply to the metrics query
 * @returns {Promise<Object>} - The metrics data from SageMaker
 */
export const getEvaluationMetrics = async (filters = {}) => {
  try {
    // For testing UI changes without server connection, use mock data
    if (process.env.REACT_APP_MOCK_API === 'true') {
      return getMockMetrics(filters);
    }
    
    const queryParams = new URLSearchParams();
    
    // Add any filters to the query params
    Object.entries(filters).forEach(([key, value]) => {
      queryParams.append(key, value);
    });
    
    const response = await fetch(`${METRICS_ENDPOINT}?${queryParams.toString()}`, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
      },
    });
    
    if (!response.ok) {
      throw new Error(`Server returned status: ${response.status}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error('Error fetching metrics from SageMaker:', error);
    return {
      success: false,
      error: error.message,
      metrics: getMockMetrics(filters).metrics
    };
  }
};

/**
 * Mock function for submitting metrics (for testing without SageMaker)
 * @param {Object} metrics - The evaluation metrics
 * @param {Object} demographics - The demographic information
 * @returns {Object} - Mock response
 */
function mockSubmitMetrics(metrics, demographics) {
  console.log('MOCK: Submitting metrics to SageMaker:', { metrics, demographics });
  
  return {
    success: true,
    message: 'Metrics submitted successfully (mock)',
    timestamp: new Date().toISOString()
  };
}

/**
 * Generate mock evaluation metrics data for testing
 * @param {Object} filters - Optional filters to apply
 * @returns {Object} - Mock metrics data
 */
function getMockMetrics(filters = {}) {
  // Create mock metrics data with demographic breakdown
  const mockData = {
    success: true,
    metrics: {
      overall: {
        accuracy: 0.87,
        precision: 0.89,
        recall: 0.86,
        f1Score: 0.875,
        confidenceScore: 0.92,
        sampleCount: 1250
      },
      byDemographic: {
        gender: {
          male: {
            accuracy: 0.89,
            precision: 0.91,
            recall: 0.88,
            f1Score: 0.895,
            confidenceScore: 0.94,
            sampleCount: 480
          },
          female: {
            accuracy: 0.86,
            precision: 0.88,
            recall: 0.85,
            f1Score: 0.865,
            confidenceScore: 0.91,
            sampleCount: 530
          },
          nonbinary: {
            accuracy: 0.83,
            precision: 0.85,
            recall: 0.82,
            f1Score: 0.835,
            confidenceScore: 0.88,
            sampleCount: 240
          }
        },
        ageGroup: {
          '18to39': {
            accuracy: 0.91,
            precision: 0.92,
            recall: 0.90,
            f1Score: 0.91,
            confidenceScore: 0.95,
            sampleCount: 450
          },
          '40to64': {
            accuracy: 0.86,
            precision: 0.88,
            recall: 0.85,
            f1Score: 0.865,
            confidenceScore: 0.90,
            sampleCount: 520
          },
          '65plus': {
            accuracy: 0.82,
            precision: 0.84,
            recall: 0.80,
            f1Score: 0.82,
            confidenceScore: 0.87,
            sampleCount: 280
          }
        },
        ethnicity: {
          asian: {
            accuracy: 0.88,
            precision: 0.90,
            recall: 0.87,
            f1Score: 0.885,
            confidenceScore: 0.92,
            sampleCount: 180
          },
          caucasian: {
            accuracy: 0.90,
            precision: 0.92,
            recall: 0.89,
            f1Score: 0.905,
            confidenceScore: 0.94,
            sampleCount: 320
          },
          aboriginal: {
            accuracy: 0.83,
            precision: 0.85,
            recall: 0.82,
            f1Score: 0.835,
            confidenceScore: 0.88,
            sampleCount: 120
          },
          african: {
            accuracy: 0.85,
            precision: 0.87,
            recall: 0.84,
            f1Score: 0.855,
            confidenceScore: 0.90,
            sampleCount: 170
          },
          latinx: {
            accuracy: 0.86,
            precision: 0.88,
            recall: 0.85,
            f1Score: 0.865,
            confidenceScore: 0.91,
            sampleCount: 190
          },
          middle_eastern: {
            accuracy: 0.84,
            precision: 0.86,
            recall: 0.83,
            f1Score: 0.845,
            confidenceScore: 0.89,
            sampleCount: 90
          },
          pacific_islander: {
            accuracy: 0.82,
            precision: 0.84,
            recall: 0.81,
            f1Score: 0.825,
            confidenceScore: 0.87,
            sampleCount: 80
          },
          indigenous: {
            accuracy: 0.81,
            precision: 0.83,
            recall: 0.80,
            f1Score: 0.815,
            confidenceScore: 0.86,
            sampleCount: 100
          }
        }
      }
    },
    lastUpdated: new Date().toISOString()
  };
  
  // Apply any filters
  if (Object.keys(filters).length > 0) {
    // This would filter the mock data based on provided filters
    // Not fully implemented for mock data
    console.log('MOCK: Applying filters to metrics:', filters);
  }
  
  return mockData;
}
