import React, { useState, useEffect } from 'react';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { Container, Typography, Box, Paper, Snackbar, Alert, CircularProgress, Button, CssBaseline } from '@mui/material';

const App = () => {
  const [currentStep, setCurrentStep] = useState('consent');
  const [hasConsent, setHasConsent] = useState(false);
  const [showCollectionTracker, setShowCollectionTracker] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [notification, setNotification] = useState({ open: false, message: '', severity: 'info' });
  
  const handleCloseNotification = () => {
    setNotification({ ...notification, open: false });
  };
  
  return (
    <div className="App">
      <ThemeProvider theme={createTheme()}>
        <CssBaseline />
        
        <Container maxWidth="lg">
          {!hasConsent ? (
            <>
              <Typography variant="h5">Consent Page</Typography>
            </>
          ) : currentStep === 'recording' ? (
            <>
              <Paper elevation={3} sx={{ p: 3, mb: 4 }}>
                <Typography variant="h5" gutterBottom>
                  ICU Communication Phrase Video Collection
                </Typography>
                <Button
                  variant="outlined"
                  color="primary"
                  size="small"
                  onClick={() => setShowCollectionTracker(true)}
                >
                  Show Progress
                </Button>
              </Paper>
              
              {showCollectionTracker ? (
                <Typography>Collection Tracker</Typography>
              ) : (
                <Container maxWidth="md">
                  <Paper elevation={3}>
                    <Typography>Recording Interface</Typography>
                  </Paper>
                  
                  {uploading && (
                    <Box sx={{ display: 'flex', justifyContent: 'center' }}>
                      <CircularProgress size={24} />
                      <Typography>Uploading video...</Typography>
                    </Box>
                  )}
                </Container>
              )}
            </>
          ) : (
            <Typography>Other Step</Typography>
          )}
        </Container>
        
        <Snackbar 
          open={notification.open} 
          autoHideDuration={6000} 
          onClose={handleCloseNotification}
        >
          <Alert onClose={handleCloseNotification} severity={notification.severity}>
            {notification.message}
          </Alert>
        </Snackbar>
      </ThemeProvider>
    </div>
  );
};

export default App;
