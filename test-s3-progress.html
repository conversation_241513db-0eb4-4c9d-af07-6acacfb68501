<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>S3 Progress Tracking Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-button {
            background: #009688;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 5px;
            font-weight: bold;
        }
        .test-button:hover {
            background: #00796b;
        }
        .result {
            background: #f0f0f0;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success { background: #e8f5e8; border-left: 4px solid #4caf50; }
        .error { background: #ffeaea; border-left: 4px solid #f44336; }
        .info { background: #e3f2fd; border-left: 4px solid #2196f3; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 S3 Progress Tracking Test</h1>
        <p>This page tests the S3 progress tracking functionality for the ICU dataset application completion page.</p>
        
        <div>
            <button class="test-button" onclick="testBackendAPI()">🔗 Test Backend API</button>
            <button class="test-button" onclick="testS3Connection()">☁️ Test S3 Connection</button>
            <button class="test-button" onclick="testProgressService()">📊 Test Progress Service</button>
            <button class="test-button" onclick="openCompletionPage()">🎯 Open Completion Page</button>
        </div>
        
        <div id="results"></div>
    </div>

    <script>
        function addResult(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${type}`;
            resultDiv.textContent = message;
            resultsDiv.appendChild(resultDiv);
        }

        async function testBackendAPI() {
            addResult('Testing backend API connection...', 'info');
            
            try {
                // Test health endpoint
                const healthResponse = await fetch('http://localhost:5000/health');
                const healthData = await healthResponse.json();
                addResult(`✅ Health check successful: ${JSON.stringify(healthData, null, 2)}`, 'success');
                
                // Test sample counts endpoint
                const countsResponse = await fetch('http://localhost:5000/api/sample-counts');
                const countsData = await countsResponse.json();
                addResult(`✅ Sample counts API successful:
Total recordings: ${countsData.counts.total}
By phrase count: ${Object.keys(countsData.counts.byPhrase).length} phrases
Sample phrases: ${Object.keys(countsData.counts.byPhrase).slice(0, 5).join(', ')}
Last updated: ${countsData.lastUpdated}`, 'success');
                
            } catch (error) {
                addResult(`❌ Backend API test failed: ${error.message}`, 'error');
            }
        }

        async function testS3Connection() {
            addResult('Testing S3 connection via backend...', 'info');
            
            try {
                const response = await fetch('http://localhost:5000/api/test-s3');
                const data = await response.json();
                
                if (data.success) {
                    addResult(`✅ S3 connection successful:
Bucket: ${data.bucket}
Region: ${data.region}
Object count: ${data.objectCount}
Sample files: ${data.sampleFiles?.map(f => f.key).join(', ') || 'None'}`, 'success');
                } else {
                    addResult(`❌ S3 connection failed: ${data.error}`, 'error');
                }
            } catch (error) {
                addResult(`❌ S3 connection test failed: ${error.message}`, 'error');
            }
        }

        async function testProgressService() {
            addResult('Testing progress service integration...', 'info');
            
            try {
                // This would normally be done through the React app
                addResult('⚠️ Progress service test requires React app context. Please use the completion page test instead.', 'info');
                
                // Show what the API returns
                const response = await fetch('http://localhost:5000/api/sample-counts');
                const data = await response.json();
                
                if (data.success) {
                    const totalPhrases = Object.keys(data.counts.byPhrase).length;
                    const totalRecordings = data.counts.total;
                    const completedPhrases = Object.values(data.counts.byPhrase).filter(count => count >= 20).length;
                    const progress = totalPhrases > 0 ? Math.round((completedPhrases / totalPhrases) * 100) : 0;
                    
                    addResult(`📊 Progress calculation preview:
Total phrases with recordings: ${totalPhrases}
Total recordings: ${totalRecordings}
Phrases with 20+ recordings: ${completedPhrases}
Estimated progress: ${progress}%

Top phrases by count:
${Object.entries(data.counts.byPhrase)
  .sort(([,a], [,b]) => b - a)
  .slice(0, 10)
  .map(([phrase, count]) => `  ${phrase}: ${count}`)
  .join('\n')}`, 'success');
                }
            } catch (error) {
                addResult(`❌ Progress service test failed: ${error.message}`, 'error');
            }
        }

        function openCompletionPage() {
            addResult('Opening ICU dataset application...', 'info');
            
            // Open the main app
            window.open('http://localhost:3000', '_blank');
            
            addResult(`📋 To test the completion page:
1. Complete consent and demographics forms
2. Open browser console (F12)
3. Run: window.testCompletionPage()
4. The completion page should appear with real S3 progress data

Alternative method:
1. Navigate through the app normally
2. Select phrases and complete recordings
3. The completion page will appear after the final recording`, 'info');
        }

        // Auto-run basic tests on page load
        window.addEventListener('load', () => {
            addResult('🚀 S3 Progress Tracking Test Page Loaded', 'info');
            addResult('Click the buttons above to run specific tests.', 'info');
        });
    </script>
</body>
</html>
