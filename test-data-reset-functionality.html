<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ICU Dataset Application - Data Reset Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .button {
            background-color: #009688;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .button:hover {
            background-color: #00796b;
        }
        .button.danger {
            background-color: #f44336;
        }
        .button.danger:hover {
            background-color: #d32f2f;
        }
        .output {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin-top: 10px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .success { color: #28a745; }
        .warning { color: #ffc107; }
        .error { color: #dc3545; }
        .info { color: #17a2b8; }
        h1 { color: #009688; }
        h2 { color: #00796b; }
        .status-box {
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .status-clean {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .status-dirty {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔒 ICU Dataset Application - Data Reset Test</h1>
        <p><strong>Purpose:</strong> Test the complete data reset functionality that ensures privacy between different users.</p>
        
        <div class="test-section">
            <h2>📊 Current Data Status</h2>
            <button class="button" onclick="checkDataStatus()">Check Current Data</button>
            <div id="data-status" class="output"></div>
        </div>

        <div class="test-section">
            <h2>🧪 Simulate User Data</h2>
            <p>Create sample user data to test the reset functionality:</p>
            <button class="button" onclick="createSampleData()">Create Sample User Data</button>
            <div id="sample-data-output" class="output"></div>
        </div>

        <div class="test-section">
            <h2>🔄 Test Data Reset</h2>
            <p><strong>Instructions:</strong></p>
            <ol>
                <li>Click "Create Sample User Data" above</li>
                <li>Verify data exists by clicking "Check Current Data"</li>
                <li>Refresh this page (F5 or Ctrl+R)</li>
                <li>Check if data was cleared automatically</li>
            </ol>
            <button class="button" onclick="window.location.reload()">🔄 Refresh Page (Test Reset)</button>
        </div>

        <div class="test-section">
            <h2>🧹 Manual Data Clear</h2>
            <p>For testing purposes only - manually clear all data:</p>
            <button class="button danger" onclick="manualDataClear()">⚠️ Manual Clear All Data</button>
            <div id="manual-clear-output" class="output"></div>
        </div>

        <div class="test-section">
            <h2>🚀 Application Access</h2>
            <p>Open the ICU Dataset Application to verify it starts with clean state:</p>
            <button class="button" onclick="openApplication()">Open ICU Dataset Application</button>
        </div>
    </div>

    <script>
        function addOutput(elementId, message, type = 'info') {
            const output = document.getElementById(elementId);
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'success' ? 'success' : 
                            type === 'warning' ? 'warning' : 
                            type === 'error' ? 'error' : 'info';
            output.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            output.scrollTop = output.scrollHeight;
        }

        function clearOutput(elementId) {
            document.getElementById(elementId).innerHTML = '';
        }

        function checkDataStatus() {
            clearOutput('data-status');
            addOutput('data-status', '🔍 Checking localStorage for ICU app data...', 'info');
            
            const icuKeys = [];
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key && (key.startsWith('icuApp') || key.startsWith('icu_'))) {
                    icuKeys.push(key);
                }
            }
            
            if (icuKeys.length === 0) {
                addOutput('data-status', '✅ CLEAN STATE: No ICU app data found in localStorage', 'success');
                addOutput('data-status', '🔒 Privacy ensured - ready for new user', 'success');
            } else {
                addOutput('data-status', `⚠️ DIRTY STATE: Found ${icuKeys.length} ICU app keys:`, 'warning');
                icuKeys.forEach(key => {
                    const value = localStorage.getItem(key);
                    addOutput('data-status', `  🔑 ${key}: ${value ? value.substring(0, 50) + '...' : 'null'}`, 'warning');
                });
            }
            
            // Check sessionStorage
            const sessionKeys = [];
            for (let i = 0; i < sessionStorage.length; i++) {
                const key = sessionStorage.key(i);
                if (key && key.startsWith('icuApp')) {
                    sessionKeys.push(key);
                }
            }
            
            if (sessionKeys.length > 0) {
                addOutput('data-status', `📝 SessionStorage: Found ${sessionKeys.length} ICU app keys`, 'info');
            } else {
                addOutput('data-status', '📝 SessionStorage: Clean', 'success');
            }
        }

        function createSampleData() {
            clearOutput('sample-data-output');
            addOutput('sample-data-output', '🧪 Creating sample user data...', 'info');
            
            try {
                // Simulate user data that would be created during app usage
                const sampleData = {
                    demographics: { age: '25-34', gender: 'Female', ethnicity: 'European' },
                    selectedPhrases: [{ phrase: 'Hello', category: 'Greetings' }],
                    recordingsCount: { 'Greetings:Hello': 2 },
                    receiptCounter: 5,
                    sessionActive: true
                };
                
                localStorage.setItem('icuAppDemographics', JSON.stringify(sampleData.demographics));
                localStorage.setItem('icu_selected_phrases', JSON.stringify(sampleData.selectedPhrases));
                localStorage.setItem('icuAppRecordingsCount', JSON.stringify(sampleData.recordingsCount));
                localStorage.setItem('icuAppReceiptCounter', sampleData.receiptCounter.toString());
                localStorage.setItem('icuAppReceiptMapping_000005', JSON.stringify({ videos: ['test.mp4'] }));
                sessionStorage.setItem('icuAppSessionActive', 'true');
                
                addOutput('sample-data-output', '✅ Sample data created successfully:', 'success');
                addOutput('sample-data-output', '  📋 Demographics: Age 25-34, Female, European', 'info');
                addOutput('sample-data-output', '  📝 Selected phrases: Hello (Greetings)', 'info');
                addOutput('sample-data-output', '  🎥 Recording counts: 2 recordings', 'info');
                addOutput('sample-data-output', '  🧾 Receipt counter: 5', 'info');
                addOutput('sample-data-output', '  🗂️ Receipt mapping: 000005', 'info');
                addOutput('sample-data-output', '  🔄 Session active: true', 'info');
                
            } catch (error) {
                addOutput('sample-data-output', `❌ Error creating sample data: ${error.message}`, 'error');
            }
        }

        function manualDataClear() {
            clearOutput('manual-clear-output');
            addOutput('manual-clear-output', '🧹 Manually clearing all data...', 'warning');
            
            try {
                const beforeCount = localStorage.length;
                localStorage.clear();
                sessionStorage.clear();
                
                addOutput('manual-clear-output', `✅ Cleared ${beforeCount} localStorage items`, 'success');
                addOutput('manual-clear-output', '✅ Cleared all sessionStorage items', 'success');
                addOutput('manual-clear-output', '🔒 All data cleared - ready for fresh start', 'success');
                
            } catch (error) {
                addOutput('manual-clear-output', `❌ Error clearing data: ${error.message}`, 'error');
            }
        }

        function openApplication() {
            window.open('http://localhost:3006', '_blank');
        }

        // Auto-check data status on page load
        window.addEventListener('load', function() {
            setTimeout(checkDataStatus, 500);
        });
    </script>
</body>
</html>
