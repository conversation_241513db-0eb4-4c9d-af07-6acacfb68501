<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ICU App - Quick Verification</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .container {
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        }
        .status-box {
            background: rgba(255,255,255,0.2);
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
            border-left: 5px solid #4CAF50;
        }
        .error-box {
            background: rgba(255,0,0,0.2);
            border-left-color: #f44336;
        }
        .warning-box {
            background: rgba(255,193,7,0.2);
            border-left-color: #ff9800;
        }
        .test-button {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            cursor: pointer;
            margin: 10px;
            text-decoration: none;
            display: inline-block;
            transition: transform 0.2s;
        }
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }
        .app-button {
            background: linear-gradient(45deg, #2196F3, #1976D2);
            font-size: 18px;
            padding: 20px 40px;
        }
        #results {
            margin-top: 20px;
        }
        .step {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            margin: 15px 0;
            border-radius: 10px;
            border-left: 5px solid #2196F3;
        }
        .step h3 {
            margin-top: 0;
            color: #FFD700;
        }
        .checklist {
            list-style: none;
            padding: 0;
        }
        .checklist li {
            padding: 8px 0;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }
        .checklist li:before {
            content: "☐ ";
            color: #FFD700;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏥 ICU Dataset Application - Quick Verification</h1>
        <p><strong>Current Status:</strong> Servers running successfully after 5-hour troubleshooting session</p>
        
        <div class="status-box">
            <strong>✅ Backend Server:</strong> Running on port 5000<br>
            <strong>✅ Frontend Server:</strong> Running on port 3001<br>
            <strong>⏰ Time to verify:</strong> 5-10 minutes
        </div>

        <div class="step">
            <h3>🎯 STEP 1: Open the Application</h3>
            <p><strong>Click this button to open the ICU Dataset Application:</strong></p>
            <a href="http://localhost:3001" target="_blank" class="test-button app-button">
                🚀 Open ICU Dataset Application
            </a>
            <p><em>This should open the consent page in a new tab</em></p>
        </div>

        <div class="step">
            <h3>📋 STEP 2: Visual Verification Checklist</h3>
            <p><strong>When the application opens, verify these elements are visible:</strong></p>
            <ul class="checklist">
                <li>Page title shows "ICU Dataset Application" or similar</li>
                <li>Consent form text is displayed clearly</li>
                <li>Medical/hospital images are visible (patient, nurse, equipment)</li>
                <li>Consent button ("I Consent" or "Accept") is present</li>
                <li>Professional medical styling with proper colors</li>
                <li>No overlapping text or broken layout</li>
                <li>Page loads completely (no infinite loading spinner)</li>
            </ul>
        </div>

        <div class="step">
            <h3>🔧 STEP 3: Check for Errors</h3>
            <p><strong>In the application tab, press F12 and check:</strong></p>
            <ul class="checklist">
                <li>Console tab shows no red error messages</li>
                <li>Network tab shows no failed requests (red status codes)</li>
                <li>No CORS errors or "Failed to fetch" messages</li>
            </ul>
        </div>

        <div class="step">
            <h3>🔗 STEP 4: Test Backend Connection</h3>
            <p><strong>Click to test backend health:</strong></p>
            <a href="http://localhost:5000/health" target="_blank" class="test-button">
                ⚕️ Test Backend Health
            </a>
            <p><em>Should show JSON response with "status": "healthy"</em></p>
        </div>

        <div class="step">
            <h3>🎮 STEP 5: Quick User Flow Test</h3>
            <p><strong>In the main application:</strong></p>
            <ul class="checklist">
                <li>Click the consent button</li>
                <li>Fill out demographics form (any values)</li>
                <li>Navigate through to phrase selection</li>
                <li>Verify each step loads without errors</li>
            </ul>
        </div>

        <div id="results">
            <h3>🚨 If You Encounter Issues:</h3>
            <div class="warning-box">
                <strong>Blank Page:</strong> Check browser console for errors<br>
                <strong>Loading Forever:</strong> Restart React server<br>
                <strong>404 Error:</strong> Verify URL is exactly http://localhost:3001<br>
                <strong>Backend Fails:</strong> Restart backend server
            </div>
        </div>

        <div class="status-box">
            <h3>✅ Success Criteria</h3>
            <p><strong>You're ready for full testing when:</strong></p>
            <ul>
                <li>✅ Consent page displays all elements correctly</li>
                <li>✅ No red errors in browser console</li>
                <li>✅ Backend health endpoint responds</li>
                <li>✅ Can navigate through user flow</li>
            </ul>
            <p><strong>Next Phase:</strong> Test automatic phrase progression (3 recordings → auto-advance)</p>
        </div>

        <div class="step">
            <h3>📞 Need Help?</h3>
            <p>If verification fails after following troubleshooting steps, provide:</p>
            <ul>
                <li>Screenshot of what you see in browser</li>
                <li>Any error messages from browser console</li>
                <li>Which step failed</li>
            </ul>
        </div>
    </div>

    <script>
        // Auto-test connectivity when page loads
        window.addEventListener('load', async function() {
            try {
                const response = await fetch('http://localhost:5000/health');
                if (response.ok) {
                    console.log('✅ Backend connectivity verified');
                } else {
                    console.warn('⚠️ Backend responded with status:', response.status);
                }
            } catch (error) {
                console.error('❌ Backend connectivity failed:', error.message);
            }
        });
    </script>
</body>
</html>
