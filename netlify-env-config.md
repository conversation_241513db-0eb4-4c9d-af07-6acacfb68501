# Netlify Environment Variables Configuration

## Required Environment Variables for Production Deployment

Add these environment variables in your Netlify dashboard:

### AWS Configuration (Required for Direct S3 Upload)
```
REACT_APP_AWS_IDENTITY_POOL_ID=ap-southeast-2:59baeaf9-a80c-4afc-8f27-f4d6191242dd
REACT_APP_AWS_REGION=ap-southeast-2
REACT_APP_S3_BUCKET=icudatasetphrasesfortesting
```

### Application Configuration
```
REACT_APP_NAME=ICU Dataset Application
REACT_APP_VERSION=1.0.0
NODE_ENV=production
REACT_APP_DEBUG=false
```

### Backend Configuration (Optional - for hybrid mode)
```
REACT_APP_BACKEND_URL=https://your-backend-server.herokuapp.com
```

## How to Add Environment Variables in Netlify:

1. Go to your Netlify site dashboard
2. Click "Site settings"
3. Click "Environment variables" in the sidebar
4. Click "Add variable" for each variable above
5. Redeploy your site

## Verification Steps:

1. Check browser console for AWS configuration logs
2. Test video recording and upload
3. Verify uploads appear in S3 bucket
4. Monitor Network tab for any CORS errors

## Troubleshooting:

If you still get network errors:
1. Check browser DevTools Console for specific error messages
2. Check Network tab for failed requests
3. Verify AWS Cognito Identity Pool permissions
4. Check S3 bucket CORS policy includes your Netlify domain
