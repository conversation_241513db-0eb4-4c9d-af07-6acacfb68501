# 🚀 Netlify + EC2 Deployment Configuration

## ✅ EC2 Server Status: OPERATIONAL

**Server URL**: `http://*************:5000`  
**Health Check**: ✅ Responding  
**CORS Configuration**: ✅ Configured for Netlify domains  
**Security Group**: ✅ Port 5000 open  

---

## 📋 Netlify Environment Variables

### **Required Environment Variables for Netlify Dashboard:**

```bash
# AWS Configuration (Frontend Direct Upload)
REACT_APP_AWS_IDENTITY_POOL_ID=ap-southeast-2:59baeaf9-a80c-4afc-8f27-f4d6191242dd
REACT_APP_AWS_REGION=ap-southeast-2
REACT_APP_S3_BUCKET=icudatasetphrasesfortesting

# Backend Configuration (EC2 Server)
REACT_APP_BACKEND_URL=http://*************:5000

# Application Configuration
REACT_APP_NAME=ICU Dataset Application
REACT_APP_VERSION=1.0.0
NODE_ENV=production
REACT_APP_DEBUG=false
```

### **How to Add Environment Variables in Netlify:**

1. **Go to Netlify Dashboard**
   - Navigate to your site dashboard
   - Click on "Site settings"

2. **Environment Variables Section**
   - Click on "Environment variables" in the left sidebar
   - Click "Add a variable" for each variable above

3. **Add Each Variable**
   - **Key**: Copy the variable name (e.g., `REACT_APP_AWS_IDENTITY_POOL_ID`)
   - **Value**: Copy the corresponding value
   - **Scopes**: Select "All deploy contexts" or "Production"

---

## 🔧 Build Configuration

### **Netlify Build Settings:**

```toml
# netlify.toml (create this file in your project root)
[build]
  publish = "build"
  command = "npm run build"

[build.environment]
  NODE_VERSION = "18"

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
```

---

## 🧪 Testing Endpoints

### **EC2 Server Endpoints (All Operational):**

1. **Health Check**: `http://*************:5000/health`
   - ✅ Status: Operational
   - ✅ CORS: Configured for Netlify

2. **Receipt Generation**: `http://*************:5000/api/receipt/generate`
   - ⚠️ Status: Needs AWS credentials configuration
   - ✅ Endpoint: Responding

3. **Receipt Counter**: `http://*************:5000/api/receipt/counter`
   - ⚠️ Status: Needs AWS credentials configuration
   - ✅ Endpoint: Responding

4. **Upload Endpoint**: `http://*************:5000/upload`
   - ✅ Status: Ready for testing
   - ✅ CORS: Configured

### **Test Page Available:**
Use `test-ec2-server-endpoints.html` to test all endpoints from your browser.

---

## 🔒 Security Considerations

### **Current Security Setup:**

1. **EC2 Security Group**:
   - ✅ Port 5000: Open to 0.0.0.0/0 (required for Netlify access)
   - ✅ SSH Port 22: Restricted to your IP

2. **CORS Configuration**:
   - ✅ Allows Netlify domains (*.netlify.app, *.netlify.com)
   - ✅ Credentials enabled for authenticated requests

3. **AWS Credentials**:
   - ✅ Stored securely in EC2 .env file
   - ⚠️ Need to verify AWS service initialization

---

## 🚀 Deployment Steps

### **Step 1: Deploy to Netlify**
1. Connect your GitHub repository to Netlify
2. Set the environment variables listed above
3. Deploy the site

### **Step 2: Update CORS for Your Netlify Domain**
Once you get your Netlify URL (e.g., `https://your-app.netlify.app`), you may need to add it specifically to the CORS configuration if needed.

### **Step 3: Test End-to-End Connectivity**
1. Open your Netlify-deployed app
2. Test video recording functionality
3. Verify receipt generation works
4. Check AWS S3 uploads

---

## 🔧 Troubleshooting

### **Common Issues & Solutions:**

1. **CORS Errors**:
   - Verify Netlify domain is allowed in server CORS config
   - Check browser console for specific CORS error messages

2. **AWS Credential Issues**:
   - Restart EC2 server to reload environment variables
   - Verify .env file has correct AWS credentials

3. **Connection Timeouts**:
   - Verify EC2 security group allows port 5000
   - Check if EC2 server is running: `curl http://*************:5000/health`

4. **Receipt Generation Fails**:
   - Check AWS credentials are properly loaded
   - Verify S3 bucket permissions

---

## 📊 Current Status Summary

### ✅ **Working Components:**
- EC2 server running and accessible
- CORS configured for Netlify domains
- Health endpoint responding
- Security group properly configured
- Environment variables set on EC2

### ⚠️ **Needs Attention:**
- AWS credentials initialization (server restart may resolve)
- Netlify deployment with environment variables
- End-to-end testing once deployed

### 🎯 **Next Steps:**
1. Deploy to Netlify with the environment variables above
2. Test the deployed application
3. Verify video recording and receipt generation work
4. Monitor for any CORS or connectivity issues

---

**🎉 Ready for Netlify Deployment!**

The EC2 backend is operational and configured to work with your Netlify frontend. Use the environment variables above in your Netlify dashboard and deploy your application.
