#!/bin/bash

echo "🏥 ICU Dataset Application - Comprehensive Verification"
echo "======================================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print status
print_status() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✅ $2${NC}"
    else
        echo -e "${RED}❌ $2${NC}"
    fi
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if servers are running
echo -e "\n${BLUE}1. Checking Server Status${NC}"
echo "=========================="

# Check backend server
print_info "Testing backend server (localhost:5000)..."
BACKEND_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:5000/health 2>/dev/null)
if [ "$BACKEND_STATUS" = "200" ]; then
    print_status 0 "Backend server is running and healthy"
    # Get backend details
    BACKEND_INFO=$(curl -s http://localhost:5000/health 2>/dev/null)
    echo "   Backend info: $(echo $BACKEND_INFO | jq -r '.status // "unknown"') (uptime: $(echo $BACKEND_INFO | jq -r '.uptime // "unknown"')s)"
else
    print_status 1 "Backend server is not responding (HTTP $BACKEND_STATUS)"
fi

# Check frontend server
print_info "Testing frontend server (localhost:3000)..."
FRONTEND_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3000 2>/dev/null)
if [ "$FRONTEND_STATUS" = "200" ]; then
    print_status 0 "Frontend server is running and serving content"
else
    print_status 1 "Frontend server is not responding (HTTP $FRONTEND_STATUS)"
fi

# Check alternative frontend access
print_info "Testing alternative frontend access (127.0.0.1:3000)..."
ALT_FRONTEND_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://127.0.0.1:3000 2>/dev/null)
if [ "$ALT_FRONTEND_STATUS" = "200" ]; then
    print_status 0 "Alternative frontend access working"
else
    print_status 1 "Alternative frontend access not working (HTTP $ALT_FRONTEND_STATUS)"
fi

# Check network access
print_info "Testing network access (*************:3000)..."
NETWORK_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://*************:3000 2>/dev/null)
if [ "$NETWORK_STATUS" = "200" ]; then
    print_status 0 "Network access working"
else
    print_status 1 "Network access not working (HTTP $NETWORK_STATUS)"
fi

# Check port usage
echo -e "\n${BLUE}2. Port Usage Analysis${NC}"
echo "======================"

print_info "Checking port 3000 usage..."
PORT_3000=$(lsof -ti:3000 2>/dev/null)
if [ -n "$PORT_3000" ]; then
    print_status 0 "Port 3000 is in use (PID: $PORT_3000)"
    PROCESS_3000=$(ps -p $PORT_3000 -o comm= 2>/dev/null)
    echo "   Process: $PROCESS_3000"
else
    print_status 1 "Port 3000 is not in use"
fi

print_info "Checking port 5000 usage..."
PORT_5000=$(lsof -ti:5000 2>/dev/null)
if [ -n "$PORT_5000" ]; then
    print_status 0 "Port 5000 is in use (PID: $PORT_5000)"
    PROCESS_5000=$(ps -p $PORT_5000 -o comm= 2>/dev/null)
    echo "   Process: $PROCESS_5000"
else
    print_status 1 "Port 5000 is not in use"
fi

# Check React processes
echo -e "\n${BLUE}3. React Process Analysis${NC}"
echo "========================="

REACT_PROCESSES=$(ps aux | grep -E "react-scripts|npm.*start" | grep -v grep | wc -l)
print_info "Found $REACT_PROCESSES React-related processes"

if [ $REACT_PROCESSES -gt 0 ]; then
    print_status 0 "React development server processes are running"
    ps aux | grep -E "react-scripts|npm.*start" | grep -v grep | while read line; do
        echo "   $line"
    done
else
    print_status 1 "No React development server processes found"
fi

# Check Node processes
echo -e "\n${BLUE}4. Node.js Process Analysis${NC}"
echo "==========================="

NODE_PROCESSES=$(ps aux | grep -E "node.*server" | grep -v grep | wc -l)
print_info "Found $NODE_PROCESSES Node.js server processes"

if [ $NODE_PROCESSES -gt 0 ]; then
    print_status 0 "Node.js server processes are running"
    ps aux | grep -E "node.*server" | grep -v grep | while read line; do
        echo "   $line"
    done
else
    print_status 1 "No Node.js server processes found"
fi

# Test API endpoints
echo -e "\n${BLUE}5. API Endpoint Testing${NC}"
echo "======================="

# Test health endpoint
print_info "Testing /health endpoint..."
HEALTH_RESPONSE=$(curl -s http://localhost:5000/health 2>/dev/null)
if [ $? -eq 0 ] && [ -n "$HEALTH_RESPONSE" ]; then
    print_status 0 "Health endpoint responding"
    echo "   Status: $(echo $HEALTH_RESPONSE | jq -r '.status // "unknown"')"
    echo "   Environment: $(echo $HEALTH_RESPONSE | jq -r '.environment // "unknown"')"
else
    print_status 1 "Health endpoint not responding"
fi

# Test sample counts endpoint
print_info "Testing /api/sample-counts endpoint..."
COUNTS_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:5000/api/sample-counts 2>/dev/null)
if [ "$COUNTS_STATUS" = "200" ]; then
    print_status 0 "Sample counts endpoint responding"
else
    print_status 1 "Sample counts endpoint not responding (HTTP $COUNTS_STATUS)"
fi

# Summary
echo -e "\n${BLUE}6. Summary & Recommendations${NC}"
echo "============================"

if [ "$BACKEND_STATUS" = "200" ] && [ "$FRONTEND_STATUS" = "200" ]; then
    echo -e "${GREEN}🎉 SUCCESS: Both servers are running and accessible!${NC}"
    echo ""
    echo "✅ You can now access the ICU Dataset Application at:"
    echo "   🌐 http://localhost:3000"
    echo "   🌐 http://127.0.0.1:3000"
    echo "   🌐 http://*************:3000"
    echo ""
    echo "✅ Backend API is available at:"
    echo "   🔧 http://localhost:5000/health"
    echo "   📊 http://localhost:5000/api/sample-counts"
    echo ""
    echo "🚀 Ready to test automatic phrase progression functionality!"
else
    echo -e "${RED}❌ ISSUES DETECTED${NC}"
    echo ""
    if [ "$BACKEND_STATUS" != "200" ]; then
        echo "🔧 Backend server issue - check server logs"
    fi
    if [ "$FRONTEND_STATUS" != "200" ]; then
        echo "🌐 Frontend server issue - check React development server"
    fi
    echo ""
    echo "💡 Troubleshooting steps:"
    echo "   1. Check terminal outputs for error messages"
    echo "   2. Restart servers if needed: ./start-both.sh"
    echo "   3. Clear browser cache and try incognito mode"
    echo "   4. Check browser DevTools (F12) for JavaScript errors"
fi

echo ""
echo "📋 Next steps:"
echo "   1. Open browser and navigate to http://localhost:3000"
echo "   2. Verify the consent page loads properly"
echo "   3. Check browser console (F12) for any errors"
echo "   4. Test the automatic phrase progression functionality"

echo ""
echo "🏥 ICU Dataset Application verification complete!"
