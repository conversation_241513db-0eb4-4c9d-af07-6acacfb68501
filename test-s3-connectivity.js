// Test S3 connectivity and upload functionality
// Run this in the browser console on localhost:3001

console.log('🧪 Testing S3 Connectivity and Upload Pipeline');

async function testS3Connectivity() {
    try {
        console.log('🔍 Step 1: Testing backend health check...');
        
        const healthResponse = await fetch('http://localhost:5000/health');
        const healthData = await healthResponse.json();
        
        console.log('✅ Backend health check:', healthData);
        
        if (healthData.status !== 'healthy') {
            throw new Error('Backend is not healthy');
        }
        
        console.log('🔍 Step 2: Testing S3 sample counts API...');
        
        const countsResponse = await fetch('http://localhost:5000/api/sample-counts');
        const countsData = await countsResponse.json();
        
        console.log('✅ S3 sample counts:', countsData);
        
        if (!countsData.success) {
            throw new Error('S3 sample counts API failed');
        }
        
        console.log('🔍 Step 3: Testing upload endpoint with test data...');
        
        // Create a small test blob
        const testBlob = new Blob(['test video data'], { type: 'video/webm' });
        
        const formData = new FormData();
        formData.append('video', testBlob, 'test-connectivity.webm');
        formData.append('metadata', JSON.stringify({
            phrase: 'connectivity_test',
            category: 'test',
            recordingNumber: 1,
            demographics: {
                ageGroup: '18to39',
                gender: 'test',
                ethnicity: 'test'
            },
            timestamp: new Date().toISOString(),
            sessionId: 'test-session-' + Date.now()
        }));
        
        const uploadResponse = await fetch('http://localhost:5000/upload', {
            method: 'POST',
            body: formData
        });
        
        const uploadData = await uploadResponse.json();
        
        console.log('✅ Upload test result:', uploadData);
        
        if (!uploadData.success) {
            throw new Error('Upload test failed: ' + uploadData.error);
        }
        
        console.log('🎉 ALL TESTS PASSED - S3 connectivity is working!');
        console.log('📊 Summary:');
        console.log('  ✅ Backend server: Healthy');
        console.log('  ✅ S3 connection: Working');
        console.log('  ✅ Upload pipeline: Functional');
        console.log('  ✅ AWS credentials: Valid');
        
        return true;
        
    } catch (error) {
        console.error('❌ S3 Connectivity Test Failed:', error);
        console.log('🔧 Troubleshooting steps:');
        console.log('  1. Check backend server is running on localhost:5000');
        console.log('  2. Verify AWS credentials in .env file');
        console.log('  3. Check S3 bucket permissions');
        console.log('  4. Verify CORS configuration');
        
        return false;
    }
}

// Test CORS configuration
async function testCORS() {
    try {
        console.log('🔍 Testing CORS configuration...');
        
        const response = await fetch('http://localhost:5000/health', {
            method: 'GET',
            headers: {
                'Origin': 'http://localhost:3001'
            }
        });
        
        console.log('✅ CORS test passed - no CORS errors');
        return true;
        
    } catch (error) {
        console.error('❌ CORS test failed:', error);
        return false;
    }
}

// Check frontend AWS configuration
function checkFrontendConfig() {
    console.log('🔍 Checking frontend AWS configuration...');
    
    const config = {
        identityPoolId: process.env.REACT_APP_AWS_IDENTITY_POOL_ID,
        region: process.env.REACT_APP_AWS_REGION,
        bucket: process.env.REACT_APP_S3_BUCKET,
        backendUrl: process.env.REACT_APP_BACKEND_URL
    };
    
    console.log('📋 Frontend configuration:', config);
    
    const missing = Object.entries(config).filter(([key, value]) => !value);
    
    if (missing.length > 0) {
        console.warn('⚠️ Missing configuration:', missing.map(([key]) => key));
        return false;
    }
    
    console.log('✅ Frontend configuration complete');
    return true;
}

// Run all tests
async function runAllConnectivityTests() {
    console.log('🚀 Starting comprehensive connectivity tests...');
    
    const frontendConfigOk = checkFrontendConfig();
    const corsOk = await testCORS();
    const s3Ok = await testS3Connectivity();
    
    console.log('\n📊 CONNECTIVITY TEST RESULTS:');
    console.log(`  Frontend Config: ${frontendConfigOk ? '✅' : '❌'}`);
    console.log(`  CORS: ${corsOk ? '✅' : '❌'}`);
    console.log(`  S3 Connectivity: ${s3Ok ? '✅' : '❌'}`);
    
    const allPassed = frontendConfigOk && corsOk && s3Ok;
    
    if (allPassed) {
        console.log('\n🎉 ALL CONNECTIVITY TESTS PASSED!');
        console.log('✅ Ready for auto-advancement testing');
    } else {
        console.log('\n💥 SOME TESTS FAILED!');
        console.log('❌ Fix connectivity issues before testing auto-advancement');
    }
    
    return allPassed;
}

// Export functions to global scope
window.testS3Connectivity = testS3Connectivity;
window.testCORS = testCORS;
window.checkFrontendConfig = checkFrontendConfig;
window.runAllConnectivityTests = runAllConnectivityTests;

console.log('🧪 S3 Connectivity Test Functions Available:');
console.log('  - runAllConnectivityTests() - Run all tests');
console.log('  - testS3Connectivity() - Test S3 upload pipeline');
console.log('  - testCORS() - Test CORS configuration');
console.log('  - checkFrontendConfig() - Check frontend AWS config');
