#!/usr/bin/env node

/**
 * Video Storage Verification Tool
 * Helps locate and verify recorded videos from the ICU Dataset Application
 */

const { S3Client, ListObjectsV2Command, HeadObjectCommand } = require('@aws-sdk/client-s3');
const { fromCognitoIdentityPool } = require('@aws-sdk/credential-providers');
require('dotenv').config();

// AWS Configuration
const BUCKET_NAME = process.env.REACT_APP_S3_BUCKET || 'icudatasetphrasesfortesting';
const REGION = process.env.REACT_APP_AWS_REGION || 'ap-southeast-2';
const IDENTITY_POOL_ID = process.env.REACT_APP_AWS_IDENTITY_POOL_ID;

// Initialize S3 Client
let s3Client = null;
try {
  if (IDENTITY_POOL_ID && IDENTITY_POOL_ID !== 'your-identity-pool-id-here') {
    s3Client = new S3Client({
      region: REGION,
      credentials: fromCognitoIdentityPool({
        clientConfig: { region: REGION },
        identityPoolId: IDENTITY_POOL_ID,
      }),
    });
    console.log('✅ S3 Client initialized with Cognito Identity Pool');
  } else {
    console.log('⚠️ AWS not configured for direct S3 access');
  }
} catch (error) {
  console.error('❌ Failed to initialize S3 client:', error.message);
}

/**
 * List all videos in the ICU videos bucket
 */
async function listAllVideos() {
  if (!s3Client) {
    console.log('❌ S3 client not available. Videos may be stored via backend server.');
    return [];
  }

  try {
    console.log('🔍 Searching for videos in S3 bucket:', BUCKET_NAME);
    
    const command = new ListObjectsV2Command({
      Bucket: BUCKET_NAME,
      Prefix: 'icu-videos/',
      MaxKeys: 1000
    });

    const response = await s3Client.send(command);
    
    if (!response.Contents || response.Contents.length === 0) {
      console.log('📭 No videos found in S3 bucket');
      return [];
    }

    console.log(`📹 Found ${response.Contents.length} files in S3 bucket`);
    
    // Filter for video files
    const videoFiles = response.Contents.filter(obj => 
      obj.Key.endsWith('.mp4') || 
      obj.Key.endsWith('.webm') || 
      obj.Key.endsWith('.mov')
    );

    return videoFiles;
  } catch (error) {
    console.error('❌ Error listing S3 objects:', error.message);
    return [];
  }
}

/**
 * Analyze video file details
 */
async function analyzeVideo(videoKey) {
  if (!s3Client) return null;

  try {
    const command = new HeadObjectCommand({
      Bucket: BUCKET_NAME,
      Key: videoKey
    });

    const response = await s3Client.send(command);
    
    return {
      key: videoKey,
      size: response.ContentLength,
      lastModified: response.LastModified,
      contentType: response.ContentType,
      metadata: response.Metadata || {}
    };
  } catch (error) {
    console.error(`❌ Error analyzing video ${videoKey}:`, error.message);
    return null;
  }
}

/**
 * Parse S3 path to extract video information
 */
function parseVideoPath(s3Key) {
  // Expected format: icu-videos/[AGE_GROUP]/[GENDER]/[ETHNICITY]/[PHRASE_LABEL]/[FILENAME]
  if (!s3Key || typeof s3Key !== 'string') {
    return { path: s3Key, parsed: false };
  }

  const parts = s3Key.split('/');

  if (parts.length < 6) {
    return { path: s3Key, parsed: false };
  }

  const [prefix, ageGroup, gender, ethnicity, phraseLabel, filename] = parts;
  
  return {
    path: s3Key,
    parsed: true,
    ageGroup,
    gender,
    ethnicity,
    phraseLabel,
    filename,
    isLipNet: filename.includes('_lipnet'),
    isOriginal: !filename.includes('_lipnet')
  };
}

/**
 * Group videos by recording session
 */
function groupVideosBySession(videos) {
  const sessions = {};

  videos.forEach(video => {
    if (!video || !video.key) return;
    const parsed = parseVideoPath(video.key);
    if (!parsed.parsed) return;
    
    // Create session key based on demographics and phrase
    const sessionKey = `${parsed.ageGroup}/${parsed.gender}/${parsed.ethnicity}/${parsed.phraseLabel}`;
    
    if (!sessions[sessionKey]) {
      sessions[sessionKey] = {
        ageGroup: parsed.ageGroup,
        gender: parsed.gender,
        ethnicity: parsed.ethnicity,
        phraseLabel: parsed.phraseLabel,
        originalVideos: [],
        lipnetVideos: [],
        totalSize: 0
      };
    }
    
    if (parsed.isLipNet) {
      sessions[sessionKey].lipnetVideos.push(video);
    } else {
      sessions[sessionKey].originalVideos.push(video);
    }
    
    sessions[sessionKey].totalSize += video.Size || 0;
  });
  
  return sessions;
}

/**
 * Display video storage report
 */
function displayReport(videos, sessions) {
  console.log('\n' + '='.repeat(80));
  console.log('📊 ICU DATASET VIDEO STORAGE REPORT');
  console.log('='.repeat(80));

  console.log(`\n📹 Total Videos Found: ${videos.length}`);
  console.log(`📁 Recording Sessions: ${Object.keys(sessions).length}`);

  const originalCount = videos.filter(v => v && v.key && !parseVideoPath(v.key).isLipNet).length;
  const lipnetCount = videos.filter(v => v && v.key && parseVideoPath(v.key).isLipNet).length;
  
  console.log(`🎬 Original Videos: ${originalCount}`);
  console.log(`🎨 LipNet Processed Videos: ${lipnetCount}`);
  
  const totalSize = videos.reduce((sum, v) => sum + (v.size || 0), 0);
  console.log(`💾 Total Storage Used: ${(totalSize / 1024 / 1024).toFixed(2)} MB`);
  
  console.log('\n📂 STORAGE LOCATIONS:');
  console.log(`🪣 AWS S3 Bucket: ${BUCKET_NAME}`);
  console.log(`🌏 AWS Region: ${REGION}`);
  console.log(`📍 Base Path: icu-videos/`);
  
  console.log('\n🗂️ RECORDING SESSIONS:');
  Object.entries(sessions).forEach(([sessionKey, session], index) => {
    console.log(`\n${index + 1}. ${sessionKey}`);
    console.log(`   👤 Demographics: ${session.ageGroup}, ${session.gender}, ${session.ethnicity}`);
    console.log(`   💬 Phrase: ${session.phraseLabel.replace(/_/g, ' ')}`);
    console.log(`   🎬 Original Videos: ${session.originalVideos.length}`);
    console.log(`   🎨 LipNet Videos: ${session.lipnetVideos.length}`);
    console.log(`   💾 Session Size: ${(session.totalSize / 1024 / 1024).toFixed(2)} MB`);
    
    // Show dual video verification
    if (session.originalVideos.length > 0 && session.lipnetVideos.length > 0) {
      console.log(`   ✅ Dual Video Output: VERIFIED`);
    } else if (session.originalVideos.length > 0) {
      console.log(`   ⚠️  Original Only: LipNet processing may have failed`);
    } else if (session.lipnetVideos.length > 0) {
      console.log(`   ⚠️  LipNet Only: Original video missing`);
    }
  });
  
  console.log('\n🔍 FILE NAMING CONVENTION:');
  console.log('Original: [phrase]__[user]__[age]__[gender]__[ethnicity]__[timestamp].mp4');
  console.log('LipNet:   [phrase]_[user]_[timestamp]_lipnet.mp4');
  
  console.log('\n📏 LIPNET SPECIFICATIONS:');
  console.log('✅ Target Dimensions: 150×75 pixels (2:1 aspect ratio)');
  console.log('✅ Frame Rate: 25 fps');
  console.log('✅ Format: MP4/WebM');
  console.log('✅ Processing: Mouth ROI cropping + grayscale conversion');
}

/**
 * Main execution
 */
async function main() {
  console.log('🎬 ICU Dataset Video Storage Verification Tool');
  console.log('=' .repeat(50));
  
  // Check configuration
  console.log('\n🔧 Configuration Check:');
  console.log(`AWS Region: ${REGION}`);
  console.log(`S3 Bucket: ${BUCKET_NAME}`);
  console.log(`Identity Pool: ${IDENTITY_POOL_ID ? '✅ Configured' : '❌ Not configured'}`);
  console.log(`S3 Client: ${s3Client ? '✅ Ready' : '❌ Not available'}`);
  
  // List videos
  const videos = await listAllVideos();
  
  if (videos.length === 0) {
    console.log('\n📭 No videos found. Possible reasons:');
    console.log('1. Videos are being stored via backend server (check server/uploads/ directory)');
    console.log('2. AWS credentials not configured properly');
    console.log('3. No recordings have been made yet');
    console.log('4. Videos are in a different S3 bucket or path');
    return;
  }
  
  // Analyze videos
  console.log('\n🔍 Analyzing video details...');
  const videoDetails = [];
  for (const video of videos) {
    const details = await analyzeVideo(video.Key);
    if (details) {
      videoDetails.push(details);
    }
  }
  
  // Group by sessions
  const sessions = groupVideosBySession(videoDetails);
  
  // Display report
  displayReport(videoDetails, sessions);
  
  console.log('\n' + '='.repeat(80));
  console.log('✅ Video storage verification complete!');
  console.log('='.repeat(80));
}

// Run the tool
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { listAllVideos, analyzeVideo, parseVideoPath, groupVideosBySession };
