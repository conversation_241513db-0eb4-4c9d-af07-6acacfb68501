# Video Upload to S3 - Setup Guide & Debugging

## Overview

This guide provides comprehensive instructions for setting up and debugging the video upload functionality to ensure reliable S3 storage for the ICU Dataset Application.

## Current Implementation Status

✅ **Completed Enhancements:**
- Systematic upload pipeline debugging with comprehensive console logging
- Enhanced error handling with specific error categorization
- Dual upload path: Frontend direct S3 upload + Backend upload endpoint
- S3 path format verification: `icu-videos/[AGEGROUP]/[GENDER]/[ETHNICITY]/[PHRASE_LABEL]/[FILENAME].webm`
- Upload debugging utility with detailed validation
- Test panel for upload functionality verification

## Configuration Requirements

### 1. Environment Variables (.env file)

Your `.env` file needs both frontend and backend AWS configuration:

```env
# Frontend AWS Configuration (Cognito Identity Pool)
REACT_APP_AWS_IDENTITY_POOL_ID=ap-southeast-2:59baeaf9-a80c-4afc-8f27-f4d6191242dd
REACT_APP_AWS_REGION=ap-southeast-2
REACT_APP_S3_BUCKET=icudatasetphrasesfortesting

# Backend AWS Configuration (IAM User Credentials)
# IMPORTANT: Add your actual AWS credentials here for real S3 uploads
AWS_ACCESS_KEY_ID=your-access-key-id-here
AWS_SECRET_ACCESS_KEY=your-secret-access-key-here
AWS_REGION=ap-southeast-2
AWS_S3_BUCKET=icudatasetphrasesfortesting
```

### 2. AWS Setup Requirements

#### For Backend Upload (Recommended):
1. **Create IAM User** with programmatic access
2. **Attach S3 Policy** with these permissions:
   ```json
   {
     "Version": "2012-10-17",
     "Statement": [
       {
         "Effect": "Allow",
         "Action": [
           "s3:PutObject",
           "s3:PutObjectAcl",
           "s3:GetObject",
           "s3:ListBucket"
         ],
         "Resource": [
           "arn:aws:s3:::icudatasetphrasesfortesting",
           "arn:aws:s3:::icudatasetphrasesfortesting/*"
         ]
       }
     ]
   }
   ```
3. **Add credentials** to `.env` file

#### For Frontend Upload (Alternative):
1. **Configure Cognito Identity Pool** with unauthenticated access
2. **Attach S3 permissions** to the unauthenticated role
3. **Update Identity Pool ID** in `.env` file

## Upload Pipeline Flow

### Current Implementation:
1. **VideoRecorder** captures video and creates blob
2. **Upload Debugger** validates video blob and demographics
3. **Frontend Check**: If AWS configured → Direct S3 upload, else → Backend upload
4. **Backend Upload**: Receives FormData, validates, uploads to S3
5. **S3 Storage**: Files stored with path format `icu-videos/[AGEGROUP]/[GENDER]/[ETHNICITY]/[PHRASE_LABEL]/[FILENAME].webm`
6. **Metadata Update**: CSV file updated with recording information

### Debug Logging Points:
- ✅ Video blob validation (size, type, content)
- ✅ Demographics validation (required fields)
- ✅ AWS configuration check
- ✅ FormData preparation
- ✅ Backend request/response
- ✅ S3 upload parameters
- ✅ S3 upload response
- ✅ Error categorization and handling

## Testing the Upload Functionality

### 1. Using the Upload Test Panel
```javascript
// Add to your component
import UploadTestPanel from './components/UploadTestPanel';

// Include in your JSX
<UploadTestPanel />
```

### 2. Manual Testing Steps
1. **Start the backend server**: `npm run server`
2. **Start the frontend**: `npm start`
3. **Open browser console** to see debug logs
4. **Record a video** using VideoRecorder component
5. **Check console logs** for upload process details

### 3. Debug Log Analysis
Look for these key log messages:
- `🚀 Starting upload process`
- `✅ Input validation passed`
- `📤 Preparing FormData for backend upload`
- `✅ Backend upload successful`
- `🎉 S3 upload process completed successfully`

## Common Issues & Solutions

### Issue 1: "AWS credentials not configured"
**Symptoms:** Upload falls back to mock/simulation mode
**Solution:** Add `AWS_ACCESS_KEY_ID` and `AWS_SECRET_ACCESS_KEY` to `.env`

### Issue 2: "Access denied to S3 bucket"
**Symptoms:** Upload fails with 403 error
**Solution:** Check IAM user permissions and bucket policy

### Issue 3: "S3 bucket not found"
**Symptoms:** Upload fails with 404 error
**Solution:** Verify bucket name and region in `.env` file

### Issue 4: "Backend upload failed"
**Symptoms:** Frontend can't reach backend
**Solution:** Ensure backend server is running on correct port

### Issue 5: "Video blob validation failed"
**Symptoms:** Upload rejected before sending
**Solution:** Check video recording process and blob creation

## Monitoring & Debugging Tools

### 1. Upload Debugger
```javascript
import { uploadDebugger, debugUploadProcess } from '../utils/uploadDebugger';

// Debug complete upload process
const report = await debugUploadProcess(videoBlob, phrase, demographics);
console.log('Debug Report:', report);

// Export debug logs
uploadDebugger.exportLogs();
```

### 2. Console Logging
All upload steps now include detailed console logging:
- Input validation
- Configuration checks
- Request/response details
- Error categorization
- Success confirmations

### 3. Error Categorization
Errors are now categorized for easier debugging:
- `AWS_CREDENTIALS_ERROR`: Missing or invalid credentials
- `AWS_ACCESS_DENIED`: Permission issues
- `AWS_BUCKET_NOT_FOUND`: Bucket doesn't exist
- `AWS_NETWORK_ERROR`: Network connectivity issues
- `VALIDATION_ERROR`: Input validation failures

## S3 File Structure

Videos are stored with this path structure:
```
icu-videos/
├── 18to39/
│   ├── female/
│   │   ├── caucasian/
│   │   │   ├── hello_world/
│   │   │   │   └── hello_world__user01__18to39__female__caucasian__20241223T143022.webm
```

## Recommendations for Production

### 1. Security
- Use IAM roles instead of access keys when possible
- Implement bucket policies with least privilege
- Enable S3 bucket encryption
- Use HTTPS for all uploads

### 2. Performance
- Consider S3 Transfer Acceleration for global users
- Implement multipart uploads for large files
- Add retry logic for failed uploads
- Monitor upload success rates

### 3. Monitoring
- Set up CloudWatch alarms for upload failures
- Log upload metrics to monitoring service
- Implement health checks for upload endpoints
- Track upload success/failure rates by demographic

### 4. Error Handling
- Implement exponential backoff for retries
- Provide user-friendly error messages
- Log detailed error information for debugging
- Implement fallback upload methods

## Next Steps

1. **Configure AWS credentials** in `.env` file
2. **Test upload functionality** using the test panel
3. **Monitor console logs** during testing
4. **Verify S3 bucket contents** after successful uploads
5. **Set up production monitoring** and alerting

For additional support, check the console logs and use the upload debugger utility to identify specific issues.
