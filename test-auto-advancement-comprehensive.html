<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Auto-Advancement Test - ICU Dataset Application</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #f9f9f9;
        }
        .test-section h2 {
            color: #34495e;
            margin-top: 0;
        }
        .step {
            margin: 15px 0;
            padding: 15px;
            background: white;
            border-left: 4px solid #3498db;
            border-radius: 4px;
        }
        .step h3 {
            margin: 0 0 10px 0;
            color: #2980b9;
        }
        .code-block {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            overflow-x: auto;
        }
        .success {
            border-left-color: #27ae60;
            background-color: #d5f4e6;
        }
        .warning {
            border-left-color: #f39c12;
            background-color: #fef9e7;
        }
        .error {
            border-left-color: #e74c3c;
            background-color: #fadbd8;
        }
        .button {
            background: #3498db;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .button:hover {
            background: #2980b9;
        }
        .button.success {
            background: #27ae60;
        }
        .button.warning {
            background: #f39c12;
        }
        .button.danger {
            background: #e74c3c;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .status.success {
            background: #d5f4e6;
            color: #27ae60;
            border: 1px solid #27ae60;
        }
        .status.error {
            background: #fadbd8;
            color: #e74c3c;
            border: 1px solid #e74c3c;
        }
        .status.info {
            background: #d6eaf8;
            color: #2980b9;
            border: 1px solid #2980b9;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Auto-Advancement Test Guide</h1>
        <p><strong>Purpose:</strong> Test the automatic phrase progression functionality after completing 3 recordings per phrase.</p>
        
        <div class="test-section">
            <h2>🔧 Pre-Test Setup</h2>
            
            <div class="step">
                <h3>1. Navigate to Application</h3>
                <p>Open the ICU Dataset Application in your browser:</p>
                <div class="code-block">http://localhost:3001</div>
                <button class="button" onclick="window.open('http://localhost:3001', '_blank')">Open Application</button>
            </div>
            
            <div class="step">
                <h3>2. Complete Initial Setup</h3>
                <p>Follow these steps in the application:</p>
                <ul>
                    <li>✅ Accept consent</li>
                    <li>✅ Fill out demographics form</li>
                    <li>✅ Watch training video</li>
                    <li>✅ Select <strong>multiple phrases</strong> (at least 2-3 phrases)</li>
                    <li>✅ Navigate to recording page</li>
                </ul>
            </div>
        </div>

        <div class="test-section">
            <h2>🧪 Automated Test</h2>
            
            <div class="step">
                <h3>3. Run Automated Test</h3>
                <p>Open browser console (F12) and run this test:</p>
                <div class="code-block">
// Test auto-advancement functionality
async function testAutoAdvancement() {
    console.log('🧪 Starting Auto-Advancement Test');
    
    // Check initial state
    console.log('🔍 Initial state check:');
    window.debugCurrentState();
    
    const initialPhraseIndex = window.currentPhraseIndex || 0;
    const initialPhrase = window.selectedPhrases?.[initialPhraseIndex]?.phrase;
    
    console.log(`📝 Starting phrase: "${initialPhrase}" (index: ${initialPhraseIndex})`);
    
    // Force recording count to 3 for current phrase
    console.log('🔧 Forcing recording count to 3...');
    window.debugForceRecordingCount();
    
    // Wait for auto-advancement
    console.log('⏳ Waiting for auto-advancement...');
    
    return new Promise((resolve) => {
        let attempts = 0;
        const maxAttempts = 30; // 3 seconds
        
        const checkAdvancement = () => {
            attempts++;
            const currentIndex = window.currentPhraseIndex;
            const currentPhrase = window.selectedPhrases?.[currentIndex]?.phrase;
            
            console.log(`🔍 Check ${attempts}: Index ${currentIndex}, Phrase: "${currentPhrase}"`);
            
            if (currentIndex > initialPhraseIndex) {
                console.log('✅ SUCCESS: Auto-advancement worked!');
                console.log(`📝 Advanced from "${initialPhrase}" to "${currentPhrase}"`);
                window.debugCurrentState();
                resolve(true);
            } else if (attempts >= maxAttempts) {
                console.log('❌ FAILED: Auto-advancement did not occur within timeout');
                window.debugCurrentState();
                resolve(false);
            } else {
                setTimeout(checkAdvancement, 100);
            }
        };
        
        setTimeout(checkAdvancement, 100);
    });
}

// Run the test
testAutoAdvancement().then(success => {
    if (success) {
        console.log('🎉 AUTO-ADVANCEMENT TEST PASSED!');
    } else {
        console.log('💥 AUTO-ADVANCEMENT TEST FAILED!');
    }
});
                </div>
                <button class="button success" onclick="runAutomatedTest()">Run Automated Test</button>
                <div id="test-result"></div>
            </div>
        </div>

        <div class="test-section">
            <h2>🎯 Manual Test</h2>
            
            <div class="step">
                <h3>4. Manual Recording Test</h3>
                <p>Test auto-advancement by actually recording videos:</p>
                <ol>
                    <li>Record first video for current phrase</li>
                    <li>Record second video for current phrase</li>
                    <li>Record third video for current phrase</li>
                    <li><strong>Watch for automatic advancement to next phrase</strong></li>
                </ol>
                <div class="status info">
                    <strong>Expected Behavior:</strong> After the 3rd recording completes uploading, the application should automatically advance to the next phrase without manual intervention.
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>🔍 Debug Tools</h2>
            
            <div class="step">
                <h3>5. Available Debug Functions</h3>
                <p>Use these functions in the browser console for debugging:</p>
                
                <div class="code-block">
// Check current application state
window.debugCurrentState();

// Force recording count to 3 for testing
window.debugForceRecordingCount();

// Test auto-advancement manually
window.debugAutoAdvancement();
                </div>
                
                <button class="button" onclick="runDebugState()">Check Current State</button>
                <button class="button warning" onclick="runForceCount()">Force Count to 3</button>
                <button class="button danger" onclick="runManualAdvancement()">Manual Advancement</button>
            </div>
        </div>

        <div class="test-section">
            <h2>✅ Success Criteria</h2>
            
            <div class="step success">
                <h3>Test Passes If:</h3>
                <ul>
                    <li>✅ After 3rd recording completes, phrase automatically advances</li>
                    <li>✅ Console shows "AUTO-ADVANCEMENT EFFECT TRIGGERED" messages</li>
                    <li>✅ Console shows "EFFECT: Executing handleNextPhrase" message</li>
                    <li>✅ UI updates to show next phrase without manual navigation</li>
                    <li>✅ Recording count resets for new phrase</li>
                </ul>
            </div>
            
            <div class="step error">
                <h3>Test Fails If:</h3>
                <ul>
                    <li>❌ No automatic advancement after 3rd recording</li>
                    <li>❌ Console errors about stale closures or missing dependencies</li>
                    <li>❌ Manual navigation required to advance phrases</li>
                    <li>❌ Auto-advancement effect not triggering</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        function runAutomatedTest() {
            const resultDiv = document.getElementById('test-result');
            resultDiv.innerHTML = '<div class="status info">Running automated test... Check browser console for details.</div>';
            
            // This would need to be run in the application context
            alert('Please copy and paste the test code into the browser console on the application page (localhost:3001)');
        }
        
        function runDebugState() {
            alert('Run this in the application console: window.debugCurrentState()');
        }
        
        function runForceCount() {
            alert('Run this in the application console: window.debugForceRecordingCount()');
        }
        
        function runManualAdvancement() {
            alert('Run this in the application console: window.debugAutoAdvancement()');
        }
    </script>
</body>
</html>
