# ICU Dataset Application - EC2 Deployment Configuration

## 🚀 EC2 Server Setup Guide

### Prerequisites
- EC2 instance running Ubuntu 20.04 or later
- Node.js 18+ installed
- PM2 process manager installed
- Security group allowing inbound traffic on port 5000
- AWS credentials configured on the EC2 instance

### Step 1: EC2 Instance Configuration

#### Security Group Settings
```
Inbound Rules:
- Type: HTTP, Port: 80, Source: 0.0.0.0/0
- Type: HTTPS, Port: 443, Source: 0.0.0.0/0
- Type: Custom TCP, Port: 5000, Source: 0.0.0.0/0
- Type: SSH, Port: 22, Source: Your IP
```

#### Install Dependencies
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Node.js 18
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install PM2 globally
sudo npm install -g pm2

# Install Git
sudo apt install git -y
```

### Step 2: Application Deployment

#### Clone Repository
```bash
cd /home/<USER>
git clone <your-repository-url> icu-dataset-app
cd icu-dataset-app
```

#### Install Dependencies
```bash
# Install backend dependencies
cd server
npm install

# Install frontend dependencies (if building on server)
cd ..
npm install
```

### Step 3: Environment Configuration

#### Create Production Environment File
```bash
# Create .env.production file
sudo nano .env.production
```

#### Environment Variables for EC2
```env
# AWS Configuration for ICU Dataset Application - EC2 Production
NODE_ENV=production

# Frontend AWS Configuration (Cognito Identity Pool)
REACT_APP_AWS_IDENTITY_POOL_ID=ap-southeast-2:59baeaf9-a80c-4afc-8f27-f4d6191242dd
REACT_APP_AWS_REGION=ap-southeast-2
REACT_APP_S3_BUCKET=icudatasetphrasesfortesting

# Backend AWS Configuration (IAM User Credentials)
AWS_ACCESS_KEY_ID=********************
AWS_SECRET_ACCESS_KEY=WOFqaWc9pLOHmhubPAtMpFGAKdYffw9CJrkk8+Ik
AWS_REGION=ap-southeast-2
AWS_S3_BUCKET=icudatasetphrasesfortesting

# Server Configuration
PORT=5000

# Backend URL for API requests (EC2 public IP or domain)
REACT_APP_BACKEND_URL=http://YOUR_EC2_PUBLIC_IP:5000

# CORS allowed origins for backend server
ALLOWED_ORIGINS=http://localhost:3000,https://icuphrasecollection.com,https://*.netlify.app

# Application Configuration
REACT_APP_NAME=ICU Dataset Application
REACT_APP_VERSION=1.0.0
REACT_APP_DEBUG=false
```

### Step 4: PM2 Configuration for EC2

#### Create EC2-specific PM2 config
```javascript
// ecosystem.ec2.config.js
module.exports = {
  apps: [
    {
      name: 'icu-backend-production',
      script: 'server/server.js',
      cwd: '/home/<USER>/icu-dataset-app',
      env: {
        NODE_ENV: 'production',
        PORT: 5000
      },
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: '1G',
      error_file: '/home/<USER>/logs/backend-error.log',
      out_file: '/home/<USER>/logs/backend-out.log',
      log_file: '/home/<USER>/logs/backend-combined.log',
      time: true,
      restart_delay: 1000,
      max_restarts: 10,
      min_uptime: '10s'
    }
  ]
};
```

### Step 5: Startup Script

#### Create startup script
```bash
#!/bin/bash
# startup.sh - ICU Dataset Application EC2 Startup Script

echo "🚀 Starting ICU Dataset Application on EC2..."

# Set working directory
cd /home/<USER>/icu-dataset-app

# Load environment variables
export $(cat .env.production | xargs)

# Create logs directory
mkdir -p /home/<USER>/logs

# Start the application with PM2
pm2 start ecosystem.ec2.config.js

# Save PM2 configuration
pm2 save

# Setup PM2 to start on boot
pm2 startup

echo "✅ ICU Dataset Application started successfully!"
echo "📊 Backend running on port 5000"
echo "🔍 Check status with: pm2 status"
echo "📋 View logs with: pm2 logs icu-backend-production"
```

### Step 6: Deployment Commands

#### Initial Deployment
```bash
# Make startup script executable
chmod +x startup.sh

# Run startup script
./startup.sh
```

#### Update Deployment
```bash
# Pull latest changes
git pull origin main

# Restart application
pm2 restart icu-backend-production

# Check status
pm2 status
```

### Step 7: Health Check

#### Test Backend Health
```bash
# Test health endpoint
curl http://localhost:5000/health

# Test S3 connectivity
curl http://localhost:5000/api/test-s3
```

### Step 8: Monitoring

#### PM2 Monitoring Commands
```bash
# View application status
pm2 status

# View logs
pm2 logs icu-backend-production

# Monitor in real-time
pm2 monit

# Restart application
pm2 restart icu-backend-production

# Stop application
pm2 stop icu-backend-production
```

### Step 9: Firewall Configuration

#### Configure UFW (if enabled)
```bash
# Allow SSH
sudo ufw allow ssh

# Allow HTTP/HTTPS
sudo ufw allow 80
sudo ufw allow 443

# Allow backend port
sudo ufw allow 5000

# Enable firewall
sudo ufw enable
```

### Step 10: SSL Configuration (Optional)

#### Install Certbot for HTTPS
```bash
# Install Certbot
sudo apt install certbot

# Get SSL certificate (if using domain)
sudo certbot certonly --standalone -d your-domain.com
```

## 🔧 Troubleshooting

### Common Issues

1. **Port 5000 already in use**
   ```bash
   sudo lsof -ti:5000
   sudo kill -9 <PID>
   ```

2. **PM2 not starting on boot**
   ```bash
   pm2 startup
   pm2 save
   ```

3. **AWS credentials not working**
   ```bash
   aws configure list
   aws s3 ls s3://icudatasetphrasesfortesting
   ```

4. **CORS issues**
   - Update ALLOWED_ORIGINS in .env.production
   - Restart PM2 application

### Log Locations
- Backend logs: `/home/<USER>/logs/`
- PM2 logs: `~/.pm2/logs/`
- System logs: `/var/log/`

## ✅ Deployment Checklist

- [ ] EC2 instance configured with proper security groups
- [ ] Node.js and PM2 installed
- [ ] Repository cloned and dependencies installed
- [ ] Environment variables configured
- [ ] PM2 configuration created
- [ ] Startup script created and tested
- [ ] Application started with PM2
- [ ] Health checks passing
- [ ] Logs monitoring setup
- [ ] Firewall configured
- [ ] SSL certificate installed (if using domain)

## 🌐 Frontend Configuration

Update your Netlify environment variables:
```
REACT_APP_BACKEND_URL=http://YOUR_EC2_PUBLIC_IP:5000
```

Or if using a domain:
```
REACT_APP_BACKEND_URL=https://your-backend-domain.com
```
