# 🚀 ICU Dataset Application - Netlify Deployment Guide

## ✅ Pre-Deployment Checklist Complete

- ✅ **Environment Variables Added** to Netlify
- ✅ **Production Build Created** (18MB, optimized)
- ✅ **Large Files Removed** (training videos excluded)
- ✅ **Backend Server Running** (localhost:5000)
- ✅ **AWS S3 Connectivity Verified**
- ✅ **Upload Pipeline Fixed** (supports both modes)

## 🔧 Next Steps

### 1. Update S3 CORS Policy

**Go to AWS S3 Console:**
1. Navigate to your bucket: `icudatasetphrasesfortesting`
2. Go to "Permissions" tab
3. Scroll to "Cross-origin resource sharing (CORS)"
4. Replace existing policy with the content from `s3-cors-policy.json`
5. **Important:** Replace `your-netlify-app.netlify.app` with your actual Netlify URL

### 2. Deploy to Netlify

**Option A: Drag and Drop (Recommended)**
1. Go to your Netlify dashboard
2. Drag the entire `/build` folder to the deploy area
3. Wait for deployment to complete

**Option B: Git Integration**
1. Commit and push your changes to GitHub
2. Netlify will auto-deploy from your repository

### 3. Post-Deployment Configuration

**After deployment, you'll need to:**
1. Get your Netlify URL (e.g., `https://amazing-app-123.netlify.app`)
2. Update the S3 CORS policy to include your actual Netlify domain
3. Test the video recording functionality

## 🧪 Testing Your Deployment

### Local Production Test (Currently Running)
- **URL:** http://localhost:8080
- **Status:** ✅ Running
- **Test:** Try recording a video to verify the upload pipeline

### Post-Netlify Deployment Tests
1. **Open your Netlify URL**
2. **Complete the consent process**
3. **Fill out demographics**
4. **Record a test video**
5. **Check browser DevTools for any errors**
6. **Verify video appears in S3 bucket**

## 🔍 Troubleshooting Common Issues

### Issue: "Network connection error"
**Solution:**
- Check browser DevTools Console for specific errors
- Verify environment variables are set in Netlify
- Ensure S3 CORS policy includes your Netlify domain

### Issue: "CORS error"
**Solution:**
- Update S3 CORS policy with your actual Netlify URL
- Wait 5-10 minutes for CORS changes to propagate
- Clear browser cache and try again

### Issue: "AWS credentials not configured"
**Solution:**
- Verify all environment variables are set in Netlify:
  - `REACT_APP_AWS_IDENTITY_POOL_ID`
  - `REACT_APP_AWS_REGION`
  - `REACT_APP_S3_BUCKET`

## 📊 Environment Variables Verification

**Required in Netlify Dashboard:**
```
REACT_APP_AWS_IDENTITY_POOL_ID=ap-southeast-2:59baeaf9-a80c-4afc-8f27-f4d6191242dd
REACT_APP_AWS_REGION=ap-southeast-2
REACT_APP_S3_BUCKET=icudatasetphrasesfortesting
REACT_APP_NAME=ICU Dataset Application
REACT_APP_VERSION=1.0.0
NODE_ENV=production
REACT_APP_DEBUG=false
```

## 🎯 Success Criteria

Your deployment is successful when:
- ✅ Application loads without errors
- ✅ Video recording works
- ✅ Videos upload to S3 bucket
- ✅ No network connection errors
- ✅ Progress tracking works
- ✅ All user workflows complete successfully

## 📞 Next Steps After Deployment

1. **Share your Netlify URL** for testing
2. **Monitor the first few video uploads** in S3
3. **Check application logs** in Netlify dashboard
4. **Verify all functionality** works as expected

---

**Current Status:** Ready for Netlify deployment
**Build Size:** 18MB (optimized)
**Test Server:** http://localhost:8080 (running)
**Backend Server:** http://localhost:5000 (running)
