<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Comprehensive VideoRecorder Testing Guide</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-header {
            background: #2196F3;
            color: white;
            padding: 15px;
            border-radius: 8px 8px 0 0;
            margin: -20px -20px 20px -20px;
        }
        .test-step {
            background: #f8f9fa;
            padding: 15px;
            margin: 10px 0;
            border-left: 4px solid #007bff;
            border-radius: 4px;
        }
        .test-substep {
            background: #e9ecef;
            padding: 10px;
            margin: 8px 0 8px 20px;
            border-left: 3px solid #6c757d;
            border-radius: 3px;
        }
        .checklist {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .error-check {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .console-output {
            background: #1e1e1e;
            color: #00ff00;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            margin: 10px 0;
        }
        .button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            text-decoration: none;
            display: inline-block;
        }
        .button:hover {
            background: #0056b3;
        }
        .button.success {
            background: #28a745;
        }
        .button.warning {
            background: #ffc107;
            color: #212529;
        }
        .button.danger {
            background: #dc3545;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-running { background: #ffc107; }
        .status-success { background: #28a745; }
        .status-error { background: #dc3545; }
        .status-pending { background: #6c757d; }
        .two-column {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        @media (max-width: 768px) {
            .two-column {
                grid-template-columns: 1fr;
            }
        }
        .iframe-container {
            border: 2px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
            margin: 20px 0;
            height: 600px;
        }
        iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
    </style>
</head>
<body>
    <h1>🧪 Comprehensive VideoRecorder Testing Guide</h1>
    <p><strong>Objective:</strong> Test the VideoRecorder component end-to-end and verify AWS S3 integration after recent fixes.</p>

    <div class="test-container">
        <div class="test-header">
            <h2>🚀 Pre-Test Server Status</h2>
        </div>
        
        <div class="two-column">
            <div>
                <div class="test-step">
                    <h3><span id="frontend-status" class="status-indicator status-success"></span>Frontend Server</h3>
                    <p><strong>URL:</strong> <a href="http://localhost:3003" target="_blank">http://localhost:3003</a></p>
                    <p><strong>Status:</strong> <span id="frontend-result">✅ Running</span></p>
                </div>
            </div>
            <div>
                <div class="test-step">
                    <h3><span id="backend-status" class="status-indicator status-success"></span>Backend Server</h3>
                    <p><strong>URL:</strong> <a href="http://localhost:5001/health" target="_blank">http://localhost:5001/health</a></p>
                    <p><strong>Status:</strong> <span id="backend-result">✅ Running on port 5001</span></p>
                </div>
            </div>
        </div>

        <div class="warning">
            <strong>⚠️ Important:</strong> Backend is running on port 5001 instead of 5000. You may need to update the REACT_APP_BACKEND_URL environment variable if uploads fail.
        </div>

        <button class="button" onclick="checkServers()">🔄 Recheck Server Status</button>
        <a href="http://localhost:3003" target="_blank" class="button success">🔗 Open Application</a>
    </div>

    <div class="test-container">
        <div class="test-header">
            <h2>📋 Test Phase 1: Initial Setup & Navigation</h2>
        </div>
        
        <div class="test-step">
            <h3>Step 1.1: Open Application & Check Console</h3>
            <div class="test-substep">
                <p>1. Open Developer Tools (F12) and go to Console tab</p>
                <p>2. Navigate to <a href="http://localhost:3003" target="_blank">http://localhost:3003</a></p>
                <p>3. Monitor console for any immediate errors</p>
            </div>
            <div class="checklist">
                <h4>✅ Expected Results:</h4>
                <ul>
                    <li>Application loads without errors</li>
                    <li>No infinite loop messages in console</li>
                    <li>TensorFlow.js and MediaPipe models load successfully</li>
                </ul>
            </div>
        </div>

        <div class="test-step">
            <h3>Step 1.2: Fill Demographics Form</h3>
            <div class="test-substep">
                <p>1. Fill out all required demographic fields</p>
                <p>2. Use any test values (e.g., Age: 25-39, Gender: Female, etc.)</p>
                <p>3. Click "Continue to Phrase Selection"</p>
            </div>
            <div class="checklist">
                <h4>✅ Expected Results:</h4>
                <ul>
                    <li>Form validation works correctly</li>
                    <li>Navigation to phrase selection page succeeds</li>
                    <li>No console errors during navigation</li>
                </ul>
            </div>
        </div>

        <div class="test-step">
            <h3>Step 1.3: Select Phrases</h3>
            <div class="test-substep">
                <p>1. Select at least 2-3 phrases from any category</p>
                <p>2. Click "Continue to Recording"</p>
                <p>3. Verify navigation to recording page</p>
            </div>
            <div class="checklist">
                <h4>✅ Expected Results:</h4>
                <ul>
                    <li>Phrase selection interface works</li>
                    <li>Selected phrases are stored in localStorage</li>
                    <li>Recording page loads with first phrase</li>
                </ul>
            </div>
        </div>
    </div>

    <div class="test-container">
        <div class="test-header">
            <h2>🎥 Test Phase 2: Camera & Recording Setup</h2>
        </div>
        
        <div class="test-step">
            <h3>Step 2.1: Camera Permissions</h3>
            <div class="test-substep">
                <p>1. Grant camera permissions when prompted</p>
                <p>2. Verify camera feed appears in oval viewport</p>
                <p>3. Check that face detection initializes</p>
            </div>
            <div class="checklist">
                <h4>✅ Expected Results:</h4>
                <ul>
                    <li>Camera permission granted successfully</li>
                    <li>Live video feed visible in oval viewport</li>
                    <li>No "camera not available" errors</li>
                </ul>
            </div>
        </div>

        <div class="test-step">
            <h3>Step 2.2: Monitor Console for Fixed Issues</h3>
            <div class="test-substep">
                <p>1. Watch console output for 30 seconds</p>
                <p>2. Look for any repetitive or infinite loop messages</p>
                <p>3. Check for LipNet processing messages</p>
            </div>
            <div class="error-check">
                <h4>❌ Should NOT See These Messages:</h4>
                <div class="console-output">
🎯 Fallback crop area: {x: 240, y: 240, width: 160, height: 80}
🎯 Canvas verification: {canvasSize: '150x75', expectedSize: '150x75', matches: true}
🎯 Low landmark confidence warning: 0.00
🎯 Canvas content check: HAS CONTENT with center pixel RGB: 243, 165, 150
                </div>
            </div>
            <div class="checklist">
                <h4>✅ Should See These Messages:</h4>
                <div class="console-output">
🚀 Using standard webcam recording for reliability
📊 LipNet processing disabled to prevent infinite loops
🎯 Face detection loop running, video size: 640 x 480
                </div>
            </div>
        </div>
    </div>

    <div class="test-container">
        <div class="test-header">
            <h2>🎬 Test Phase 3: Recording Functionality</h2>
        </div>
        
        <div class="test-step">
            <h3>Step 3.1: Start Recording</h3>
            <div class="test-substep">
                <p>1. Click "Start Recording" button</p>
                <p>2. Observe the 5-second countdown timer</p>
                <p>3. Monitor console for recording messages</p>
            </div>
            <div class="checklist">
                <h4>✅ Expected Console Output:</h4>
                <div class="console-output">
🎬 Starting webcam recording...
🎬 Webcam stream details: {streamActive: true, videoTracks: 1, audioTracks: 0}
🎬 MediaRecorder created with VP9 codec
🎬 Webcam recording started successfully
                </div>
            </div>
        </div>

        <div class="test-step">
            <h3>Step 3.2: Recording Process</h3>
            <div class="test-substep">
                <p>1. Verify countdown timer counts down from 5 to 0</p>
                <p>2. Speak the displayed phrase clearly</p>
                <p>3. Wait for automatic recording stop</p>
            </div>
            <div class="checklist">
                <h4>✅ Expected Behavior:</h4>
                <ul>
                    <li>Countdown timer displays and decrements</li>
                    <li>Recording stops automatically after 5 seconds</li>
                    <li>No "No video data captured" errors</li>
                </ul>
            </div>
        </div>

        <div class="test-step">
            <h3>Step 3.3: Recording Completion</h3>
            <div class="test-substep">
                <p>1. Monitor console for completion messages</p>
                <p>2. Check for video blob validation</p>
                <p>3. Verify processing state changes</p>
            </div>
            <div class="checklist">
                <h4>✅ Expected Console Output:</h4>
                <div class="console-output">
🎬 Recording chunk received: {size: 12345, type: "video/webm"}
🎬 MediaRecorder onstop event triggered
  videoBlob created: {size: 67890, type: "video/webm"}
🔄 Processing phase: Video processing and upload preparation
                </div>
            </div>
        </div>
    </div>

    <script>
        async function checkServers() {
            // Check frontend server
            try {
                const frontendResponse = await fetch('http://localhost:3003');
                if (frontendResponse.ok) {
                    document.getElementById('frontend-status').className = 'status-indicator status-success';
                    document.getElementById('frontend-result').textContent = '✅ Running';
                } else {
                    throw new Error('Server responded with error');
                }
            } catch (error) {
                document.getElementById('frontend-status').className = 'status-indicator status-error';
                document.getElementById('frontend-result').textContent = '❌ Not accessible';
            }

            // Check backend server
            try {
                const backendResponse = await fetch('http://localhost:5001/health');
                if (backendResponse.ok) {
                    document.getElementById('backend-status').className = 'status-indicator status-success';
                    document.getElementById('backend-result').textContent = '✅ Running on port 5001';
                } else {
                    throw new Error('Server responded with error');
                }
            } catch (error) {
                document.getElementById('backend-status').className = 'status-indicator status-error';
                document.getElementById('backend-result').textContent = '❌ Not accessible';
            }
        }

        // Check servers on page load
        window.addEventListener('load', checkServers);
    </script>

    <div class="test-container">
        <div class="test-header">
            <h2>☁️ Test Phase 4: AWS S3 Upload Integration</h2>
        </div>

        <div class="test-step">
            <h3>Step 4.1: Upload Process</h3>
            <div class="test-substep">
                <p>1. After recording completes, monitor upload process</p>
                <p>2. Check for AWS S3 upload messages in console</p>
                <p>3. Verify upload progress indicators</p>
            </div>
            <div class="checklist">
                <h4>✅ Expected Console Output:</h4>
                <div class="console-output">
📤 Uploading single video to AWS S3...
✅ Single recording saved successfully: {url: "https://...", key: "icu-videos/..."}
✅ Upload completed successfully - resetting processing state
🎉 Recording saved and processed without errors!
                </div>
            </div>
        </div>

        <div class="test-step">
            <h3>Step 4.2: Validation Checks</h3>
            <div class="test-substep">
                <p>1. Ensure no validation errors occur</p>
                <p>2. Check that video blob size is > 0</p>
                <p>3. Verify metadata creation succeeds</p>
            </div>
            <div class="error-check">
                <h4>❌ Should NOT See These Errors:</h4>
                <div class="console-output">
⚠️ Recording validation failed. Please try again.
❌ Video blob is empty! This will cause validation failure.
VALIDATION_ERROR: Invalid video blob provided
Recording failed: No video data captured. Please try again.
                </div>
            </div>
        </div>

        <div class="test-step">
            <h3>Step 4.3: Backend Connectivity</h3>
            <div class="test-substep">
                <p>1. Verify backend server communication</p>
                <p>2. Check for network errors</p>
                <p>3. Test health endpoint manually</p>
            </div>
            <div class="warning">
                <strong>⚠️ Backend Port Issue:</strong> If uploads fail, the backend is running on port 5001 instead of 5000.
                You may need to update the environment variable or restart the backend on port 5000.
            </div>
            <a href="http://localhost:5001/health" target="_blank" class="button">🔗 Test Backend Health</a>
        </div>
    </div>

    <div class="test-container">
        <div class="test-header">
            <h2>🔄 Test Phase 5: Complete User Flow</h2>
        </div>

        <div class="test-step">
            <h3>Step 5.1: Multiple Recordings</h3>
            <div class="test-substep">
                <p>1. Record 3 videos for the same phrase</p>
                <p>2. Verify progress tracking updates</p>
                <p>3. Check for auto-advancement to next phrase</p>
            </div>
            <div class="checklist">
                <h4>✅ Expected Behavior:</h4>
                <ul>
                    <li>Each recording completes successfully</li>
                    <li>Progress counter updates (1/3, 2/3, 3/3)</li>
                    <li>Auto-advancement occurs after 3rd recording</li>
                    <li>localStorage tracking works correctly</li>
                </ul>
            </div>
        </div>

        <div class="test-step">
            <h3>Step 5.2: Error Recovery</h3>
            <div class="test-substep">
                <p>1. Test recording with poor lighting</p>
                <p>2. Try recording without speaking</p>
                <p>3. Test network disconnection scenarios</p>
            </div>
            <div class="checklist">
                <h4>✅ Expected Behavior:</h4>
                <ul>
                    <li>Graceful error handling</li>
                    <li>User-friendly error messages</li>
                    <li>Ability to retry failed recordings</li>
                </ul>
            </div>
        </div>
    </div>

    <div class="test-container">
        <div class="test-header">
            <h2>🛠️ Troubleshooting Guide</h2>
        </div>

        <div class="test-step">
            <h3>Common Issues & Solutions</h3>

            <div class="test-substep">
                <h4>🔴 Issue: "No video data captured"</h4>
                <p><strong>Cause:</strong> MediaRecorder not receiving data from webcam stream</p>
                <p><strong>Solution:</strong> Check camera permissions, refresh page, try different browser</p>
            </div>

            <div class="test-substep">
                <h4>🔴 Issue: Upload fails with network error</h4>
                <p><strong>Cause:</strong> Backend server not accessible or wrong port</p>
                <p><strong>Solution:</strong> Verify backend is running on correct port, check CORS settings</p>
            </div>

            <div class="test-substep">
                <h4>🔴 Issue: Infinite loops in console</h4>
                <p><strong>Cause:</strong> LipNet processing not properly disabled</p>
                <p><strong>Solution:</strong> Check that lipnetProcessingActive is false, refresh page</p>
            </div>

            <div class="test-substep">
                <h4>🔴 Issue: Recording validation failed</h4>
                <p><strong>Cause:</strong> Empty video blob or missing metadata</p>
                <p><strong>Solution:</strong> Check video blob size in console, verify webcam stream is active</p>
            </div>
        </div>

        <div class="test-step">
            <h3>Debug Commands</h3>
            <div class="console-output">
// Check localStorage data
console.log('Selected phrases:', localStorage.getItem('icu_selected_phrases'));
console.log('Recording counts:', localStorage.getItem('icuAppRecordingsCount'));

// Check webcam stream
console.log('Webcam ref:', webcamRef.current?.stream);
console.log('Video tracks:', webcamRef.current?.stream?.getVideoTracks());

// Check backend connectivity
fetch('http://localhost:5001/health').then(r => r.json()).then(console.log);
            </div>
        </div>
    </div>

    <div class="test-container">
        <div class="test-header">
            <h2>📊 Test Results Summary</h2>
        </div>

        <div class="test-step">
            <h3>Test Completion Checklist</h3>
            <div class="checklist">
                <h4>Mark as Complete When:</h4>
                <ul>
                    <li>☐ Application loads without infinite loops</li>
                    <li>☐ Camera permissions work correctly</li>
                    <li>☐ Recording starts and stops automatically</li>
                    <li>☐ Video blob has content (size > 0)</li>
                    <li>☐ AWS S3 upload completes successfully</li>
                    <li>☐ No "recording validation failed" errors</li>
                    <li>☐ Progress tracking updates correctly</li>
                    <li>☐ Auto-advancement works (if applicable)</li>
                    <li>☐ Error handling is graceful</li>
                    <li>☐ Multiple recordings work in sequence</li>
                </ul>
            </div>
        </div>

        <div class="warning">
            <h4>🚨 If Any Tests Fail:</h4>
            <p>1. Document the exact error messages from console</p>
            <p>2. Note the specific step where failure occurred</p>
            <p>3. Check network connectivity and server status</p>
            <p>4. Try refreshing the page and retesting</p>
            <p>5. Report issues with detailed reproduction steps</p>
        </div>
    </div>

</body>
</html>
