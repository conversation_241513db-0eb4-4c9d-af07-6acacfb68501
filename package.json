{"name": "icu-dataset-application", "version": "1.0.0", "description": "ICU Dataset Collection Application", "main": "index.js", "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "dependencies": {"@aws-sdk/client-s3": "^3.830.0", "@aws-sdk/client-sagemaker": "^3.835.0", "@aws-sdk/credential-providers": "^3.830.0", "@emotion/react": "^11.11.0", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.14.0", "@mui/material": "^5.14.0", "@tensorflow-models/face-landmarks-detection": "^1.0.6", "@tensorflow/tfjs": "^4.22.0", "aws-sdk": "^2.1400.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "react-webcam": "^7.2.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}