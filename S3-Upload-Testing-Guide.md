# 🧪 S3 Upload Testing Guide

## 📋 **Pre-Test Checklist**

### ✅ **AWS Configuration Verification**
Your current `.env` configuration:
```
REACT_APP_AWS_IDENTITY_POOL_ID=ap-southeast-2:59baeaf9-a80c-4afc-8f27-f4d6191242dd
REACT_APP_AWS_REGION=ap-southeast-2
REACT_APP_S3_BUCKET=icudatasetphrasesfortesting
```

**Status**: ✅ Configuration looks correct for ap-southeast-2 region

---

## 🎯 **Step-by-Step Testing Process**

### **Step 1: Open the Application**
1. Navigate to: `http://localhost:3000?direct=recording`
2. Allow camera permissions when prompted
3. Look for the **"🧪 Test S3 Upload"** button in the top-right corner

### **Step 2: Run S3 Connection Tests**
1. **Click the "🧪 Test S3 Upload" button** to open the S3 Test Panel
2. **Connection Test** (runs automatically):
   - ✅ **Expected Success**: "✅ Connection Successful!" with bucket details
   - ❌ **If Failed**: Check error code and message for specific issue

3. **Upload Test** (click "Run Upload Test"):
   - ✅ **Expected Success**: "✅ Upload Successful!" with file URL
   - ❌ **If Failed**: Check error details for permissions/access issues

### **Step 3: Monitor Browser Console**
**Open Developer Tools** (F12 → Console tab) and look for:

#### **🔍 Connection Test Logs**:
```
🧪 Testing AWS connection...
🔍 Testing bucket access...
✅ AWS connection test successful!
📊 Bucket access confirmed: {bucket: "icudatasetphrasesfortesting", region: "ap-southeast-2", objectCount: X}
```

#### **🔍 Upload Test Logs**:
```
🧪 Testing S3 upload with test file...
🔄 Uploading test file...
✅ Test upload successful!
📊 Upload result: {ETag: "...", Location: "..."}
✅ Test file verified in bucket!
```

### **Step 4: Test Video Recording Upload**
1. **Close the S3 Test Panel** (if tests passed)
2. **Record a 5-second video** using the main interface
3. **Monitor console for detailed upload logs**:

#### **🔍 Expected Video Upload Logs**:
```
=== AWS STORAGE: Starting upload process ===
🔍 AWS Configuration Check:
  - REACT_APP_AWS_IDENTITY_POOL_ID: ap-southeast-2:59baeaf9-a80c-4afc-8f27-f4d6191242dd
  - REACT_APP_AWS_REGION: ap-southeast-2
  - REACT_APP_S3_BUCKET: icudatasetphrasesfortesting
  - isAWSConfigured(): true
  - s3Client initialized: true

🔄 Generating S3 key...
Generated S3 key: icu-videos/40to64/female/not_specified/[phrase]/[filename].mp4

🚀 Starting S3 upload...
🔍 About to send PutObjectCommand with params: {bucket: "...", key: "...", contentType: "video/mp4", bodySize: XXXXX}

✅ S3 upload completed successfully!
📊 Upload result details: {ETag: "...", Location: "..."}
🌐 File should be available at: https://icudatasetphrasesfortesting.s3.ap-southeast-2.amazonaws.com/icu-videos/...
```

---

## 🔍 **Expected S3 File Structure**

### **Path Format**:
```
icu-videos/[AGEGROUP]/[GENDER]/[ETHNICITY]/[PHRASE_LABEL]/[FILENAME].mp4
```

### **Example Path**:
```
icu-videos/40to64/female/not_specified/i_need_water/i_need_water__user01__40to64__female__not_specified__20241222_143052.mp4
```

### **Metadata Attached**:
- `phrase`: The spoken phrase
- `userId`: user01 (default)
- `ageGroup`: 40to64 (default)
- `gender`: female (default)
- `ethnicity`: not_specified (default)
- `timestamp`: ISO timestamp

---

## ❌ **Common Issues & Solutions**

### **Issue 1: "Access Denied" Error**
**Symptoms**: 
```
❌ S3 upload failed with error: AccessDenied
📋 Error details: {statusCode: 403}
```

**Solutions**:
1. **Check Cognito Identity Pool permissions**
2. **Verify S3 bucket policy allows uploads**
3. **Ensure bucket exists in ap-southeast-2 region**

### **Issue 2: "NoSuchBucket" Error**
**Symptoms**:
```
❌ AWS connection test failed: NoSuchBucket
📋 Error details: {statusCode: 404}
```

**Solutions**:
1. **Verify bucket name**: `icudatasetphrasesfortesting`
2. **Check region**: Must be `ap-southeast-2`
3. **Create bucket if it doesn't exist**

### **Issue 3: "Credentials not configured" Error**
**Symptoms**:
```
❌ S3 client not initialized
⚠️ AWS credentials not configured. S3 uploads will be simulated.
```

**Solutions**:
1. **Check .env file** is in project root
2. **Restart development server** after .env changes
3. **Verify Identity Pool ID format**: `region:uuid`

### **Issue 4: Network/CORS Errors**
**Symptoms**:
```
❌ S3 upload failed with error: NetworkingError
📋 Error details: {message: "Network request failed"}
```

**Solutions**:
1. **Check internet connection**
2. **Verify CORS configuration on S3 bucket**
3. **Check firewall/proxy settings**

---

## 🎯 **Success Criteria**

### ✅ **Connection Test Success**:
- AWS credentials authenticate successfully
- S3 bucket is accessible
- ListObjects operation succeeds
- Object count is returned

### ✅ **Upload Test Success**:
- Test file uploads successfully
- File appears in S3 bucket
- Metadata is correctly attached
- File URL is accessible

### ✅ **Video Upload Success**:
- Video blob is created from recording
- S3 key is generated with correct path structure
- Upload completes without errors
- File appears in expected S3 location
- Metadata includes all required fields

---

## 📞 **Next Steps After Testing**

### **If All Tests Pass** ✅:
1. **Proceed with normal video recording**
2. **Verify files appear in S3 console**
3. **Check file sizes and metadata**
4. **Test multiple recordings**

### **If Tests Fail** ❌:
1. **Copy error messages from console**
2. **Check AWS IAM permissions**
3. **Verify S3 bucket configuration**
4. **Review network connectivity**
5. **Contact AWS support if needed**

---

**🔧 Enhanced Debugging**: The application now includes comprehensive logging and error handling to help identify and resolve any S3 upload issues quickly.
