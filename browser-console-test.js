/**
 * <PERSON><PERSON>er Console Test for Receipt Mapping Service
 * 
 * Copy and paste this script into the browser console while on the ICU Dataset Application
 * (localhost:3004) to test the receipt mapping functionality directly.
 * 
 * Instructions:
 * 1. Open ICU Dataset Application (localhost:3004)
 * 2. Open browser console (F12)
 * 3. Copy and paste this entire script
 * 4. Press Enter to run the test
 */

console.log('🧪 === RECEIPT MAPPING SERVICE BROWSER TEST ===');
console.log('');

// Test function to check receipt mapping service
async function testReceiptMappingService() {
    try {
        console.log('📋 Testing Receipt Mapping Service...');
        console.log('');
        
        // Test 1: Check if receiptMappingService is available
        console.log('🔍 Test 1: Check if receiptMappingService is available');
        
        // Try to import the service (this should work in the React app context)
        let receiptMappingService;
        try {
            // This will work if we're in the React app context
            const module = await import('./src/services/receiptMappingService.js');
            receiptMappingService = module.receiptMappingService;
            console.log('✅ receiptMappingService imported successfully');
        } catch (importError) {
            console.log('❌ Failed to import receiptMappingService:', importError.message);
            console.log('💡 This test must be run from the React app context (localhost:3004)');
            return;
        }
        
        console.log('');
        
        // Test 2: Check AWS configuration
        console.log('🔧 Test 2: Check AWS configuration');
        console.log('AWS Region:', process.env.REACT_APP_AWS_REGION || 'Not set');
        console.log('S3 Bucket:', process.env.REACT_APP_S3_BUCKET || 'Not set');
        console.log('Identity Pool ID:', process.env.REACT_APP_AWS_IDENTITY_POOL_ID ? 'Set' : 'Not set');
        console.log('');
        
        // Test 3: Try to get existing receipt log
        console.log('📄 Test 3: Try to get existing receipt log');
        try {
            const existingLog = await receiptMappingService.getReceiptLog();
            console.log('✅ Successfully retrieved receipt log');
            console.log('📊 Existing receipts:', Object.keys(existingLog).length);
            if (Object.keys(existingLog).length > 0) {
                console.log('📋 Receipt numbers:', Object.keys(existingLog).join(', '));
            }
        } catch (logError) {
            console.log('❌ Failed to get receipt log:', logError.message);
            console.log('💡 This might be expected if the file doesn\'t exist yet');
        }
        console.log('');
        
        // Test 4: Try to create a test receipt mapping
        console.log('💾 Test 4: Try to create a test receipt mapping');
        const testReceiptNumber = '000004';
        const testVideoUrls = [
            'https://s3.amazonaws.com/icudatasetphrasesfortesting/icu-videos/25to40/female/caucasian/hello/test_video_' + Date.now() + '.webm'
        ];
        const testDemographics = {
            age: '25to40',
            gender: 'female',
            ethnicity: 'caucasian'
        };
        const testSessionId = 'browser-test-' + Date.now();
        
        console.log('🧪 Test parameters:');
        console.log('  Receipt Number:', testReceiptNumber);
        console.log('  Video URLs:', testVideoUrls);
        console.log('  Demographics:', testDemographics);
        console.log('  Session ID:', testSessionId);
        console.log('');
        
        try {
            const mappingResult = await receiptMappingService.addReceiptMapping(
                testReceiptNumber,
                testVideoUrls,
                testDemographics,
                testSessionId
            );
            
            if (mappingResult) {
                console.log('✅ Receipt mapping created successfully!');
                console.log('🎉 receipt-log.json should now exist in S3');
            } else {
                console.log('❌ Receipt mapping failed (returned false)');
            }
        } catch (mappingError) {
            console.log('❌ Receipt mapping failed with error:', mappingError.message);
            console.log('📋 Full error:', mappingError);
        }
        console.log('');
        
        // Test 5: Verify the mapping was created
        console.log('🔍 Test 5: Verify the mapping was created');
        try {
            const updatedLog = await receiptMappingService.getReceiptLog();
            if (updatedLog[testReceiptNumber]) {
                console.log('✅ Test receipt mapping verified in log');
                console.log('📄 Receipt data:', updatedLog[testReceiptNumber]);
            } else {
                console.log('❌ Test receipt mapping not found in log');
            }
        } catch (verifyError) {
            console.log('❌ Failed to verify receipt mapping:', verifyError.message);
        }
        console.log('');
        
        // Summary
        console.log('📊 TEST SUMMARY');
        console.log('===============');
        console.log('If all tests passed, receipt-log.json should now exist in S3 at:');
        console.log('s3://icudatasetphrasesfortesting/receipt-numbers/receipt-log.json');
        console.log('');
        console.log('If tests failed, check:');
        console.log('1. AWS credentials and permissions');
        console.log('2. S3 bucket access');
        console.log('3. Network connectivity');
        console.log('4. CORS settings for S3');
        
    } catch (error) {
        console.error('❌ Test execution failed:', error);
    }
}

// Test function to check localStorage receipt counter
function testReceiptCounter() {
    console.log('🔢 Testing Receipt Counter...');
    console.log('');
    
    const currentCounter = localStorage.getItem('icuAppReceiptCounter');
    console.log('Current receipt counter:', currentCounter || 'Not set');
    
    if (currentCounter) {
        const nextReceipt = (parseInt(currentCounter) + 1).toString().padStart(6, '0');
        console.log('Next receipt number would be:', nextReceipt);
    } else {
        console.log('Next receipt number would be: 000001');
    }
    console.log('');
}

// Test function to simulate receipt generation
function testReceiptGeneration() {
    console.log('📋 Testing Receipt Generation...');
    console.log('');
    
    const generateReceiptNumber = () => {
        try {
            const currentCounter = parseInt(localStorage.getItem('icuAppReceiptCounter') || '0', 10);
            const nextCounter = currentCounter + 1;
            localStorage.setItem('icuAppReceiptCounter', nextCounter.toString());
            return nextCounter.toString().padStart(6, '0');
        } catch (error) {
            console.warn('Error generating receipt number:', error);
            return Date.now().toString().slice(-6);
        }
    };
    
    const newReceipt = generateReceiptNumber();
    console.log('Generated receipt number:', newReceipt);
    console.log('Updated localStorage counter:', localStorage.getItem('icuAppReceiptCounter'));
    console.log('');
}

// Run all tests
console.log('🚀 Starting Receipt Mapping Tests...');
console.log('');

// Test receipt counter first
testReceiptCounter();

// Test receipt generation
testReceiptGeneration();

// Test the main receipt mapping service
testReceiptMappingService();

console.log('');
console.log('🎯 INSTRUCTIONS FOR MANUAL VERIFICATION:');
console.log('========================================');
console.log('1. Check AWS S3 Console for receipt-numbers/receipt-log.json');
console.log('2. Complete a recording session in the app');
console.log('3. Look for console messages about receipt mapping');
console.log('4. Verify receipt numbers are displayed correctly');
console.log('');
console.log('📞 If issues persist, check:');
console.log('- Browser network tab for failed S3 requests');
console.log('- AWS CloudWatch logs for permission errors');
console.log('- CORS configuration for S3 bucket');
