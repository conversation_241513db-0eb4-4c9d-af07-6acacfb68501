# ICU Dataset Application v21.6.25 - Complete UX Design Specification

## 1. Color Scheme & Theme

### Primary Colors
- **Primary**: `#009688` (Teal 500)
- **Primary Dark**: `#00796b` (Teal 700)
- **Primary Light**: `#26a69a` (Teal 400)
- **Secondary**: `#ef5350` (Red 400)
- **Secondary Dark**: `#e53935` (Red 600)

### Background Colors
- **App Background**: Linear gradient `rgba(0, 150, 136, 0.05)` to `rgba(38, 166, 154, 0.05)`
- **Body Background**: Linear gradient `#f5f7fa` to `#e4e8eb`
- **Card/Container Background**: `#ffffff`
- **Phrase Display Background**: Linear gradient `#ffffff` to `#e0f2f1`
- **Completed Phrase Background**: `#e8f5e9` (Light Green 50)

### Text Colors
- **Primary Text**: `#212121` (Grey 900)
- **Secondary Text**: `#757575` (Grey 600)
- **Disabled Text**: `#bdbdbd` (Grey 400)
- **Header Text**: `#ffffff` on teal background

### Accent Colors
- **Success**: `#4caf50` (Green 500)
- **Warning**: `#ff9800` (Orange 500)
- **Error**: `#f44336` (Red 500)
- **Info**: `#2196f3` (Blue 500)

## 2. Typography

- **Font Family**: 'Roboto', sans-serif
- **Header Sizes**:
  - H1: 2.5rem, 700 weight
  - H2: 2rem, 600 weight
  - H3: 1.75rem, 600 weight
  - H4: 1.5rem, 500 weight
  - H5: 1.25rem, 500 weight
  - H6: 1rem, 500 weight
- **Body Text**: 1rem (16px), 400 weight
- **Caption/Small Text**: 0.875rem (14px), 400 weight

## 3. Layout & Spacing

### Container Sizes
- **Max Width**: 1200px for main content
- **Content Width**: 100% on mobile, 80% on desktop
- **Video Container**: Max width 800px on mobile, 900px on laptop, 1000px on desktop

### Spacing System
- **Base Unit**: 8px
- **Margins**:
  - Section Margins: 2rem (32px)
  - Component Margins: 1.5rem (24px)
  - Inner Element Margins: 1rem (16px)
  - Small Element Margins: 0.5rem (8px)
- **Padding**:
  - Container Padding: 2rem (32px)
  - Card Padding: 1.5rem (24px)
  - Button Padding: 12px 24px (desktop), 8px 16px (mobile)

### Grid System
- **12-column grid** based on Material-UI's grid system
- **Responsive Breakpoints**:
  - xs: 0px
  - sm: 600px
  - md: 900px
  - lg: 1200px
  - xl: 1536px

## 4. Component Styling

### Buttons
- **Primary Button**:
  - Background: `#009688`
  - Text: White
  - Border Radius: 4px
  - Padding: 12px 24px (desktop), 8px 16px (mobile)
  - Hover Effect: Darken background, slight elevation increase
  - Min Width: 120px
  - Box Shadow: `0 2px 5px rgba(0, 0, 0, 0.2)`
  - Transition: All 0.3s ease

- **Secondary Button**:
  - Border: 1px solid `#009688`
  - Text: `#009688`
  - Background: Transparent
  - Same dimensions as Primary Button

- **Error/Stop Button**:
  - Background: `#f44336`
  - Text: White

### Cards & Containers
- **Card Style**:
  - Background: White
  - Border Radius: 12px
  - Box Shadow: `0 4px 20px rgba(0, 150, 136, 0.15)`
  - Padding: 2rem
  - Margin: 1.5rem 0

- **Category Card**:
  - Border: 2px solid transparent
  - Hover: Transform translateY(-5px), border-color `rgba(38, 166, 154, 0.3)`
  - Transition: All 0.3s ease
  - Cursor: Pointer

### Form Elements
- **Input Fields**:
  - Border Radius: 4px
  - Border: 1px solid `rgba(0, 0, 0, 0.23)`
  - Focus: Border 2px solid `#009688`
  - Height: 56px
  - Label Color: `#757575`
  - Font Size: 1rem

- **Slider**:
  - Track Color: `rgba(0, 150, 136, 0.24)`
  - Thumb Color: `#009688`
  - Rail Height: 4px
  - Thumb Size: 20px

- **Checkbox/Radio**:
  - Checked Color: `#009688`
  - Size: 20px

### Progress Indicators
- **Linear Progress**:
  - Height: 4px
  - Border Radius: 2px
  - Colors: Success (`#4caf50`), Error (`#f44336`)

- **Circular Progress**:
  - Size: 24px (small), 40px (medium)
  - Color: `#009688`

### Notifications
- **Snackbar**:
  - Position: Top center
  - Background: Based on severity (success: green, error: red)
  - Border Radius: 4px
  - Animation: Slide down and fade in
  - Auto Hide: 3000ms (3 seconds)

## 5. Video Recording Interface

### Webcam Container
- **Shape**: Rounded rectangle with 4:3 aspect ratio
- **Border Radius**: 12px (mobile/tablet), 16px (desktop)
- **Box Shadow**: `0 4px 20px rgba(0, 150, 136, 0.15)` (mobile/tablet), `0 8px 30px rgba(0, 150, 136, 0.15)` (desktop)
- **Max Height**: 80vh (mobile), 60vh (laptop), adjustable
- **Width**: 100% (mobile), 80% (desktop), max-width 800px-1000px
- **Margin**: 1.5rem auto (mobile), 2rem auto (laptop), 2.5rem auto (desktop)

### Oval Guide Overlay
- **Shape**: Oval with `borderRadius: '50% / 60%'` (more height than width)
- **Size**: Responsive - xs: 260px×325px, sm: 300px×375px, md: 350px×440px, lg: 380px×480px
- **Border**: 3px dashed, teal when ready, red when recording
- **Position**: Absolute, centered (top 50%, left 50%, transform translate(-50%, -50%))
- **Opacity**: 0.8 normal, 0.9 when recording
- **Instruction Text**: "Position Face in Oval" at top, "Ready"/"Speaking..." at bottom
- **Recording Indicator**: Red pulsing circle at top-right when recording
- **Z-Index**: 10 (above video but below controls)

### Zoom Controls
- **Slider**: Material-UI Slider component
- **Range**: 1.0x to 3.0x (maximum)
- **Step**: 0.1
- **Width**: 80% of container
- **Margin**: 1rem auto
- **Labels**: Min (1x) and Max (3x) on ends

### Recording Controls
- **Start Button**: Green/primary color, "Start Recording" text
- **Stop Button**: Red color, "Stop Recording" text
- **Record Again Button**: Outlined style, secondary color
- **Button Size**: minWidth 120px
- **Layout**: Centered, horizontal arrangement with 2rem gap

### Recording Indicators
- **Status**: Red pulsing circle when recording
- **Timer**: Countdown from 3 seconds
- **Completion**: Green checkmark in box showing "Completed: X/3"
- **Quality Metrics**:
  - Linear progress bars for Brightness, Sharpness, Face Detection
  - Color-coded (green for good, red for poor)
  - Overall quality rating (POOR, FAIR, GOOD, EXCELLENT) with color coding:
    - Poor: `#f44336` (Red)
    - Fair: `#ff9800` (Orange)
    - Good: `#2196f3` (Blue)
    - Excellent: `#4caf50` (Green)

### Face/Mouth Detection
- **Technology**: MediaPipe Face Mesh for mouth region detection
- **Target Area**: Mouth region (100x50 pixels)
- **Visualization**: Optional bounding box around detected mouth
- **Quality Indicator**: Face confidence percentage
- **Instructions**: "Position your face within the oval showing just your lips and chin"

## 6. Navigation System

### Main Navigation Menu
- **Position**: Absolute, top: 10px, left: 10px, zIndex: 1000
- **Button Style**: 
  - Material-UI contained button
  - Primary color
  - "Menu" text with MenuIcon
  - Bold font weight
  - Box shadow: 3
  - Hover effect: Darker primary color

### Menu Items
- **Width**: minWidth: 180px
- **Display**: Flex with gap: 1
- **Selected State**: Highlighted background
- **Disabled State**: Opacity: 0.5
- **Icons**: Material-UI icons for each menu item
- **Menu Items**:
  1. Home (HomeIcon)
  2. Demographics (PersonIcon)
  3. Training Video (PlayCircleOutlineIcon)
  4. Select Phrases (ListAltIcon)
  5. Recording (VideocamIcon)
  6. Progress (BarChartIcon)
  7. Sample Tracker (EqualizerIcon)
  8. Metadata Manifest (DescriptionIcon)
  9. Demographic Evaluation (AssessmentIcon)

## 7. Page-Specific Elements

### Consent Page
- **Container**: White background, 12px border radius, 2rem padding
- **Header**: Teal gradient background (`linear-gradient(90deg, #009688 0%, #26a69a 100%)`), white text, 1.5rem padding
- **Sections**: Separated by 1.5rem margin, light border bottom (`1px solid rgba(0, 150, 136, 0.2)`)
- **Checkbox**: Teal when checked

### Demographics Form
- **Layout**: Grid layout with responsive columns
- **Field Groups**: Logical grouping with subheadings
- **Required Fields**: Marked with asterisk
- **Validation**: Inline error messages
- **Age Groups**: Exactly "18to39", "40to64", "65plus"
- **Gender Options**: "male", "female", "nonbinary"
- **Ethnicity Categories**: Comprehensive list with subcategories:
  - Caucasian/White
  - Asian (with subcategory examples)
  - African Descent/Black
  - Hispanic/Latin American
  - Middle Eastern/North African
  - Pacific Islander
  - Aboriginal/Torres Strait Islander
  - Indigenous/First Nations (non-Australia)
  - Mixed/Multiple ethnicities

### Phrase Selection
- **Category Cards**: Grid layout, hover effect
- **Selected State**: Border highlight
- **Phrase List**: No scrolling restrictions
- **Select All**: Checkbox when category has ≤10 items
- **Completion Indicator**: Green background for completed phrases

### Training Video Page
- **Video Player**: Centered, responsive size
- **Instructions**: "Position your face within the oval showing just your lips and chin"
- **Next Button**: Primary color, bottom right
- **Progress Indicator**: Step indicator at top

### Progress Tracking
- **Progress Bars**: Linear progress with percentage
- **Category Breakdown**: Visual representation by category
- **Target**: 20 recordings per phrase (reduced from 60)
- **Auto-Update**: After each recording via React useEffect

## 8. Camera Functionality

### Video Processing
- **Resolution**: 640x480 standard (adjustable based on device)
- **Frame Rate**: 30fps capture, 25fps processing for LipNet
- **Format**: MP4 with H.264 encoding
- **Duration**: 3-second recording standard
- **Processing**: 
  - Grayscale conversion
  - Mouth region cropping to 100x50 resolution
  - Face mesh detection for precise mouth tracking

### Quality Validation
- **Brightness**: 40-240 range (out of 255)
- **Sharpness**: >20 threshold
- **Face Detection**: Confidence score >0.7
- **Overall Rating**: 
  - Poor: <50% metrics passing
  - Fair: 50-70% metrics passing
  - Good: 70-90% metrics passing
  - Excellent: >90% metrics passing

### File Naming Convention
- Format: `[PHRASE_LABEL]__[USER_ID]__[AGEGROUP]__[GENDER]__[ETHNICITY]__[TIMESTAMP].mp4`
- All lowercase ASCII
- Underscores only
- No punctuation

## 9. Animations & Transitions

### Page Transitions
- **Fade In/Out**: 300ms duration
- **Timing Function**: ease-in-out

### Interactive Elements
- **Button Hover**: Scale up slightly (transform: translateY(-2px))
- **Card Hover**: translateY(-5px) with shadow increase
- **Transition**: all 0.3s ease

### Loading States
- **Circular Progress**: Rotating animation
- **Pulse Effects**: 
  - Oval guide: 2s infinite pulse animation
  - Recording indicator: 2s infinite red pulse

## 10. Responsive Design

### Mobile (< 600px)
- **Full Width** containers
- **Stacked** form fields and controls
- **Simplified** navigation
- **Larger** touch targets (min 44px)
- **Oval Guide**: 260px×325px

### Tablet (600px - 900px)
- **80-90%** width containers
- **2-column** grid for forms
- **Standard** button sizes
- **Oval Guide**: 300px×375px

### Laptop (900px - 1200px)
- **Webcam Container**: 80% width, max-width 900px
- **3-column** grid for forms and cards
- **Enhanced** hover effects
- **Oval Guide**: 350px×440px

### Desktop (> 1200px)
- **Webcam Container**: max-width 1000px
- **4-column** grid for dense information
- **Full** feature set visible
- **Keyboard shortcuts** available
- **Oval Guide**: 380px×480px

## 11. AWS Integration

### S3 Storage Structure
```
icu-videos/
  [AGEGROUP]/
    [GENDER]/
      [ETHNICITY]/
        [PHRASE_LABEL]/
          [FILENAME].mp4
```

### Metadata Manifest
- **Format**: CSV and JSON options
- **Columns**: `filename,phrase_label,user_id,agegroup,gender,ethnicity,timestamp`
- **Storage**: Auto-generated in S3 bucket
- **Updates**: Real-time after each recording

### SageMaker Integration
- **Evaluation Dashboard**: Demographic-based performance metrics
- **Visualizations**: Confusion matrices, performance charts
- **Analysis**: Intersectional demographic analysis
- **Reports**: Exportable CSV/JSON reports

## 12. Implementation Notes

1. **Framework**: React with Material-UI components
2. **State Management**: React hooks (useState, useEffect, useRef, useCallback)
3. **Video Processing**: MediaPipe + TensorFlow.js for face detection
4. **AWS SDK**: Version 3.x for S3 and SageMaker integration
5. **Responsive Design**: Material-UI's responsive system with breakpoints
6. **Video Constants**:
   ```javascript
   const VIDEO_CONSTRAINTS = {
     width: 640,
     height: 480,
     frameRate: 30,
     facingMode: 'user'
   };
   
   const MOUTH_ROI = {
     width: 100,
     height: 50
   };
   
   const LIPNET_OPTIONS = {
     targetWidth: 100,
     targetHeight: 50,
     frameRate: 25,
     grayscale: true
   };
   ```
