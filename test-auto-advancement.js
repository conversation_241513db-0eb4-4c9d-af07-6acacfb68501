// Test script for auto-advancement functionality
// Run this in the browser console after navigating to the recording page

console.log('🧪 Starting Auto-Advancement Test');

// Function to wait for a condition
function waitFor(condition, timeout = 5000) {
  return new Promise((resolve, reject) => {
    const startTime = Date.now();
    const check = () => {
      if (condition()) {
        resolve();
      } else if (Date.now() - startTime > timeout) {
        reject(new Error('Timeout waiting for condition'));
      } else {
        setTimeout(check, 100);
      }
    };
    check();
  });
}

// Test auto-advancement
async function testAutoAdvancement() {
  try {
    console.log('🔍 Step 1: Check initial state');
    window.debugCurrentState();
    
    const initialPhraseIndex = window.currentPhraseIndex || 0;
    console.log('Initial phrase index:', initialPhraseIndex);
    
    console.log('🔧 Step 2: Force recording count to 3 for current phrase');
    window.debugForceRecordingCount();
    
    console.log('⏳ Step 3: Wait for auto-advancement to trigger...');
    
    // Wait for phrase index to change (indicating auto-advancement worked)
    await waitFor(() => {
      const currentIndex = window.currentPhraseIndex;
      console.log('Checking phrase index:', currentIndex, 'vs initial:', initialPhraseIndex);
      return currentIndex > initialPhraseIndex;
    }, 3000);
    
    console.log('✅ SUCCESS: Auto-advancement worked!');
    console.log('🔍 Final state:');
    window.debugCurrentState();
    
    return true;
  } catch (error) {
    console.error('❌ FAILED: Auto-advancement test failed:', error);
    console.log('🔍 Final state:');
    window.debugCurrentState();
    return false;
  }
}

// Instructions for manual testing
console.log(`
🧪 AUTO-ADVANCEMENT TEST INSTRUCTIONS:

1. Navigate to the ICU dataset application
2. Complete consent and demographics
3. Select multiple phrases for recording
4. Navigate to the recording page
5. Run this test: testAutoAdvancement()

Or test manually:
1. Record 2 videos for the first phrase
2. On the 3rd recording, the app should automatically advance to the next phrase
3. Check console logs for auto-advancement messages

Debug functions available:
- window.debugCurrentState() - Check current state
- window.debugForceRecordingCount() - Force count to 3 for testing
- window.debugAutoAdvancement() - Test auto-advancement manually
`);

// Export the test function to global scope
window.testAutoAdvancement = testAutoAdvancement;
