#!/usr/bin/env node

/**
 * Lip Guide Overlay Verification Tool
 * Verifies that the semi-transparent lip guide overlay is working correctly
 * in the ICU Dataset Application VideoRecorder component
 */

const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

// Configuration
const APP_URL = 'http://localhost:3000?direct=recording';
const MOUTH_TEST_URL = 'http://localhost:3000?direct=mouth-test';
const SCREENSHOT_DIR = './lip-guide-verification';

// Verification criteria
const VERIFICATION_CRITERIA = {
  lipGuideImage: {
    src: '/images/lips.png',
    opacity: 0.35,
    position: 'absolute',
    top: '65%',
    left: '50%',
    width: '33%',
    zIndex: 3
  },
  conditionalVisibility: {
    showWhenCameraActive: true,
    showWhenModelLoaded: true,
    hideWhenRecording: true,
    smoothTransitions: true
  },
  positioning: {
    horizontalCenter: true,
    lowerHalfViewport: true,
    belowBlackOverlay: true,
    mirrorVideoPreview: true
  }
};

/**
 * Create screenshots directory
 */
function createScreenshotDir() {
  if (!fs.existsSync(SCREENSHOT_DIR)) {
    fs.mkdirSync(SCREENSHOT_DIR, { recursive: true });
    console.log(`📁 Created screenshot directory: ${SCREENSHOT_DIR}`);
  }
}

/**
 * Wait for element with timeout
 */
async function waitForElement(page, selector, timeout = 10000) {
  try {
    await page.waitForSelector(selector, { timeout });
    return true;
  } catch (error) {
    console.warn(`⚠️ Element not found: ${selector}`);
    return false;
  }
}

/**
 * Take screenshot with timestamp
 */
async function takeScreenshot(page, name) {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const filename = `${name}_${timestamp}.png`;
  const filepath = path.join(SCREENSHOT_DIR, filename);
  
  await page.screenshot({ 
    path: filepath, 
    fullPage: false,
    clip: { x: 0, y: 0, width: 1200, height: 800 }
  });
  
  console.log(`📸 Screenshot saved: ${filename}`);
  return filepath;
}

/**
 * Check if lip guide image exists and is accessible
 */
async function verifyLipGuideImage(page) {
  console.log('\n🔍 Verifying lip guide image...');
  
  try {
    // Check if image file exists
    const imageResponse = await page.goto('http://localhost:3000/images/lips.png');
    const imageExists = imageResponse.status() === 200;
    
    console.log(`📷 Lip guide image accessible: ${imageExists ? '✅' : '❌'}`);
    
    // Go back to recording page
    await page.goto(APP_URL);
    
    return imageExists;
  } catch (error) {
    console.error(`❌ Error checking lip guide image: ${error.message}`);
    return false;
  }
}

/**
 * Verify lip guide overlay properties
 */
async function verifyLipGuideProperties(page) {
  console.log('\n🎨 Verifying lip guide overlay properties...');
  
  try {
    // Wait for camera to load
    await page.waitForTimeout(3000);
    
    // Check if lip guide element exists
    const lipGuideExists = await page.$('img[src="/images/lips.png"]') !== null;
    console.log(`🖼️ Lip guide element exists: ${lipGuideExists ? '✅' : '❌'}`);
    
    if (!lipGuideExists) {
      return false;
    }
    
    // Get lip guide properties
    const lipGuideProps = await page.evaluate(() => {
      const lipGuide = document.querySelector('img[src="/images/lips.png"]');
      if (!lipGuide) return null;
      
      const computedStyle = window.getComputedStyle(lipGuide);
      const rect = lipGuide.getBoundingClientRect();
      
      return {
        src: lipGuide.src,
        opacity: parseFloat(computedStyle.opacity),
        position: computedStyle.position,
        top: computedStyle.top,
        left: computedStyle.left,
        width: computedStyle.width,
        zIndex: parseInt(computedStyle.zIndex),
        transform: computedStyle.transform,
        pointerEvents: computedStyle.pointerEvents,
        transition: computedStyle.transition,
        visible: computedStyle.display !== 'none' && computedStyle.visibility !== 'hidden',
        rect: {
          x: rect.x,
          y: rect.y,
          width: rect.width,
          height: rect.height
        }
      };
    });
    
    if (!lipGuideProps) {
      console.log('❌ Could not get lip guide properties');
      return false;
    }
    
    console.log('📊 Lip Guide Properties:');
    console.log(`   Opacity: ${lipGuideProps.opacity} (expected: 0.35) ${lipGuideProps.opacity === 0.35 ? '✅' : '❌'}`);
    console.log(`   Position: ${lipGuideProps.position} (expected: absolute) ${lipGuideProps.position === 'absolute' ? '✅' : '❌'}`);
    console.log(`   Top: ${lipGuideProps.top} (expected: 65%) ${lipGuideProps.top === '65%' ? '✅' : '❌'}`);
    console.log(`   Left: ${lipGuideProps.left} (expected: 50%) ${lipGuideProps.left === '50%' ? '✅' : '❌'}`);
    console.log(`   Width: ${lipGuideProps.width} (expected: 33%) ${lipGuideProps.width === '33%' ? '✅' : '❌'}`);
    console.log(`   Z-Index: ${lipGuideProps.zIndex} (expected: 3) ${lipGuideProps.zIndex === 3 ? '✅' : '❌'}`);
    console.log(`   Pointer Events: ${lipGuideProps.pointerEvents} (expected: none) ${lipGuideProps.pointerEvents === 'none' ? '✅' : '❌'}`);
    console.log(`   Transform: ${lipGuideProps.transform}`);
    console.log(`   Transition: ${lipGuideProps.transition}`);
    console.log(`   Visible: ${lipGuideProps.visible ? '✅' : '❌'}`);
    console.log(`   Dimensions: ${lipGuideProps.rect.width.toFixed(0)}×${lipGuideProps.rect.height.toFixed(0)}px`);
    
    return lipGuideProps;
  } catch (error) {
    console.error(`❌ Error verifying lip guide properties: ${error.message}`);
    return false;
  }
}

/**
 * Verify conditional visibility
 */
async function verifyConditionalVisibility(page) {
  console.log('\n👁️ Verifying conditional visibility...');
  
  try {
    // Wait for page to load
    await page.waitForTimeout(2000);
    
    // Check debug panel for model status
    const debugInfo = await page.evaluate(() => {
      const debugPanel = document.querySelector('[style*="position: fixed"][style*="top: 10"]');
      if (!debugPanel) return null;
      
      const text = debugPanel.textContent;
      return {
        modelLoaded: text.includes('Model: ✅'),
        faceDetection: text.includes('Face Detection: 🔄'),
        lipnetProcessing: text.includes('LipNet Processing: ✅'),
        mouthPosition: text.includes('Mouth Position: ✅')
      };
    });
    
    if (debugInfo) {
      console.log('🐛 Debug Panel Status:');
      console.log(`   Model Loaded: ${debugInfo.modelLoaded ? '✅' : '❌'}`);
      console.log(`   Face Detection: ${debugInfo.faceDetection ? '✅' : '❌'}`);
      console.log(`   LipNet Processing: ${debugInfo.lipnetProcessing ? '✅' : '❌'}`);
      console.log(`   Mouth Position: ${debugInfo.mouthPosition ? '✅' : '❌'}`);
    }
    
    // Check if lip guide is visible when conditions are met
    const lipGuideVisible = await page.evaluate(() => {
      const lipGuide = document.querySelector('img[src="/images/lips.png"]');
      if (!lipGuide) return false;
      
      const computedStyle = window.getComputedStyle(lipGuide);
      return computedStyle.display !== 'none' && 
             computedStyle.visibility !== 'hidden' && 
             parseFloat(computedStyle.opacity) > 0;
    });
    
    console.log(`👁️ Lip guide visible: ${lipGuideVisible ? '✅' : '❌'}`);
    
    return { debugInfo, lipGuideVisible };
  } catch (error) {
    console.error(`❌ Error verifying conditional visibility: ${error.message}`);
    return false;
  }
}

/**
 * Verify layering and integration
 */
async function verifyLayering(page) {
  console.log('\n📚 Verifying z-index layering...');
  
  try {
    const layering = await page.evaluate(() => {
      const elements = {
        video: document.querySelector('video'),
        canvas: document.querySelector('canvas'),
        lipGuide: document.querySelector('img[src="/images/lips.png"]'),
        blackOverlay: document.querySelector('[style*="background: rgba(0,0,0,1.0)"]'),
        debugPanel: document.querySelector('[style*="position: fixed"][style*="top: 10"]')
      };
      
      const getZIndex = (element) => {
        if (!element) return null;
        const style = window.getComputedStyle(element);
        return parseInt(style.zIndex) || 0;
      };
      
      return {
        video: getZIndex(elements.video),
        canvas: getZIndex(elements.canvas),
        lipGuide: getZIndex(elements.lipGuide),
        blackOverlay: getZIndex(elements.blackOverlay),
        debugPanel: getZIndex(elements.debugPanel)
      };
    });
    
    console.log('📚 Z-Index Layering:');
    console.log(`   Video: ${layering.video}`);
    console.log(`   Canvas: ${layering.canvas}`);
    console.log(`   Lip Guide: ${layering.lipGuide} (expected: 3)`);
    console.log(`   Black Overlay: ${layering.blackOverlay} (expected: 5)`);
    console.log(`   Debug Panel: ${layering.debugPanel} (expected: 9999)`);
    
    // Verify correct layering order
    const correctLayering = layering.lipGuide === 3 && 
                           layering.blackOverlay === 5 && 
                           layering.debugPanel === 9999;
    
    console.log(`📚 Correct layering order: ${correctLayering ? '✅' : '❌'}`);
    
    return layering;
  } catch (error) {
    console.error(`❌ Error verifying layering: ${error.message}`);
    return false;
  }
}

/**
 * Main verification function
 */
async function verifyLipGuideOverlay() {
  console.log('🎯 Starting Lip Guide Overlay Verification');
  console.log('=' .repeat(50));
  
  createScreenshotDir();
  
  let browser;
  try {
    // Launch browser
    browser = await puppeteer.launch({ 
      headless: false, // Set to true for headless mode
      defaultViewport: { width: 1200, height: 800 },
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    const page = await browser.newPage();
    
    // Enable camera permissions
    const context = browser.defaultBrowserContext();
    await context.overridePermissions(APP_URL, ['camera']);
    
    console.log(`🌐 Navigating to: ${APP_URL}`);
    await page.goto(APP_URL, { waitUntil: 'networkidle0' });
    
    // Wait for page to load
    await page.waitForTimeout(3000);
    
    // Take initial screenshot
    await takeScreenshot(page, 'initial_load');
    
    // Run verification tests
    const results = {
      imageExists: await verifyLipGuideImage(page),
      properties: await verifyLipGuideProperties(page),
      visibility: await verifyConditionalVisibility(page),
      layering: await verifyLayering(page)
    };
    
    // Take final screenshot
    await takeScreenshot(page, 'final_verification');
    
    // Generate report
    console.log('\n' + '='.repeat(50));
    console.log('📊 VERIFICATION REPORT');
    console.log('='.repeat(50));
    
    console.log(`\n✅ Image Accessibility: ${results.imageExists ? 'PASS' : 'FAIL'}`);
    console.log(`✅ Properties Configuration: ${results.properties ? 'PASS' : 'FAIL'}`);
    console.log(`✅ Conditional Visibility: ${results.visibility ? 'PASS' : 'FAIL'}`);
    console.log(`✅ Z-Index Layering: ${results.layering ? 'PASS' : 'FAIL'}`);
    
    const overallPass = results.imageExists && results.properties && results.visibility && results.layering;
    console.log(`\n🎯 OVERALL RESULT: ${overallPass ? '✅ PASS' : '❌ FAIL'}`);
    
    if (overallPass) {
      console.log('\n🎉 Lip guide overlay is working correctly!');
      console.log('The semi-transparent lip positioning guide meets all requirements.');
    } else {
      console.log('\n⚠️ Some verification tests failed. Please check the implementation.');
    }
    
    console.log(`\n📁 Screenshots saved in: ${SCREENSHOT_DIR}`);
    
  } catch (error) {
    console.error(`❌ Verification failed: ${error.message}`);
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

// Run verification if called directly
if (require.main === module) {
  verifyLipGuideOverlay().catch(console.error);
}

module.exports = { verifyLipGuideOverlay };
