<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ICU Dataset Application - Workflow Bypass Debug</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        h1 {
            color: #009688;
            text-align: center;
            margin-bottom: 30px;
        }
        h2 {
            color: #00796b;
            border-bottom: 2px solid #e0f2f1;
            padding-bottom: 10px;
        }
        .button-group {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        button {
            background: #009688;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: background-color 0.3s;
        }
        button:hover {
            background: #00796b;
        }
        button.danger {
            background: #f44336;
        }
        button.danger:hover {
            background: #d32f2f;
        }
        .output {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.4;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
            margin-top: 10px;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-good { background: #4caf50; }
        .status-warning { background: #ff9800; }
        .status-error { background: #f44336; }
    </style>
</head>
<body>
    <h1>🔍 ICU Dataset Application - Workflow Bypass Debugger</h1>
    
    <div class="container">
        <h2>📊 Current Application State Analysis</h2>
        <div class="button-group">
            <button onclick="analyzeCurrentState()">🔍 Analyze Current State</button>
            <button onclick="checkWorkflowConditions()">🔄 Check Workflow Conditions</button>
            <button onclick="inspectProviderState()">🏗️ Inspect Provider State</button>
        </div>
        <div id="state-output" class="output"></div>
    </div>

    <div class="container">
        <h2>🗄️ localStorage Investigation</h2>
        <div class="button-group">
            <button onclick="inspectLocalStorage()">📱 Inspect localStorage</button>
            <button onclick="analyzeRecordingCounts()">📈 Analyze Recording Counts</button>
            <button onclick="checkPhraseSelection()">📝 Check Phrase Selection</button>
        </div>
        <div id="localstorage-output" class="output"></div>
    </div>

    <div class="container">
        <h2>🔧 Workflow Debugging</h2>
        <div class="button-group">
            <button onclick="traceWorkflowPath()">🗺️ Trace Workflow Path</button>
            <button onclick="simulateWorkflow()">🎭 Simulate Workflow</button>
            <button onclick="checkCompletionLogic()">✅ Check Completion Logic</button>
        </div>
        <div id="workflow-output" class="output"></div>
    </div>

    <div class="container">
        <h2>🛠️ Fix Actions</h2>
        <div class="button-group">
            <button onclick="clearAllData()" class="danger">🗑️ Clear All Data</button>
            <button onclick="resetWorkflowState()">🔄 Reset Workflow State</button>
            <button onclick="forceRecordingMode()">🎥 Force Recording Mode</button>
        </div>
        <div id="fix-output" class="output"></div>
    </div>

    <script>
        function log(outputId, message) {
            const output = document.getElementById(outputId);
            output.textContent += message + '\n';
        }

        function clearLog(outputId) {
            document.getElementById(outputId).textContent = '';
        }

        function analyzeCurrentState() {
            clearLog('state-output');
            log('state-output', '🔍 Analyzing current application state...\n');
            
            // Check URL parameters
            const urlParams = new URLSearchParams(window.location.search);
            log('state-output', '🌐 URL Parameters:');
            if (urlParams.toString()) {
                log('state-output', `  ${urlParams.toString()}`);
            } else {
                log('state-output', '  No URL parameters');
            }
            
            // Check sessionStorage
            log('state-output', '\n📱 sessionStorage:');
            const sessionActive = sessionStorage.getItem('icuAppSessionActive');
            log('state-output', `  icuAppSessionActive: ${sessionActive || 'not set'}`);
            
            // Check if React app is loaded
            log('state-output', '\n⚛️ React Application:');
            if (window.React) {
                log('state-output', '  ✅ React is loaded');
            } else {
                log('state-output', '  ❌ React not detected');
            }
            
            // Check for React DevTools
            if (window.__REACT_DEVTOOLS_GLOBAL_HOOK__) {
                log('state-output', '  ✅ React DevTools available');
            } else {
                log('state-output', '  ⚠️ React DevTools not available');
            }
        }

        function inspectLocalStorage() {
            clearLog('localstorage-output');
            log('localstorage-output', '📱 Inspecting localStorage for ICU app data...\n');
            
            const relevantKeys = [
                'icuAppRecordingsCount',
                'icu_selected_phrases',
                'icuAppDemographics',
                'icuAppSelectedPhrases',
                'icuAppCompletedPhrases',
                'icuAppConsent',
                'testing_mode',
                'mock_recordings'
            ];
            
            let foundData = false;
            
            relevantKeys.forEach(key => {
                const value = localStorage.getItem(key);
                if (value) {
                    foundData = true;
                    log('localstorage-output', `🔑 ${key}:`);
                    try {
                        const parsed = JSON.parse(value);
                        log('localstorage-output', JSON.stringify(parsed, null, 2));
                    } catch (e) {
                        log('localstorage-output', `  ${value}`);
                    }
                    log('localstorage-output', '');
                }
            });
            
            if (!foundData) {
                log('localstorage-output', '✅ No ICU app data found in localStorage');
            }
        }

        function analyzeRecordingCounts() {
            clearLog('localstorage-output');
            log('localstorage-output', '📈 Analyzing recording counts...\n');
            
            const recordingCounts = localStorage.getItem('icuAppRecordingsCount');
            const selectedPhrases = localStorage.getItem('icu_selected_phrases');
            
            if (!recordingCounts) {
                log('localstorage-output', '✅ No recording counts found - clean state');
                return;
            }
            
            if (!selectedPhrases) {
                log('localstorage-output', '⚠️ Recording counts exist but no selected phrases');
                log('localstorage-output', 'This might indicate a workflow issue');
                return;
            }
            
            try {
                const counts = JSON.parse(recordingCounts);
                const phrases = JSON.parse(selectedPhrases);
                
                log('localstorage-output', '📊 Recording Counts Analysis:');
                log('localstorage-output', `  Total phrase keys: ${Object.keys(counts).length}`);
                log('localstorage-output', `  Selected phrases: ${phrases.length}`);
                
                let totalRecordings = 0;
                let completedPhrases = 0;
                
                Object.entries(counts).forEach(([phraseKey, count]) => {
                    totalRecordings += count;
                    if (count >= 3) completedPhrases++;
                    log('localstorage-output', `  ${phraseKey}: ${count} recordings ${count >= 3 ? '(COMPLETE ✅)' : ''}`);
                });
                
                log('localstorage-output', `\n📊 Summary:`);
                log('localstorage-output', `  Total recordings: ${totalRecordings}`);
                log('localstorage-output', `  Completed phrases: ${completedPhrases}`);
                
                // Check for workflow bypass conditions
                if (completedPhrases === phrases.length && phrases.length > 0) {
                    log('localstorage-output', '\n🚨 WORKFLOW BYPASS DETECTED!');
                    log('localstorage-output', 'All selected phrases are marked as complete.');
                    log('localstorage-output', 'This will cause the app to skip to completion page.');
                }
                
            } catch (e) {
                log('localstorage-output', `❌ Error parsing data: ${e.message}`);
            }
        }

        function checkWorkflowConditions() {
            clearLog('state-output');
            log('state-output', '🔄 Checking workflow conditions...\n');
            
            // Simulate the AppContent routing logic
            const hasConsent = true; // Assume consent given if we're debugging
            const demographicsCompleted = !!localStorage.getItem('icuAppDemographics');
            const selectedPhrases = localStorage.getItem('icu_selected_phrases');
            const phrasesSelected = !!selectedPhrases;
            
            log('state-output', '📋 Workflow Conditions:');
            log('state-output', `  hasConsent: ${hasConsent}`);
            log('state-output', `  demographicsCompleted: ${demographicsCompleted}`);
            log('state-output', `  phrasesSelected: ${phrasesSelected}`);
            
            // Determine expected step
            if (!hasConsent) {
                log('state-output', '\n➡️ Expected step: consent');
            } else if (!demographicsCompleted) {
                log('state-output', '\n➡️ Expected step: demographics');
            } else if (!phrasesSelected) {
                log('state-output', '\n➡️ Expected step: phrases');
            } else {
                log('state-output', '\n➡️ Expected step: recording');
                
                // Check if completion should be shown instead
                const recordingCounts = localStorage.getItem('icuAppRecordingsCount');
                if (recordingCounts && selectedPhrases) {
                    try {
                        const counts = JSON.parse(recordingCounts);
                        const phrases = JSON.parse(selectedPhrases);
                        
                        const allComplete = phrases.every(phrase => {
                            const phraseKey = `${phrase.category}:${phrase.phrase}`;
                            return (counts[phraseKey] || 0) >= 3;
                        });
                        
                        if (allComplete) {
                            log('state-output', '⚠️ BUT: All phrases complete - will show completion instead');
                        }
                    } catch (e) {
                        log('state-output', `❌ Error checking completion: ${e.message}`);
                    }
                }
            }
        }

        function clearAllData() {
            clearLog('fix-output');
            log('fix-output', '🗑️ Clearing all application data...\n');
            
            const keysToRemove = [
                'icuAppRecordingsCount',
                'icu_selected_phrases',
                'icuAppDemographics',
                'icuAppSelectedPhrases',
                'icuAppCompletedPhrases',
                'icuAppConsent',
                'testing_mode',
                'mock_recordings'
            ];
            
            keysToRemove.forEach(key => {
                if (localStorage.getItem(key)) {
                    localStorage.removeItem(key);
                    log('fix-output', `✅ Removed: ${key}`);
                }
            });
            
            sessionStorage.clear();
            log('fix-output', '✅ Cleared sessionStorage');
            
            log('fix-output', '\n🎉 All data cleared! Application should start fresh.');
            log('fix-output', 'Refresh the page to test the workflow.');
        }

        function resetWorkflowState() {
            clearLog('fix-output');
            log('fix-output', '🔄 Resetting workflow state...\n');
            
            // Keep demographics but reset recording progress
            const keysToReset = [
                'icuAppRecordingsCount',
                'icu_selected_phrases',
                'icuAppSelectedPhrases',
                'icuAppCompletedPhrases'
            ];
            
            keysToReset.forEach(key => {
                if (localStorage.getItem(key)) {
                    localStorage.removeItem(key);
                    log('fix-output', `✅ Reset: ${key}`);
                }
            });
            
            log('fix-output', '\n✅ Workflow state reset while preserving demographics.');
            log('fix-output', 'Refresh the page to restart from phrase selection.');
        }

        function traceWorkflowPath() {
            clearLog('workflow-output');
            log('workflow-output', '🗺️ Tracing expected workflow path...\n');
            
            // Simulate the exact logic from AppContent.js
            const hasConsent = true; // Assume we're past consent
            const demographicsCompleted = !!localStorage.getItem('icuAppDemographics');
            const currentStep = 'recording'; // What we expect
            
            log('workflow-output', '📍 AppContent.js Routing Logic:');
            log('workflow-output', `  !hasConsent: ${!hasConsent} → ${!hasConsent ? 'SHOW ConsentPage' : 'continue'}`);
            log('workflow-output', `  !demographicsCompleted: ${!demographicsCompleted} → ${!demographicsCompleted ? 'SHOW DemographicForm' : 'continue'}`);
            log('workflow-output', `  currentStep === 'training': ${currentStep === 'training'} → ${currentStep === 'training' ? 'SHOW TrainingVideoPage' : 'continue'}`);
            log('workflow-output', `  currentStep === 'phrases': ${currentStep === 'phrases'} → ${currentStep === 'phrases' ? 'SHOW PhraseSelector' : 'continue'}`);
            log('workflow-output', `  currentStep === 'recording': ${currentStep === 'recording'} → ${currentStep === 'recording' ? 'SHOW RecordingSessionManager' : 'continue'}`);
            
            // Check RecordingSessionManager conditions
            log('workflow-output', '\n📍 RecordingSessionManager Routing Logic:');
            const selectedPhrases = localStorage.getItem('icu_selected_phrases');
            const phrasesSelected = !!selectedPhrases;
            
            log('workflow-output', `  showReceipt: false (assumed)`);
            log('workflow-output', `  showCompletionPrompt: ? (need to check provider state)`);
            log('workflow-output', `  showCollectionTracker: false (assumed)`);
            log('workflow-output', `  !phrasesSelected: ${!phrasesSelected} → ${!phrasesSelected ? 'SHOW PhraseSelector' : 'SHOW Recording Interface'}`);
            
            if (!phrasesSelected) {
                log('workflow-output', '\n🚨 ISSUE FOUND: phrasesSelected is false!');
                log('workflow-output', 'This will cause RecordingSessionManager to show PhraseSelector instead of recording interface.');
            }
        }

        // Initialize on page load
        window.addEventListener('load', () => {
            analyzeCurrentState();
        });
    </script>
</body>
</html>
