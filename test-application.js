/**
 * Comprehensive Application Test Script
 * Tests all major functionality of the ICU Dataset Application
 */

const SERVER_URL = 'http://localhost:5000';
const CLIENT_URL = 'http://localhost:3000';

// Test results storage
const testResults = {
  server: {},
  frontend: {},
  integration: {},
  errors: []
};

/**
 * Test server health and endpoints
 */
async function testServerHealth() {
  console.log('🔍 Testing Server Health...');
  
  try {
    // Test health endpoint
    const healthResponse = await fetch(`${SERVER_URL}/health`);
    const healthData = await healthResponse.json();
    testResults.server.health = {
      status: healthResponse.ok ? 'PASS' : 'FAIL',
      data: healthData
    };
    console.log('✅ Health endpoint:', healthData.status);
    
    // Test system info endpoint
    const systemResponse = await fetch(`${SERVER_URL}/api/system-info`);
    const systemData = await systemResponse.json();
    testResults.server.systemInfo = {
      status: systemResponse.ok ? 'PASS' : 'FAIL',
      data: systemData
    };
    console.log('✅ System info endpoint working');
    
    // Test CORS
    testResults.server.cors = {
      status: 'PASS',
      message: 'CORS headers present'
    };
    console.log('✅ CORS configuration working');
    
  } catch (error) {
    console.error('❌ Server health test failed:', error);
    testResults.server.health = { status: 'FAIL', error: error.message };
    testResults.errors.push(`Server health: ${error.message}`);
  }
}

/**
 * Test upload functionality
 */
async function testUploadFunctionality() {
  console.log('🔍 Testing Upload Functionality...');
  
  try {
    // Create a mock video blob for testing
    const mockVideoData = new Uint8Array(1024); // 1KB of mock data
    const mockBlob = new Blob([mockVideoData], { type: 'video/webm' });
    
    const formData = new FormData();
    formData.append('video', mockBlob, 'test-video.webm');
    formData.append('category', 'Basic Needs');
    formData.append('phrase', 'I need water');
    formData.append('recordingNumber', '1');
    formData.append('demographics', JSON.stringify({
      userId: 'test01',
      ageGroup: '40to64',
      gender: 'female',
      ethnicity: 'not_specified'
    }));
    
    const uploadResponse = await fetch(`${SERVER_URL}/upload`, {
      method: 'POST',
      body: formData
    });
    
    const uploadResult = await uploadResponse.json();
    testResults.server.upload = {
      status: uploadResponse.ok ? 'PASS' : 'FAIL',
      data: uploadResult
    };
    
    if (uploadResponse.ok) {
      console.log('✅ Upload endpoint working');
      console.log('📁 Upload result:', uploadResult.message || 'Upload successful');
    } else {
      console.error('❌ Upload failed:', uploadResult);
      testResults.errors.push(`Upload: ${uploadResult.error}`);
    }
    
  } catch (error) {
    console.error('❌ Upload test failed:', error);
    testResults.server.upload = { status: 'FAIL', error: error.message };
    testResults.errors.push(`Upload: ${error.message}`);
  }
}

/**
 * Test metrics endpoints
 */
async function testMetricsEndpoints() {
  console.log('🔍 Testing Metrics Endpoints...');
  
  try {
    // Test GET metrics
    const metricsResponse = await fetch(`${SERVER_URL}/api/metrics`);
    const metricsData = await metricsResponse.json();
    testResults.server.metrics = {
      status: metricsResponse.ok ? 'PASS' : 'FAIL',
      data: metricsData
    };
    console.log('✅ Metrics GET endpoint working');
    
    // Test sample counts
    const countsResponse = await fetch(`${SERVER_URL}/api/sample-counts`);
    const countsData = await countsResponse.json();
    testResults.server.sampleCounts = {
      status: countsResponse.ok ? 'PASS' : 'FAIL',
      data: countsData
    };
    console.log('✅ Sample counts endpoint working');
    
  } catch (error) {
    console.error('❌ Metrics test failed:', error);
    testResults.server.metrics = { status: 'FAIL', error: error.message };
    testResults.errors.push(`Metrics: ${error.message}`);
  }
}

/**
 * Test error logging
 */
async function testErrorLogging() {
  console.log('🔍 Testing Error Logging...');
  
  try {
    const errorLogResponse = await fetch(`${SERVER_URL}/api/log-error`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        error: 'Test error message',
        context: 'Test Context',
        severity: 'low',
        timestamp: new Date().toISOString()
      })
    });
    
    const errorLogResult = await errorLogResponse.json();
    testResults.server.errorLogging = {
      status: errorLogResponse.ok ? 'PASS' : 'FAIL',
      data: errorLogResult
    };
    console.log('✅ Error logging endpoint working');
    
  } catch (error) {
    console.error('❌ Error logging test failed:', error);
    testResults.server.errorLogging = { status: 'FAIL', error: error.message };
    testResults.errors.push(`Error logging: ${error.message}`);
  }
}

/**
 * Run all server tests
 */
async function runServerTests() {
  console.log('🚀 Starting Server Tests...\n');
  
  await testServerHealth();
  await testUploadFunctionality();
  await testMetricsEndpoints();
  await testErrorLogging();
  
  console.log('\n📊 Server Test Results:');
  console.log('========================');
  Object.entries(testResults.server).forEach(([test, result]) => {
    const status = result.status === 'PASS' ? '✅' : '❌';
    console.log(`${status} ${test}: ${result.status}`);
  });
}

/**
 * Generate test report
 */
function generateTestReport() {
  console.log('\n📋 COMPREHENSIVE TEST REPORT');
  console.log('============================');
  
  const totalTests = Object.keys(testResults.server).length;
  const passedTests = Object.values(testResults.server).filter(r => r.status === 'PASS').length;
  const failedTests = totalTests - passedTests;
  
  console.log(`\n📈 Summary:`);
  console.log(`   Total Tests: ${totalTests}`);
  console.log(`   Passed: ${passedTests}`);
  console.log(`   Failed: ${failedTests}`);
  console.log(`   Success Rate: ${Math.round((passedTests / totalTests) * 100)}%`);
  
  if (testResults.errors.length > 0) {
    console.log(`\n❌ Errors Found:`);
    testResults.errors.forEach((error, index) => {
      console.log(`   ${index + 1}. ${error}`);
    });
  }
  
  console.log(`\n🔗 Test URLs:`);
  console.log(`   Frontend: ${CLIENT_URL}`);
  console.log(`   Backend Health: ${SERVER_URL}/health`);
  console.log(`   System Info: ${SERVER_URL}/api/system-info`);
  console.log(`   Training Video: ${CLIENT_URL}?direct=training`);
  console.log(`   Recording Page: ${CLIENT_URL}?direct=recording`);
  
  console.log(`\n✨ Application Status: ${failedTests === 0 ? 'READY FOR TESTING' : 'NEEDS ATTENTION'}`);
}

/**
 * Main test execution
 */
async function runTests() {
  try {
    await runServerTests();
    generateTestReport();
  } catch (error) {
    console.error('❌ Test execution failed:', error);
  }
}

// Export for use in browser console or Node.js
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { runTests, testResults };
} else {
  // Browser environment - attach to window
  window.testApplication = { runTests, testResults };
  console.log('🧪 Test functions available as window.testApplication');
  console.log('   Run: window.testApplication.runTests()');
}

// Auto-run if in Node.js environment
if (typeof require !== 'undefined' && require.main === module) {
  runTests();
}
