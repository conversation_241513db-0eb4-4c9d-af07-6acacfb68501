<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Demographic Form Ethnicity Update - Test Documentation</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 40px;
            color: #333;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            border-bottom: 3px solid #2c5aa0;
            padding-bottom: 20px;
        }
        h1 {
            color: #2c5aa0;
            font-size: 28px;
            margin-bottom: 10px;
        }
        .subtitle {
            color: #666;
            font-size: 16px;
            font-style: italic;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            background-color: #f8f9fa;
            border-left: 4px solid #2c5aa0;
            border-radius: 5px;
        }
        .test-section h2 {
            color: #2c5aa0;
            margin-top: 0;
            font-size: 20px;
        }
        .step {
            margin: 15px 0;
            padding: 10px;
            background: white;
            border-radius: 5px;
            border: 1px solid #ddd;
        }
        .step-number {
            background: #2c5aa0;
            color: white;
            padding: 5px 10px;
            border-radius: 50%;
            font-weight: bold;
            margin-right: 10px;
            display: inline-block;
            min-width: 25px;
            text-align: center;
        }
        .expected-result {
            background-color: #e8f5e8;
            padding: 10px;
            border-left: 4px solid #28a745;
            margin: 10px 0;
            border-radius: 3px;
        }
        .warning {
            background-color: #fff3cd;
            padding: 10px;
            border-left: 4px solid #ffc107;
            margin: 10px 0;
            border-radius: 3px;
        }
        .code {
            background-color: #f1f1f1;
            padding: 10px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
        }
        .ethnicity-options {
            background: white;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .option-item {
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 3px;
            background: #f9f9f9;
        }
        .updated-option {
            background: #e8f5e8;
            border-color: #28a745;
            font-weight: bold;
        }
        .app-link {
            display: inline-block;
            background: #2c5aa0;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
            margin: 10px 0;
        }
        .app-link:hover {
            background: #1e3d6f;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Demographic Form Ethnicity Update</h1>
            <div class="subtitle">Test Documentation for Updated Ethnicity Categories</div>
            <div class="subtitle">Updated: July 2, 2025</div>
        </div>

        <div class="test-section">
            <h2>🎯 Update Summary</h2>
            <p>The demographic form has been updated to refine the ethnicity category options:</p>
            <ul>
                <li><strong>Latest Change:</strong> "Indigenous (overseas i.e. Canadian)" → "Indigenous from overseas (i.e. Canadian Inuit)"</li>
                <li><strong>Previous Change:</strong> "Indigenous / First Nations (non-Australia)" → "Indigenous (overseas i.e. Canadian)"</li>
                <li><strong>Removed:</strong> All Australia-specific geographic references</li>
                <li><strong>Clarified:</strong> Indigenous category now specifically refers to Canadian Inuit populations</li>
                <li><strong>Purpose:</strong> More precise demographic categorization for international lipreading dataset</li>
            </ul>
        </div>

        <div class="test-section">
            <h2>📋 Updated Ethnicity Options</h2>
            <p>The complete list of ethnicity options now includes:</p>
            <div class="ethnicity-options">
                <div class="option-item">Caucasian / White</div>
                <div class="option-item">Asian (e.g. South Asian, East Asian, Southeast Asian)</div>
                <div class="option-item">African Descent / Black</div>
                <div class="option-item">Hispanic / Latin American</div>
                <div class="option-item">Middle Eastern / North African</div>
                <div class="option-item">Pacific Islander</div>
                <div class="option-item">Aboriginal / Torres Strait Islander</div>
                <div class="option-item updated-option">Indigenous from overseas (i.e. Canadian Inuit) ← UPDATED</div>
                <div class="option-item">Mixed / Multiple ethnicities</div>
            </div>
        </div>

        <div class="test-section">
            <h2>🧪 Testing Procedure</h2>
            
            <div class="step">
                <span class="step-number">1</span>
                <strong>Access the Application</strong>
                <p>Open the ICU dataset application in your browser:</p>
                <a href="http://localhost:3000" class="app-link" target="_blank">Open ICU Dataset Application</a>
            </div>

            <div class="step">
                <span class="step-number">2</span>
                <strong>Navigate to Demographic Form</strong>
                <p>Complete the consent process and proceed to the demographic information section.</p>
                <div class="expected-result">
                    <strong>Expected:</strong> You should see a two-step demographic form with "Gender and Age" and "Ethnic Background" steps.
                </div>
            </div>

            <div class="step">
                <span class="step-number">3</span>
                <strong>Complete Step 1 (Gender and Age)</strong>
                <p>Select any gender and age group options, then click "Next" to proceed to the ethnicity section.</p>
            </div>

            <div class="step">
                <span class="step-number">4</span>
                <strong>Verify Updated Ethnicity Options</strong>
                <p>In the "Ethnic Background" step, verify that the ethnicity options display correctly:</p>
                <div class="expected-result">
                    <strong>Expected Results:</strong>
                    <ul>
                        <li>The option should read "Indigenous from overseas (i.e. Canadian Inuit)" exactly</li>
                        <li>No "(non-Australia)" text should appear anywhere</li>
                        <li>All other ethnicity options should remain unchanged</li>
                        <li>Options should be displayed in radio button format with Material-UI styling</li>
                    </ul>
                </div>
            </div>

            <div class="step">
                <span class="step-number">5</span>
                <strong>Test Selection and Saving</strong>
                <p>Select the updated "Indigenous from overseas (i.e. Canadian Inuit)" option and submit the form.</p>
                <div class="expected-result">
                    <strong>Expected:</strong> The selection should be accepted and saved without errors.
                </div>
            </div>

            <div class="step">
                <span class="step-number">6</span>
                <strong>Verify Data Persistence</strong>
                <p>Check that the ethnicity selection is properly saved to localStorage and passed to recording metadata.</p>
                <div class="code">
                    // Open browser console and check localStorage:
                    console.log(localStorage.getItem('demographics'));
                    
                    // Should show: {"gender":"...","ageGroup":"...","ethnicity":"indigenous"}
                </div>
            </div>

            <div class="step">
                <span class="step-number">7</span>
                <strong>Test Form Navigation</strong>
                <p>Navigate back to the demographic form during the same session to verify the selection is preserved.</p>
                <div class="expected-result">
                    <strong>Expected:</strong> The "Indigenous from overseas (i.e. Canadian Inuit)" option should remain selected when returning to the form.
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>🔍 Verification Checklist</h2>
            <p>Confirm the following items are working correctly:</p>
            <ul>
                <li>✅ Ethnicity option displays as "Indigenous from overseas (i.e. Canadian Inuit)"</li>
                <li>✅ No "(non-Australia)" text appears in any ethnicity options</li>
                <li>✅ Selection can be made and submitted successfully</li>
                <li>✅ Data is saved to localStorage with correct value ("indigenous")</li>
                <li>✅ Selection persists during same-session navigation</li>
                <li>✅ Form validation works correctly for ethnicity field</li>
                <li>✅ No console errors when selecting the updated option</li>
                <li>✅ Demographic data is properly passed to recording metadata</li>
            </ul>
        </div>

        <div class="test-section">
            <h2>⚠️ Troubleshooting</h2>
            <div class="warning">
                <strong>If issues are found:</strong>
                <ul>
                    <li>Check browser console for JavaScript errors</li>
                    <li>Verify localStorage data structure matches expected format</li>
                    <li>Clear localStorage and test fresh form submission</li>
                    <li>Test with different browsers to ensure compatibility</li>
                    <li>Verify that the demographic data is properly passed to S3 upload metadata</li>
                </ul>
            </div>
        </div>

        <div class="test-section">
            <h2>📊 Technical Details</h2>
            <p><strong>File Modified:</strong> <code>src/components/DemographicForm.js</code></p>
            <p><strong>Change Location:</strong> Line 248 in the ethnicity options array</p>
            <p><strong>Data Value:</strong> The internal value remains "indigenous" for backward compatibility</p>
            <p><strong>Display Text:</strong> Updated to "Indigenous from overseas (i.e. Canadian Inuit)"</p>

            <div class="code">
// Original:
{ value: 'indigenous', label: 'Indigenous / First Nations (non-Australia)' }

// Previous Update:
{ value: 'indigenous', label: 'Indigenous (overseas i.e. Canadian)' }

// Current (Latest):
{ value: 'indigenous', label: 'Indigenous from overseas (i.e. Canadian Inuit)' }
            </div>
        </div>

        <div style="margin-top: 40px; padding: 20px; background-color: #e8f4f8; border-radius: 8px; border: 2px solid #2c5aa0;">
            <h3 style="color: #2c5aa0; margin-top: 0;">✅ Test Completion</h3>
            <p>Once all tests pass successfully, the demographic form ethnicity update is confirmed to be working correctly. The change clarifies the scope of the Indigenous category for international data collection while maintaining all existing functionality and data compatibility.</p>
            
            <p><strong>Next Steps:</strong></p>
            <ul>
                <li>Monitor for any user feedback regarding the updated terminology</li>
                <li>Verify that existing data with "indigenous" ethnicity value continues to work correctly</li>
                <li>Update any documentation or user guides that reference the old terminology</li>
            </ul>
        </div>
    </div>
</body>
</html>
