// Debug script for testing phrase progression
console.log('=== PHRASE PROGRESSION DEBUG TEST ===');
console.log('');

console.log('🔧 DEBUGGING COMMANDS:');
console.log('');

console.log('1. Check current state:');
console.log('   window.debugCurrentState()');
console.log('');

console.log('2. Force recording count to 3 for testing:');
console.log('   window.debugForceRecordingCount()');
console.log('');

console.log('3. Test auto-advancement manually:');
console.log('   window.debugAutoAdvancement()');
console.log('');

console.log('4. Clear localStorage for fresh test:');
console.log('   localStorage.clear()');
console.log('');

console.log('🧪 TESTING PROCEDURE:');
console.log('');

console.log('STEP 1: Open Browser Console');
console.log('• Press F12 to open DevTools');
console.log('• Go to Console tab');
console.log('• Look for configuration messages:');
console.log('  - "📋 App.js Configuration:"');
console.log('  - "RECORDINGS_PER_PHRASE: 3"');
console.log('');

console.log('STEP 2: Test Single Phrase');
console.log('• Navigate through: Consent → Demographics → Training Video');
console.log('• Select ONLY 1 phrase');
console.log('• Record 3 videos and watch console for:');
console.log('  - "🔢 RECORDING COUNT UPDATE:"');
console.log('  - "actualNewRecordingCount captured: 1, 2, 3"');
console.log('  - "Will trigger auto-advance? true" (on 3rd recording)');
console.log('  - "🎯 PHRASE COMPLETION DETECTED"');
console.log('  - "🚀 TIMEOUT EXECUTED - CALLING handleNextPhrase NOW"');
console.log('  - "🏁 COMPLETION CASE: This is the last/only phrase"');
console.log('');

console.log('STEP 3: Debug if Stuck');
console.log('• If phrase progression is stuck, run:');
console.log('  window.debugCurrentState()');
console.log('• Check if recording count is actually 3:');
console.log('  JSON.parse(localStorage.getItem("icuAppRecordingsCount"))');
console.log('• Force advancement manually:');
console.log('  window.debugAutoAdvancement()');
console.log('');

console.log('🔍 KEY CONSOLE MESSAGES TO WATCH:');
console.log('');

console.log('Recording 1:');
console.log('  → "actualNewRecordingCount captured: 1"');
console.log('  → "Will trigger auto-advance? false"');
console.log('  → "⏳ STAYING ON CURRENT PHRASE - Not yet 3 recordings"');
console.log('');

console.log('Recording 2:');
console.log('  → "actualNewRecordingCount captured: 2"');
console.log('  → "Will trigger auto-advance? false"');
console.log('  → "⏳ STAYING ON CURRENT PHRASE - Not yet 3 recordings"');
console.log('');

console.log('Recording 3 (CRITICAL):');
console.log('  → "actualNewRecordingCount captured: 3"');
console.log('  → "Will trigger auto-advance? true"');
console.log('  → "🎯 PHRASE COMPLETION DETECTED - 3 recordings completed"');
console.log('  → "⏰ SETTING TIMEOUT for handleNextPhrase in 100ms..."');
console.log('  → "🚀 TIMEOUT EXECUTED - CALLING handleNextPhrase NOW"');
console.log('  → "🏁 COMPLETION CASE: This is the last/only phrase"');
console.log('  → "✅ ALL PHRASES COMPLETED! Showing completion page"');
console.log('');

console.log('🚨 FAILURE INDICATORS:');
console.log('');

console.log('If you see these, the bug is still present:');
console.log('❌ "actualNewRecordingCount captured: 1" repeatedly (not incrementing)');
console.log('❌ "Will trigger auto-advance? false" on 3rd recording');
console.log('❌ No "🚀 TIMEOUT EXECUTED" message after 3rd recording');
console.log('❌ No "🏁 COMPLETION CASE" message');
console.log('❌ Phrase text doesn\'t change after 3 recordings');
console.log('❌ Same phrase keeps recording indefinitely');
console.log('');

console.log('🔧 EMERGENCY DEBUGGING:');
console.log('');

console.log('If completely stuck, try these commands:');
console.log('');

console.log('// Check if RECORDINGS_PER_PHRASE is actually 3');
console.log('console.log("RECORDINGS_PER_PHRASE:", window.RECORDINGS_PER_PHRASE || "not exposed");');
console.log('');

console.log('// Check current recording counts');
console.log('window.debugCurrentState()');
console.log('');

console.log('// Force recording count and test advancement');
console.log('window.debugForceRecordingCount()');
console.log('window.debugAutoAdvancement()');
console.log('');

console.log('// Clear everything and start fresh');
console.log('localStorage.clear()');
console.log('location.reload()');
console.log('');

console.log('=== READY FOR TESTING ===');
console.log('Open http://localhost:3001 and follow the testing procedure above.');
