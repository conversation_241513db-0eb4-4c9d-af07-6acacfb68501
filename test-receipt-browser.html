<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ICU Dataset - Receipt Generation Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #009688;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 5px;
            background-color: #fafafa;
        }
        .test-button {
            background-color: #009688;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .test-button:hover {
            background-color: #00796b;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 3px;
        }
        .success {
            background-color: #e8f5e8;
            color: #2e7d32;
            border: 1px solid #4caf50;
        }
        .error {
            background-color: #ffebee;
            color: #c62828;
            border: 1px solid #f44336;
        }
        .info {
            background-color: #e3f2fd;
            color: #1565c0;
            border: 1px solid #2196f3;
        }
        .receipt-display {
            background: white;
            border: 2px solid #009688;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }
        .receipt-number {
            font-size: 2em;
            font-weight: bold;
            font-family: monospace;
            color: #263238;
            margin: 10px 0;
        }
        .status-display {
            font-family: monospace;
            background: #f0f0f0;
            padding: 10px;
            border-radius: 3px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧾 ICU Dataset Receipt Generation Test</h1>
        
        <div class="test-section">
            <h3>📋 Test 1: Receipt Number Generation</h3>
            <p>Test the simplified 6-digit sequential receipt number system.</p>
            <button class="test-button" onclick="testReceiptGeneration()">Generate Receipt Number</button>
            <button class="test-button" onclick="clearReceiptCounter()">Reset Counter</button>
            <div id="receipt-test-results"></div>
        </div>

        <div class="test-section">
            <h3>🔢 Test 2: Session Reference Logic</h3>
            <p>Test the session reference generation that fixes the "Generating..." issue.</p>
            <button class="test-button" onclick="testSessionReference()">Test Session Reference</button>
            <div id="session-test-results"></div>
        </div>

        <div class="test-section">
            <h3>💾 Test 3: localStorage Persistence</h3>
            <p>Test that receipt counter persists across browser sessions.</p>
            <button class="test-button" onclick="testLocalStoragePersistence()">Test Persistence</button>
            <button class="test-button" onclick="showStorageData()">Show Storage Data</button>
            <div id="storage-test-results"></div>
        </div>

        <div class="test-section">
            <h3>🎬 Test 4: Complete Flow Simulation</h3>
            <p>Simulate the complete recording session and receipt generation flow.</p>
            <button class="test-button" onclick="simulateCompleteFlow()">Simulate Complete Flow</button>
            <div id="flow-test-results"></div>
        </div>

        <div class="receipt-display" id="receipt-preview" style="display: none;">
            <h3>🧾 Receipt Preview</h3>
            <div>Thank you for your contribution!</div>
            <div style="margin: 20px 0;">
                <strong>Receipt Number</strong><br>
                <div class="receipt-number" id="receipt-number-display">000001</div>
            </div>
            <button class="test-button" onclick="copyReceiptNumber()">📋 Copy Receipt Number</button>
        </div>
    </div>

    <script>
        // Receipt number generation function (matches the React component)
        function generateReceiptNumber() {
            try {
                const currentCounter = parseInt(localStorage.getItem('icuAppReceiptCounter') || '0', 10);
                const nextCounter = currentCounter + 1;
                localStorage.setItem('icuAppReceiptCounter', nextCounter.toString());
                return nextCounter.toString().padStart(6, '0');
            } catch (error) {
                console.warn('Error generating receipt number:', error);
                return Date.now().toString().slice(-6);
            }
        }

        // Test 1: Receipt Number Generation
        function testReceiptGeneration() {
            const resultsDiv = document.getElementById('receipt-test-results');
            resultsDiv.innerHTML = '';

            try {
                const receipt1 = generateReceiptNumber();
                const receipt2 = generateReceiptNumber();
                const receipt3 = generateReceiptNumber();

                resultsDiv.innerHTML = `
                    <div class="result success">✅ Receipt generation successful!</div>
                    <div class="status-display">
                        First receipt: ${receipt1}<br>
                        Second receipt: ${receipt2}<br>
                        Third receipt: ${receipt3}
                    </div>
                    <div class="result info">Sequential numbering: ${isSequential(receipt1, receipt2, receipt3) ? 'WORKING' : 'FAILED'}</div>
                `;

                // Show receipt preview
                document.getElementById('receipt-preview').style.display = 'block';
                document.getElementById('receipt-number-display').textContent = receipt3;

            } catch (error) {
                resultsDiv.innerHTML = `<div class="result error">❌ Error: ${error.message}</div>`;
            }
        }

        function isSequential(r1, r2, r3) {
            const n1 = parseInt(r1, 10);
            const n2 = parseInt(r2, 10);
            const n3 = parseInt(r3, 10);
            return (n2 === n1 + 1) && (n3 === n2 + 1);
        }

        function clearReceiptCounter() {
            localStorage.removeItem('icuAppReceiptCounter');
            document.getElementById('receipt-test-results').innerHTML = 
                '<div class="result info">🔄 Receipt counter reset. Next receipt will be 000001.</div>';
        }

        // Test 2: Session Reference Logic
        function testSessionReference() {
            const resultsDiv = document.getElementById('session-test-results');
            
            // Simulate the useEffect logic
            let showCompletionPrompt = false;
            let currentSessionReference = '';
            let generateSessionReferenceCalled = false;

            const mockGenerateSessionReference = () => {
                generateSessionReferenceCalled = true;
                currentSessionReference = 'ICU-' + Date.now().toString(36).toUpperCase();
                return currentSessionReference;
            };

            // Simulate useEffect trigger
            const simulateUseEffect = (completionPrompt, sessionRef) => {
                if (completionPrompt && !sessionRef) {
                    return mockGenerateSessionReference();
                }
                return sessionRef;
            };

            // Test the logic
            showCompletionPrompt = true;
            const result = simulateUseEffect(showCompletionPrompt, currentSessionReference);

            resultsDiv.innerHTML = `
                <div class="result success">✅ Session reference logic test completed!</div>
                <div class="status-display">
                    Completion prompt shown: ${showCompletionPrompt}<br>
                    Generate function called: ${generateSessionReferenceCalled}<br>
                    Session reference: ${result}<br>
                    No "Generating..." status: ${result && !result.includes('Generating') ? 'YES' : 'NO'}
                </div>
                <div class="result info">Logic working: ${generateSessionReferenceCalled && result ? 'CORRECTLY' : 'INCORRECTLY'}</div>
            `;
        }

        // Test 3: localStorage Persistence
        function testLocalStoragePersistence() {
            const resultsDiv = document.getElementById('storage-test-results');
            
            // Test data persistence
            const testData = {
                'Greetings:Hello': 3,
                'Greetings:Good morning': 3,
                'Pain Assessment:I am in pain': 3
            };

            localStorage.setItem('icuAppRecordingsCount', JSON.stringify(testData));
            
            // Generate a receipt to test counter persistence
            const receiptNumber = generateReceiptNumber();
            
            // Verify data persistence
            const savedRecordings = localStorage.getItem('icuAppRecordingsCount');
            const savedCounter = localStorage.getItem('icuAppReceiptCounter');

            resultsDiv.innerHTML = `
                <div class="result success">✅ localStorage persistence test completed!</div>
                <div class="status-display">
                    Recordings data saved: ${savedRecordings ? 'YES' : 'NO'}<br>
                    Receipt counter saved: ${savedCounter ? 'YES' : 'NO'}<br>
                    Current receipt number: ${receiptNumber}<br>
                    Counter value: ${savedCounter}
                </div>
                <div class="result info">Persistence working: ${savedRecordings && savedCounter ? 'CORRECTLY' : 'INCORRECTLY'}</div>
            `;
        }

        function showStorageData() {
            const resultsDiv = document.getElementById('storage-test-results');
            const recordings = localStorage.getItem('icuAppRecordingsCount');
            const counter = localStorage.getItem('icuAppReceiptCounter');
            
            resultsDiv.innerHTML += `
                <div class="result info">
                    <strong>Current localStorage Data:</strong><br>
                    <div class="status-display">
                        icuAppRecordingsCount: ${recordings || 'Not set'}<br>
                        icuAppReceiptCounter: ${counter || 'Not set'}
                    </div>
                </div>
            `;
        }

        // Test 4: Complete Flow Simulation
        function simulateCompleteFlow() {
            const resultsDiv = document.getElementById('flow-test-results');
            resultsDiv.innerHTML = '<div class="result info">🎬 Simulating complete recording session...</div>';

            setTimeout(() => {
                // Step 1: Simulate recordings completion
                const completedRecordings = {
                    'Greetings:Hello': 3,
                    'Greetings:Good morning': 3,
                    'Pain Assessment:I am in pain': 3
                };
                localStorage.setItem('icuAppRecordingsCount', JSON.stringify(completedRecordings));

                // Step 2: Trigger completion prompt
                const showCompletionPrompt = true;

                // Step 3: Generate session reference (no delay)
                const sessionReference = 'ICU-' + Date.now().toString(36).toUpperCase();

                // Step 4: Generate receipt
                const receiptNumber = generateReceiptNumber();

                // Step 5: Display results
                resultsDiv.innerHTML = `
                    <div class="result success">✅ Complete flow simulation successful!</div>
                    <div class="status-display">
                        Step 1 - Recordings completed: ✅<br>
                        Step 2 - Completion prompt shown: ✅<br>
                        Step 3 - Session reference generated: ${sessionReference}<br>
                        Step 4 - Receipt generated: ${receiptNumber}<br>
                        Step 5 - No "Generating..." status: ✅
                    </div>
                    <div class="result success">🎉 Receipt generation system working correctly!</div>
                `;

                // Show receipt preview
                document.getElementById('receipt-preview').style.display = 'block';
                document.getElementById('receipt-number-display').textContent = receiptNumber;

            }, 1000);
        }

        // Copy receipt number to clipboard
        function copyReceiptNumber() {
            const receiptNumber = document.getElementById('receipt-number-display').textContent;
            navigator.clipboard.writeText(receiptNumber).then(() => {
                alert('Receipt number copied to clipboard: ' + receiptNumber);
            });
        }

        // Initialize page
        window.onload = function() {
            console.log('🧪 ICU Dataset Receipt Generation Test Page Loaded');
            console.log('📋 Ready to test receipt generation fixes');
        };
    </script>
</body>
</html>
